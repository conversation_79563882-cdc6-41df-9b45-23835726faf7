
import vectorbt as vbt
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import quantstats as qs
import os

# --- Module 1: Data Handler ---
class DataHandler:
    """
    Responsible for fetching, loading, and preparing market data.
    """
    def __init__(self, ticker: str, period: str = "5y", interval: str = "1d"):
        self.ticker = ticker
        self.period = period
        self.interval = interval
        self.data = None

    def fetch_data(self) -> pd.DataFrame:
        """Fetches data from Yahoo Finance and performs initial cleaning."""
        print(f"Fetching data for {self.ticker}...")
        price_data = vbt.YFData.download(
            self.ticker, 
            period=self.period, 
            interval=self.interval
        ).get(["Close", "High", "Low", "Volume"])
        
        self.data = price_data.dropna().copy()
        print("Data fetching complete.")
        return self.data

# --- Module 2: Signal Generator ---
class SignalGenerator:
    """
    Encapsulates all signal generation logic for the strategies.
    """
    def __init__(self, data: pd.DataFrame):
        if data is None or "Close" not in data.columns:
            raise ValueError("Data must be a pandas DataFrame with a 'Close' column.")
        self.data = data.copy()
        self.volume = self.data.get('Volume', pd.Series(np.nan, index=self.data.index))

    def _calculate_williams_r(self, period=14):
        high = self.data['High']
        low = self.data['Low']
        close = self.data['Close']
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        return (highest_high - close) / (highest_high - lowest_low) * -100

    def _calculate_vroc(self, period=14):
        return (self.volume - self.volume.shift(period)) / self.volume.shift(period) * 100

    def generate_signals(self) -> dict:
        """Calculates all indicators and generates strategy signals."""
        print("Generating trading signals...")
        self.data['MA50'] = self.data['Close'].rolling(window=50).mean()
        self.data['RSI'] = vbt.RSI.run(self.data['Close'], window=14).rsi
        self.data['Williams %R'] = self._calculate_williams_r()
        self.data['VROC'] = self._calculate_vroc()

        strategies = {
            "MA_Strategy": {
                "entries": self.data['Close'] > self.data['MA50'],
                "exits": self.data['Close'] < self.data['MA50']
            },
            "RSI_Strategy": {
                "entries": self.data['RSI'] < 30,
                "exits": self.data['RSI'] > 70
            },
            "VROC_Strategy": {
                "entries": self.data['VROC'] > 0,
                "exits": self.data['VROC'] < 0
            },
            "Williams_R_Strategy": {
                "entries": self.data['Williams %R'] < -80,
                "exits": self.data['Williams %R'] > -20
            }
        }
        print("Signal generation complete.")
        return strategies

# --- Module 3: Backtester ---
class Backtester:
    """
    Core module for executing vectorbt backtests.
    """
    def __init__(self, close_prices: pd.Series, signals: dict, freq: str = 'D', fees=0.002, slippage=0.001):
        self.close_prices = close_prices
        self.signals = signals
        self.freq = freq
        self.fees = fees
        self.slippage = slippage
        self.results = {}

    def run(self):
        """Runs the backtest for all strategies."""
        print("Running backtests...")
        for name, params in self.signals.items():
            portfolio = vbt.Portfolio.from_signals(
                close=self.close_prices,
                entries=params["entries"],
                exits=params["exits"],
                size=np.inf,
                fees=self.fees,
                slippage=self.slippage,
                freq=self.freq
            )
            self.results[name] = portfolio
        print("Backtesting complete.")
        return self.results

# --- Module 4: Results Analyzer ---
class Analyzer:
    """
    Responsible for displaying, visualizing, and reporting backtest results.
    """
    def __init__(self, backtest_results: dict, report_dir: str = "reports"):
        self.results = backtest_results
        self.report_dir = report_dir
        if not os.path.exists(self.report_dir):
            os.makedirs(self.report_dir)

    def print_stats(self):
        """Prints key performance indicators for each strategy."""
        for name, portfolio in self.results.items():
            print(f"\n--- Performance Analysis for: {name} ---")
            print(portfolio.stats())
            print("-" * 50)

    def generate_html_reports(self):
        """Generates comprehensive HTML reports for each strategy."""
        print(f"\nGenerating HTML reports in '{self.report_dir}' directory...")
        for name, portfolio in self.results.items():
            # --- VBT Report with Stats Table ---
            vbt_report_path = os.path.join(self.report_dir, f"{name}_vbt_dashboard.html")
            try:
                # 1. Get the stats table as a pandas DataFrame
                stats_df = portfolio.stats().to_frame(name="Value")
                # Convert to a styled HTML table with smaller font
                stats_html = stats_df.style.set_table_attributes(
                    'class="table table-striped table-hover table-sm"'
                ).to_html()

                # 2. Create the VBT plot with no title
                vbt_fig = portfolio.plot(title="") # Remove title from plot object
                plot_html = vbt_fig.to_html(full_html=False, include_plotlyjs='cdn')

                # 3. Create the combined HTML with a two-column layout
                html_content = f"""
                <html>
                <head>
                    <title>{name} - Backtest Dashboard</title>
                    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
                    <style>
                        body {{ font-family: Arial, sans-serif; }}
                        .dashboard-title {{ text-align: center; margin-bottom: 20px; font-size: 1.5rem; font-weight: bold; }}
                        .container-fluid {{ display: flex; flex-wrap: nowrap; padding: 10px; }}
                        .plot-container {{ flex: 3; padding-right: 10px; }}
                        .stats-container {{ flex: 1; overflow-y: auto; font-size: 0.7rem; }}
                        .stats-container h4 {{ font-size: 1rem; font-weight: bold; margin-bottom: 10px; }}
                        .table-sm td, .table-sm th {{ padding: .2rem; }}
                    </style>
                </head>
                <body>
                    <h2 class="dashboard-title">{name} - Backtest Analysis</h2>
                    <div class="container-fluid">
                        <div class="plot-container">
                            {plot_html}
                        </div>
                        <div class="stats-container">
                            <h4>Performance Metrics</h4>
                            {stats_html}
                        </div>
                    </div>
                </body>
                </html>
                """
                with open(vbt_report_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                print(f"  - Successfully generated VBT Dashboard for {name}: {vbt_report_path}")
            except Exception as e:
                print(f"  - Could not generate VBT Dashboard for {name}: {e}")

            # --- QuantStats Report ---
            qs_report_path = os.path.join(self.report_dir, f"{name}_qs_report.html")
            try:
                qs.extend_pandas()
                returns = portfolio.returns()
                qs.reports.html(returns, output=qs_report_path, title=f"{name} - QuantStats Analysis")
                print(f"  - Successfully generated QuantStats report for {name}: {qs_report_path}")
            except Exception as e:
                print(f"  - Could not generate QuantStats report for {name}: {e}")

# --- Main Execution Flow ---
def main():
    """
    Connects all modules to run the full backtesting pipeline.
    """
    # 1. Data Handling
    data_handler = DataHandler(ticker="SPY", period="5y")
    market_data = data_handler.fetch_data()

    # 2. Signal Generation
    signal_generator = SignalGenerator(market_data)
    strategy_signals = signal_generator.generate_signals()

    # 3. Backtesting
    backtester = Backtester(market_data['Close'], strategy_signals)
    results = backtester.run()

    # 4. Analysis & Reporting
    analyzer = Analyzer(results)
    analyzer.print_stats()
    analyzer.generate_html_reports()

if __name__ == "__main__":
    main()
