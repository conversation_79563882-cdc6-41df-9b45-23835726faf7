#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
from datetime import datetime, timedelta
import pandas as pd
from futu import *
from futu import Market as FutuMarket, KLType as FutuKLType, AuType
from enum import Enum
import logging
from typing import List, Tuple, Optional
import re
import argparse

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Market(Enum):
    """市场枚举类"""
    HK = "HK"  # 港股市场
    SH = "SH"  # 上交所
    SZ = "SZ"  # 深交所
    BJ = "BJ"  # 北交所

class KLType(Enum):
    """K线类型枚举"""
    DAILY = ("daily", FutuKLType.K_DAY)
    WEEKLY = ("weekly", FutuKLType.K_WEEK)
    MONTHLY = ("monthly", FutuKLType.K_MON)
    YEARLY = ("yearly", FutuKLType.K_YEAR)
    MIN_5 = ("5min", FutuKLType.K_5M)
    MIN_15 = ("15min", FutuKLType.K_15M)
    MIN_30 = ("30min", FutuKLType.K_30M)
    HOUR_1 = ("1h", FutuKLType.K_60M)  # 1小时数据
    HOUR_2 = ("2h", FutuKLType.K_60M)  # 通过数据处理实现
    HOUR_4 = ("4h", FutuKLType.K_60M)  # 通过数据处理实现

    def __init__(self, file_suffix, futu_kltype):
        self.file_suffix = file_suffix
        self.futu_kltype = futu_kltype

class StockInfo:
    """股票信息类"""
    def __init__(self, code: str, name: str):
        self.code = code  # 带市场前缀的代码，如 SH.600000
        self.name = name
        # 提取纯代码（不带市场前缀）
        self.pure_code = re.search(r'\.(\d+)$', code).group(1) if re.search(r'\.(\d+)$', code) else code

    @property
    def directory_code(self) -> str:
        """获取用于目录名的代码（确保代码格式正确）"""
        if self.code.startswith('HK.'):
            return self.pure_code  # 港股保持5位
        return self.pure_code.zfill(6)  # 其他市场填充为6位

class FutuDataDownloader:
    def __init__(self, host='127.0.0.1', port=11111):
        """
        初始化FutuDataDownloader
        :param host: 富途API主机地址
        :param port: 富途API端口
        """
        self.host = host
        self.port = port
        self.quote_ctx = None
        self.base_path = "Data"
        self.last_request_time = time.time()  # 最后请求时间
        self.last_reset_time = time.time()  # 最后重置时间
        self.request_count = 0      # 记录30秒内的请求次数
        self._connected = False  # 添加连接状态标志

    def connect(self):
        """建立与富途API的连接"""
        try:
            if self.quote_ctx is None:
                self.quote_ctx = OpenQuoteContext(host=self.host, port=self.port)
                self._connected = True
                logger.info("成功连接到富途API")
            return True
        except Exception as e:
            logger.error(f"连接富途API失败: {str(e)}")
            self._connected = False
            return False

    def disconnect(self):
        """断开与富途API的连接"""
        if self.quote_ctx:
            self.quote_ctx.close()
            self.quote_ctx = None
            self._connected = False
            logger.info("已断开与富途API的连接")

    def _create_directory_structure(self, market: Market, directory_code: str):
        """
        创建数据存储目录结构
        :param market: 市场类型
        :param directory_code: 用于目录的股票代码
        :return: 股票数据目录路径
        """
        market_dir = os.path.join(self.base_path, market.value)
        stock_dir = os.path.join(market_dir, directory_code)
        os.makedirs(stock_dir, exist_ok=True)
        return stock_dir

    def _get_last_record_date(self, file_path: str) -> Tuple[Optional[str], Optional[str]]:
        """
        获取数据文件中的最后一条记录时间
        :param file_path: 数据文件路径
        :return: (最后一条记录的时间, 用于请求的开始时间)，如果文件不存在则返回(None, None)
        """
        try:
            if os.path.exists(file_path):
                df = pd.read_csv(file_path)
                if not df.empty:
                    # 获取最后一条记录的时间
                    last_time = df['time_key'].max()
                    # 获取最后一条记录的前1天作为请求的开始时间，以确保获取完整的交易时段数据
                    request_start = (pd.to_datetime(last_time) - timedelta(days=1)).strftime('%Y-%m-%d')
                    return last_time, request_start
            return None, None
        except Exception as e:
            logger.error(f"读取文件{file_path}最后记录时间时出错: {str(e)}")
            return None, None

    def _process_multi_hour_kline(self, df: pd.DataFrame, hours: int) -> pd.DataFrame:
        # 移除重采样逻辑
        return df  # 直接返回原始数据

    def _merge_and_sort_data(self, existing_df: Optional[pd.DataFrame], new_df: pd.DataFrame) -> pd.DataFrame:
        """
        合并并排序数据，确保时间序列的正确性
        :param existing_df: 现有数据
        :param new_df: 新数据
        :return: 合并后的数据
        """
        if existing_df is None or existing_df.empty:
            return new_df

        # 确保时间列是datetime类型
        existing_df['time_key'] = pd.to_datetime(existing_df['time_key'])
        new_df['time_key'] = pd.to_datetime(new_df['time_key'])

        # 确保字段顺序与富途API默认输出一致
        columns = [
            'code', 'name', 'time_key', 'open', 'close', 
            'high', 'low', 'pe_ratio', 'turnover_rate', 
            'volume', 'turnover', 'change_rate', 'last_close'
        ]
        existing_df = existing_df.reindex(columns=columns)
        new_df = new_df.reindex(columns=columns)

        # 合并数据
        merged_df = pd.concat([existing_df, new_df])
        
        # 按时间排序并删除重复数据
        merged_df = merged_df.sort_values('time_key')
        merged_df = merged_df.drop_duplicates(subset=['time_key'], keep='last')
        
        return merged_df

    def get_market_and_directory_code(self, stock_info: StockInfo) -> Tuple[Market, str]:
        """根据股票信息获取市场类型和目录代码"""
        if stock_info.code.startswith('HK.'):
            return Market.HK, stock_info.pure_code  # 港股保持5位
        elif stock_info.code.startswith('SH.'):
            return Market.SH, stock_info.pure_code.zfill(6)  # 上交所填充为6位
        elif stock_info.code.startswith('SZ.'):
            return Market.SZ, stock_info.pure_code.zfill(6)  # 深交所填充为6位
        else:
            raise ValueError(f"不支持的股票代码: {stock_info.code}")

    def get_next_request_time(self, last_time: datetime, kl_type: KLType) -> str:
        """根据不同K线类型确定下次请求的起始时间"""
        if kl_type == KLType.DAILY:
            # 日线：直接取最后记录的第二天
            next_time = last_time + timedelta(days=1)
        elif kl_type in [KLType.HOUR_1, KLType.HOUR_2, KLType.HOUR_4]:
            # 小时线：取最后记录所在的日期
            next_time = last_time.replace(hour=0, minute=0, second=0)
        else:
            # 分钟线：也取最后记录所在的日期
            next_time = last_time.replace(hour=0, minute=0, second=0)
        return next_time.strftime('%Y-%m-%d')

    def merge_new_data(self, existing_df: pd.DataFrame, new_df: pd.DataFrame, kl_type: KLType) -> pd.DataFrame:
        """合并新旧数据，对所有K线类型采用统一的逻辑"""
        if existing_df is None or existing_df.empty:
            return new_df

        # 转换时间列为datetime
        existing_df['time_key'] = pd.to_datetime(existing_df['time_key'])
        new_df['time_key'] = pd.to_datetime(new_df['time_key'])
        
        # 合并所有数据
        combined_df = pd.concat([existing_df, new_df])
        
        # 按时间排序并删除重复数据，保留最新的记录
        combined_df = combined_df.sort_values('time_key')
        combined_df = combined_df.drop_duplicates(subset=['time_key'], keep='last')
        
        return combined_df

    def _check_need_update(self, stock_info: StockInfo, kl_type: KLType, 
                          start_date: str = None, end_date: str = None) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        检查是否需要更新数据
        :param stock_info: 股票信息
        :param kl_type: K线类型
        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: (是否需要更新, 开始日期, 结束日期)
        """
        try:
            market, directory_code = self.get_market_and_directory_code(stock_info)
            file_path = os.path.join(self.base_path, market.value, directory_code, f"{kl_type.file_suffix}.csv")
            
            if not end_date:
                end_date = datetime.now().strftime("%Y-%m-%d")
                
            # 如果文件不存在，需要从头开始下载
            if not os.path.exists(file_path):
                return True, start_date or "2019-01-01", end_date
                
            # 读取最后记录时间
            df = pd.read_csv(file_path)
            if df.empty:
                return True, start_date or "2019-01-01", end_date
                
            # 获取最后一条记录的时间
            df['time_key'] = pd.to_datetime(df['time_key'])
            last_time = df['time_key'].max()
            
            # 转换时间为datetime对象进行比较
            end_time = pd.to_datetime(end_date)
            
            # 如果用户指定了开始日期，使用用户指定的
            if start_date:
                return True, start_date, end_date
                
            # 获取最后记录日期的前一天作为请求的开始时间
            # 这样可以确保获取完整的交易时段数据
            request_start = (last_time.date() - timedelta(days=1)).strftime("%Y-%m-%d")
            
            # 如果最后记录日期大于结束日期，不需要更新
            if last_time.date() > end_time.date():
                return False, None, None
                
            return True, request_start, end_date
                
        except Exception as e:
            logger.error(f"检查更新状态时发生错误: {str(e)}")
            return True, start_date or "2019-01-01", end_date

    def _wait_for_rate_limit(self, is_first_page=True):
        """
        控制请求频率，确保符合API限制规则
        每30秒最多60次请求，平均每次请求至少等待0.5秒
        
        :param is_first_page: 是否是首页请求，首页请求受到严格的频率限制，后续页请求不受此限制
        """
        current_time = time.time()
        
        # 后续页请求不受"每30秒最多60次"的限制，但仍需保持最小间隔
        if not is_first_page:
            elapsed = current_time - self.last_request_time
            if elapsed < 0.2:  # 对后续页使用更短的最小间隔
                time.sleep(0.2 - elapsed)
            self.last_request_time = time.time()
            return
        
        # 以下是首页请求的频率限制处理
        
        # 检查是否需要重置计数器（每30秒一个周期）
        if current_time - self.last_reset_time >= 30:
            self.request_count = 0
            self.last_reset_time = current_time
        
        # 如果30秒内已经请求了60次，等待到下一个30秒周期
        if self.request_count >= 60:
            wait_time = 30 - (current_time - self.last_reset_time)
            if wait_time > 0:
                logger.info(f"达到频率限制，等待 {wait_time:.2f} 秒")
                time.sleep(wait_time)
                self.request_count = 0  # 重置计数器
                self.last_reset_time = time.time()  # 更新重置时间
        
        # 确保每次请求间隔至少0.5秒
        elapsed = current_time - self.last_request_time
        if elapsed < 0.5:
            time.sleep(0.5 - elapsed)
        
        self.request_count += 1  # 增加请求计数
        self.last_request_time = time.time()  # 更新最后请求时间

    def request_new_data(self, stock_info: StockInfo, kl_type: KLType, start_date: str, end_date: str) -> pd.DataFrame:
        """请求新的K线数据"""
        all_data = []  # 用于存储所有数据
        page_req_key = None  # 分页请求的键
        is_first_page = True  # 标记是否是首页请求
        
        while True:
            # 等待以符合频率限制，区分首页和后续页
            self._wait_for_rate_limit(is_first_page)
            
            ret, data, page_req_key = self.quote_ctx.request_history_kline(
                code=stock_info.code,
                start=start_date,
                end=end_date,
                ktype=kl_type.futu_kltype,
                autype=AuType.QFQ,  # 使用前复权
                page_req_key=page_req_key  # 分页请求的键
            )
            
            if ret != RET_OK:
                logger.error(f"获取历史K线数据失败: {data}")
                return pd.DataFrame()  # 返回空的DataFrame
                
            all_data.append(data)  # 添加当前页的数据
            
            # 后续请求不再是首页
            is_first_page = False
            
            if page_req_key is None or len(data) < 1000:
                break  # 如果没有下一页或当前页数据少于1000条，说明没有更多数据了
                
        return pd.concat(all_data, ignore_index=True) if all_data else pd.DataFrame()  # 合并所有数据并返回

    def download_history_data(self, stock_info: StockInfo, kl_type: KLType, 
                          start_date: str = None, end_date: str = None) -> bool:
        """下载历史K线数据"""
        try:
            # 确保连接
            if not self._connected and not self.connect():
                return False
            
            # 创建目录结构
            market, directory_code = self.get_market_and_directory_code(stock_info)
            stock_dir = self._create_directory_structure(market, directory_code)
            file_path = os.path.join(stock_dir, f"{kl_type.file_suffix}.csv")
            
            # 下载新数据
            logger.info(f"请求 {stock_info.code} 的 {kl_type.file_suffix} 数据: {start_date} 到 {end_date}")
            new_df = self.request_new_data(stock_info, kl_type, start_date, end_date)
            if new_df.empty:
                logger.warning(f"未获取到 {stock_info.code} 的新数据")
                return False
            
            # 读取现有数据
            existing_df = None
            if os.path.exists(file_path):
                existing_df = pd.read_csv(file_path)
                
                # 使用merge_new_data方法合并数据
                original_count = len(existing_df)
                final_df = self.merge_new_data(existing_df, new_df, kl_type)
                
                # 检查是否有新数据或更新
                if len(final_df) > original_count:
                    logger.info(f"新增 {len(final_df) - original_count} 条记录")
                else:
                    # 检查是否有数据被更新
                    existing_df['time_key'] = pd.to_datetime(existing_df['time_key'])
                    new_df['time_key'] = pd.to_datetime(new_df['time_key'])
                    
                    # 找出重叠的时间点
                    overlap_times = set(existing_df['time_key']).intersection(set(new_df['time_key']))
                    if overlap_times:
                        logger.info(f"更新了 {len(overlap_times)} 条现有记录")
                    else:
                        logger.info(f"{stock_info.code} 没有新的数据需要更新")
                        return True
            else:
                final_df = new_df
                logger.info(f"新文件，写入 {len(new_df)} 条记录")
            
            # 保存更新后的数据
            final_df.to_csv(file_path, index=False)
            logger.info(f"成功更新 {stock_info.code} 的 {kl_type.file_suffix} 数据，总计 {len(final_df)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"下载数据时发生错误: {str(e)}")
            return False

    def load_stock_list(self, csv_path: str) -> List[StockInfo]:
        """
        从CSV文件加载股票列表
        :param csv_path: CSV文件路径
        :return: 股票信息列表
        """
        try:
            df = pd.read_csv(csv_path)
            return [StockInfo(code=row['Ticker'], name=row['Name']) for _, row in df.iterrows()]
        except Exception as e:
            logger.error(f"加载股票列表失败: {str(e)}")
            return []

def test_single_stock(stock_code: str, start_date: str = None, end_date: str = None):
    """
    测试单只股票的数据下载
    
    :param stock_code: 股票代码
    :param start_date: 开始日期
    :param end_date: 结束日期
    :return: 是否成功下载数据
    """
    downloader = FutuDataDownloader()
    stock_info = StockInfo(code=stock_code, name=stock_code)
    
    # 定义需要下载的K线类型
    kl_types = [
        KLType.DAILY,    # 日线
        KLType.HOUR_1,   # 1小时线
        KLType.MIN_30,   # 30分钟线
        KLType.MIN_15,   # 15分钟线
        KLType.MIN_5     # 5分钟线
    ]
    
    success = True  # 标记是否成功下载所有数据
    
    try:
        # 建立连接
        if not downloader.connect():
            return False
            
        # 下载每种类型的数据
        for kl_type in kl_types:
            logger.info(f"开始处理 {stock_info.code} 的 {kl_type.file_suffix} 数据")
            retry_count = 0
            max_retries = 3
            kl_success = False  # 标记当前K线类型是否成功下载
            
            while retry_count < max_retries and not kl_success:
                try:
                    # 检查是否需要更新
                    need_update, update_start, update_end = downloader._check_need_update(
                        stock_info, kl_type, start_date, end_date
                    )
                    
                    if not need_update:
                        logger.info(f"{stock_info.code} 的 {kl_type.file_suffix} 数据已是最新，跳过下载")
                        kl_success = True
                        break  # 跳出当前循环，处理下一个K线类型
                        
                    kl_success = downloader.download_history_data(
                        stock_info=stock_info,
                        kl_type=kl_type,
                        start_date=update_start,  # 使用_check_need_update返回的时间范围
                        end_date=update_end
                    )
                    
                    if kl_success:
                        time.sleep(1)  # 只有在成功下载新数据后才进行等待
                except Exception as e:
                    logger.error(f"下载失败，重试次数 {retry_count + 1}/{max_retries}: {str(e)}")
                    retry_count += 1
                    time.sleep(5 * (retry_count + 1))
            
            # 如果当前K线类型下载失败，标记整体下载失败
            if not kl_success:
                success = False
                logger.warning(f"{stock_info.code} 的 {kl_type.file_suffix} 数据下载失败")
            
            logger.info("--------")  # 添加分隔符以便于观察执行到哪个环节
            
    except Exception as e:
        logger.error(f"处理 {stock_info.code} 时发生错误: {str(e)}")
        success = False
    finally:
        # 确保断开连接
        downloader.disconnect()
    
    logger.info(f"完成 {stock_info.code} 的数据下载，状态: {'成功' if success else '失败'}")
    return success

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='下载富途股票数据')
    parser.add_argument('--code', type=str, help='股票代码（带市场前缀，如HK.00700）')
    parser.add_argument('--start', type=str, help='开始日期（YYYY-MM-DD格式）')
    parser.add_argument('--end', type=str, help='结束日期（YYYY-MM-DD格式，可选）')
    parser.add_argument('--all', type=str, choices=['HK', 'CN'], help='下载指定市场的所有股票数据')
    parser.add_argument('--batch-size', type=int, default=50, help='批量处理的股票数量，处理完一批后会暂停一段时间')
    parser.add_argument('--batch-pause', type=int, default=60, help='每批次处理完后暂停的秒数')
    
    args = parser.parse_args()
    
    if args.all:
        downloader = FutuDataDownloader()
        csv_file = f'Data/tickers_{args.all.lower()}.csv'
        stock_list = downloader.load_stock_list(csv_file)
        if not stock_list:
            logger.error(f"没有找到{args.all}市场的股票列表或列表为空")
            return

        total_stocks = len(stock_list)
        updated_count = 0
        skipped_count = 0
        error_count = 0
        
        # 分批处理股票列表
        batch_size = args.batch_size
        batch_pause = args.batch_pause
        
        for batch_idx in range(0, total_stocks, batch_size):
            batch_end = min(batch_idx + batch_size, total_stocks)
            logger.info(f"开始处理第 {batch_idx//batch_size + 1} 批股票 ({batch_idx+1}-{batch_end}/{total_stocks})")
            
            # 处理当前批次的股票
            for i in range(batch_idx, batch_end):
                stock_info = stock_list[i]
                logger.info(f"开始处理第 {i+1}/{total_stocks} 只股票: {stock_info.code}")
                
                try:
                    # 检查是否所有周期都是最新的
                    all_updated = True
                    for kl_type in [KLType.DAILY, KLType.HOUR_1, KLType.MIN_30, KLType.MIN_15, KLType.MIN_5]:
                        need_update, _, _ = downloader._check_need_update(stock_info, kl_type, args.start, args.end)
                        if need_update:
                            all_updated = False
                            break
                    
                    if all_updated:
                        logger.info(f"{stock_info.code} 所有周期数据都是最新的，跳过处理")
                        skipped_count += 1
                        continue
                    
                    # 下载数据
                    success = test_single_stock(
                        stock_code=stock_info.code,
                        start_date=args.start,
                        end_date=args.end
                    )
                    
                    if success:
                        updated_count += 1
                    else:
                        error_count += 1
                        logger.warning(f"{stock_info.code} 数据下载失败")
                    
                    # 每只股票处理完后等待一小段时间，避免频繁请求
                    if i < batch_end - 1:  # 不是批次中的最后一只
                        time.sleep(2)  # 股票间短暂等待
                
                except Exception as e:
                    error_count += 1
                    logger.error(f"处理 {stock_info.code} 时发生错误: {str(e)}")
                    # 发生错误后短暂等待，避免连续错误
                    time.sleep(5)
            
            # 每批次处理完后暂停一段时间，让API限制重置
            if batch_end < total_stocks:  # 不是最后一批
                logger.info(f"第 {batch_idx//batch_size + 1} 批处理完成，暂停 {batch_pause} 秒后继续...")
                time.sleep(batch_pause)
        
        logger.info(f"数据下载完成！总计 {total_stocks} 只股票，"
                   f"更新 {updated_count} 只，跳过 {skipped_count} 只，失败 {error_count} 只")
    
    elif args.code:
        test_single_stock(
            stock_code=args.code,
            start_date=args.start,
            end_date=args.end
        )
    else:
        parser.print_help()

if __name__ == "__main__":
    main() 