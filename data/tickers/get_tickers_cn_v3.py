#!/usr/bin/env python3
"""
A股潜力股候选池筛选 V3.0 - 主涨段前期识别系统
专注于发现即将进入主涨段的潜力股票

V3.0 核心理念：
- 不追涨，找蓄势：寻找还在相对低位但开始活跃的股票
- 宽松筛选：宁可多选不可漏选，为后续主涨段提供充足候选池
- 前瞻性指标：关注资金流入迹象而非已经大涨的股票
- 近两周视角：分析近期持续关注度，而非单日爆发

筛选策略：
1. 基础健康 - 排除问题股，保留稳健标的
2. 近期活跃 - 近两周有资金关注，但未大幅上涨
3. 技术准备 - 处于相对低位，技术形态健康
4. 流动性充足 - 有足够成交量支撑后续上涨
5. 基本面过关 - 避开基本面恶化的股票
"""

import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
import time
import warnings
warnings.filterwarnings('ignore')


class PotentialStockSelectorV3:
    """潜力股候选池筛选V3.0"""
    
    def __init__(self):
        self.today = datetime.now()
        self.two_weeks_ago = self.today - timedelta(days=14)
        
        # V3.0 潜力股筛选参数（宽松策略）
        self.config = {
            # 基础健康筛选
            'min_market_cap': 20 * 100000000,     # 最小市值20亿（降低门槛）
            'max_market_cap': 1000 * 100000000,   # 最大市值1000亿
            'min_listing_days': 120,              # 最少上市120天
            'exclude_st': True,                   # 排除ST股
            
            # 活跃度筛选（宽松）
            'min_avg_amount': 50000000,           # 近两周日均成交额5000万
            'min_active_days': 6,                 # 近两周至少6天活跃
            'min_turnover': 1.0,                  # 最低换手率1%
            'max_turnover': 30.0,                 # 最高换手率30%（控制炒作）
            
            # 涨跌幅控制（关键：不追涨）
            'max_two_week_gain': 30.0,            # 近两周涨幅不超过30%
            'min_two_week_gain': -15.0,           # 近两周跌幅不超过15%
            'max_daily_gain': 15.0,              # 当日涨幅不超过15%
            'min_daily_gain': -8.0,              # 当日跌幅不超过8%
            
            # 技术位置（寻找低位蓄势）
            'min_position_60d': 0.3,             # 60日内价格位置至少30%
            'max_position_60d': 0.8,             # 60日内价格位置不超过80%
            'max_volatility': 100.0,             # 最大60日波动率100%
            
            # 趋势健康度（温和要求）
            'allow_consolidation': True,          # 允许横盘整理
            'min_volume_growth': -20.0,          # 成交量不能萎缩超过20%
            
            # 其他参数
            'max_candidates': 50,                # 最大候选数量
            'request_delay': 0.08,               # 请求延迟
        }
    
    def get_basic_pool(self):
        """获取基础股票池"""
        print("📊 正在获取A股基础数据...")
        all_stocks = ak.stock_zh_a_spot_em()
        print(f"🔍 A股总数: {len(all_stocks)} 只")
        
        # 基础健康筛选（非常宽松）
        basic_pool = all_stocks[
            # 排除问题股
            (~all_stocks['名称'].str.contains('ST|退|暂停|^[NC]')) &
            (~all_stocks['代码'].str.startswith('8')) &
            (~all_stocks['代码'].str.startswith('92')) &
            
            # 市值和流动性
            (all_stocks['总市值'] >= self.config['min_market_cap']) &
            (all_stocks['总市值'] <= self.config['max_market_cap']) &
            (all_stocks['成交额'] >= self.config['min_avg_amount']) &
            (all_stocks['换手率'] >= self.config['min_turnover']) &
            (all_stocks['换手率'] <= self.config['max_turnover']) &
            
            # 当日涨跌幅控制（不追涨不杀跌）
            (all_stocks['涨跌幅'] >= self.config['min_daily_gain']) &
            (all_stocks['涨跌幅'] <= self.config['max_daily_gain'])
        ].copy()
        
        print(f"✅ 基础筛选后: {len(basic_pool)} 只")
        return basic_pool
    
    def analyze_two_week_activity(self, stock_data):
        """分析近两周活跃度"""
        code = stock_data['代码']
        name = stock_data['名称']
        
        try:
            # 获取近两周数据
            start_date = self.two_weeks_ago.strftime('%Y%m%d')
            end_date = self.today.strftime('%Y%m%d')
            
            hist_df = ak.stock_zh_a_hist(
                symbol=code, period="daily",
                start_date=start_date, end_date=end_date, adjust="qfq"
            )
            
            if len(hist_df) < 8:  # 至少8个交易日
                return None
            
            # 检查上市时间
            first_date = pd.to_datetime(hist_df.iloc[0]['日期'])
            days_since_listing = (self.today - first_date).days
            if days_since_listing < self.config['min_listing_days']:
                return None
            
            # 计算近两周指标
            latest = hist_df.iloc[-1]
            first = hist_df.iloc[0]
            
            # 两周涨跌幅
            two_week_return = (latest['收盘'] / first['收盘'] - 1) * 100
            
            # 检查涨跌幅是否在合理范围（关键：不要已经大涨的）
            if (two_week_return > self.config['max_two_week_gain'] or 
                two_week_return < self.config['min_two_week_gain']):
                return None
            
            # 平均成交额
            avg_amount = hist_df['成交额'].mean()
            if avg_amount < self.config['min_avg_amount']:
                return None
            
            # 活跃天数（成交额超过平均值80%的天数）
            active_days = len(hist_df[hist_df['成交额'] > avg_amount * 0.8])
            if active_days < self.config['min_active_days']:
                return None
            
            # 获取更长期数据分析技术位置
            start_date_long = (self.today - timedelta(days=90)).strftime('%Y%m%d')
            hist_long = ak.stock_zh_a_hist(
                symbol=code, period="daily",
                start_date=start_date_long, end_date=end_date, adjust="qfq"
            )
            
            if len(hist_long) < 60:
                return None
            
            # 计算60日内价格位置
            high_60d = hist_long['最高'].max()
            low_60d = hist_long['最低'].min()
            current_position = (latest['收盘'] - low_60d) / (high_60d - low_60d) if (high_60d > low_60d) else 0.5
            
            # 关键筛选：寻找相对低位但开始活跃的股票
            if (current_position < self.config['min_position_60d'] or 
                current_position > self.config['max_position_60d']):
                return None
            
            # 价格波动率控制
            price_volatility = (high_60d / low_60d - 1) * 100 if low_60d > 0 else 999
            if price_volatility > self.config['max_volatility']:
                return None
            
            # 成交量趋势分析
            early_vol = hist_df['成交量'].head(5).mean()
            recent_vol = hist_df['成交量'].tail(5).mean()
            volume_growth = (recent_vol / early_vol - 1) * 100 if early_vol > 0 else 0
            
            # 允许成交量温和萎缩，但不能大幅萎缩
            if volume_growth < self.config['min_volume_growth']:
                return None
            
            # 计算潜力评分（注重未来潜力而非当前表现）
            potential_score = (
                # 活跃度评分
                min(active_days, 10) * 1.0 +
                min(avg_amount / 100000000, 10) * 0.8 +
                
                # 位置评分（中低位加分）
                (0.6 - current_position) * 10 if current_position < 0.6 else 0 +
                
                # 稳定性评分
                max(0, 100 - price_volatility) * 0.05 +
                
                # 成交量健康度
                max(volume_growth, 0) * 0.1 +
                
                # 两周表现适中性（不要涨太多，也不要跌太多）
                max(0, 10 - abs(two_week_return)) * 0.3
            )
            
            return {
                '代码': code,
                '名称': name,
                '最新价': latest['收盘'],
                '当日涨跌幅': latest['涨跌幅'],
                '两周涨跌幅': round(two_week_return, 2),
                '两周均成交额(万)': round(avg_amount / 10000, 0),
                '活跃天数': active_days,
                '总交易日': len(hist_df),
                '60日位置': round(current_position * 100, 1),
                '价格波动率': round(price_volatility, 1),
                '成交量增长': round(volume_growth, 1),
                '潜力评分': round(potential_score, 2),
                '总市值(亿)': round(stock_data['总市值'] / 100000000, 1),
                '当前换手率': stock_data['换手率'],
                '上市天数': days_since_listing
            }
            
        except Exception as e:
            return None
    
    def run_screening(self):
        """运行潜力股筛选"""
        print("🎯 A股潜力股候选池筛选 V3.0")
        print("=" * 60)
        print("🔍 策略：寻找即将启动的主涨段候选股票")
        print("📈 重点：相对低位 + 近期活跃 + 技术健康")
        print("=" * 60)
        
        # 1. 获取基础池
        basic_pool = self.get_basic_pool()
        
        if len(basic_pool) == 0:
            print("❌ 基础筛选无结果")
            return pd.DataFrame()
        
        # 2. 按成交额排序，优先分析活跃股票
        if len(basic_pool) > 200:
            basic_pool = basic_pool.sort_values(by='成交额', ascending=False).head(200)
            print(f"⚡ 优先分析前200只活跃股票")
        
        # 3. 逐个分析两周活跃度
        print(f"🔬 开始深度分析（预计需要{len(basic_pool) * 0.12 / 60:.1f}分钟）...")
        
        candidates = []
        total = len(basic_pool)
        
        for idx, (_, stock) in enumerate(basic_pool.iterrows()):
            if idx % 25 == 0:
                progress = (idx + 1) / total * 100
                print(f"📊 分析进度: {idx+1}/{total} ({progress:.1f}%) - {stock['名称']}")
            
            result = self.analyze_two_week_activity(stock)
            if result:
                candidates.append(result)
                print(f"🌟 发现潜力股: {result['名称']} (潜力评分: {result['潜力评分']}, 位置: {result['60日位置']}%)")
            
            time.sleep(self.config['request_delay'])
        
        # 4. 结果处理
        if not candidates:
            print("❌ 未发现符合条件的潜力股")
            return pd.DataFrame()
        
        result_df = pd.DataFrame(candidates)
        
        # 按潜力评分排序
        final_result = result_df.sort_values(
            by=['潜力评分', '活跃天数'], ascending=[False, False]
        ).head(self.config['max_candidates'])
        
        print(f"✅ 潜力股筛选完成，发现 {len(final_result)} 只候选股票")
        
        # 5. 保存结果
        timestamp = self.today.strftime('%Y%m%d_%H%M%S')
        filename = f"potential_stocks_v3_{timestamp}.csv"
        final_result.to_csv(filename, index=False, encoding='utf-8-sig')
        final_result.to_csv("tickers_cn.csv", index=False, encoding='utf-8-sig')
        
        print(f"💾 结果已保存到: {filename} 和 tickers_cn.csv")
        
        return final_result
    
    def analyze_results(self, result_df):
        """分析筛选结果"""
        if result_df.empty:
            return
        
        print("\n" + "=" * 60)
        print("📊 潜力股候选池分析报告")
        print("=" * 60)
        
        print(f"🎯 筛选出潜力股: {len(result_df)} 只")
        print(f"⭐ 平均潜力评分: {result_df['潜力评分'].mean():.2f}")
        print(f"💰 平均市值: {result_df['总市值(亿)'].mean():.1f} 亿元")
        print(f"📍 平均60日位置: {result_df['60日位置'].mean():.1f}%")
        print(f"📈 平均两周涨跌幅: {result_df['两周涨跌幅'].mean():.2f}%")
        print(f"🔄 平均换手率: {result_df['当前换手率'].mean():.2f}%")
        
        # 位置分布分析
        low_position = len(result_df[result_df['60日位置'] < 50])
        mid_position = len(result_df[(result_df['60日位置'] >= 50) & (result_df['60日位置'] < 70)])
        high_position = len(result_df[result_df['60日位置'] >= 70])
        
        print(f"\n📍 价格位置分布:")
        print(f"   低位蓄势 (<50%): {low_position} 只")
        print(f"   中位整理 (50-70%): {mid_position} 只") 
        print(f"   相对高位 (>70%): {high_position} 只")
        
        # 投资策略建议
        print(f"\n💡 潜力股投资策略:")
        print(f"   1. 重点关注潜力评分 > {result_df['潜力评分'].quantile(0.7):.1f} 的股票")
        print(f"   2. 优先布局60日位置 < 60% 的低位股票（上涨空间大）")
        print(f"   3. 关注活跃天数 ≥ 8天的股票（资金持续关注）")
        print(f"   4. 分批建仓，每只股票仓位 1-2%（候选池较大）")
        print(f"   5. 止损设置 -10%，给予足够耐心等待主升浪")
        
        print(f"\n🎯 主涨段识别要点:")
        print(f"   - 这些股票目前处于相对低位，适合提前布局")
        print(f"   - 近两周有资金关注但未大涨，具备启动潜力")
        print(f"   - 后续关注成交量放大和技术突破信号")
        print(f"   - 预期1-2个月内可能出现主升浪机会")
        
        print("\n⚠️ 风险提示:")
        print("   - 潜力股投资周期较长，需要耐心持有")
        print("   - 不是所有候选股都会启动，需要分散投资")
        print("   - 市场环境变化可能影响个股表现")
        print("   - 建议结合基本面研究进行最终筛选")


def main():
    """主函数"""
    print("🌟 A股潜力股候选池筛选系统 V3.0")
    print("📅 运行时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("🎯 目标：发现即将进入主涨段的潜力股票")
    
    # 创建筛选器
    selector = PotentialStockSelectorV3()
    
    # 运行筛选
    result_df = selector.run_screening()
    
    # 显示和分析结果
    if not result_df.empty:
        print("\n📋 潜力股候选池预览:")
        display_columns = ['代码', '名称', '两周涨跌幅', '潜力评分', '60日位置', '活跃天数']
        print(result_df[display_columns].head(20))
        
        # 分析结果
        selector.analyze_results(result_df)
    
    print("\n✅ 潜力股筛选完成！")


if __name__ == "__main__":
    main()