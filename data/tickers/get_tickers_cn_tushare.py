#!/usr/bin/env python3
"""
A股股票代码获取模块
===============
功能: 获取沪深300、中证500、创业板等主要指数成分股
特点: 多指数合并、市值筛选、数据验证
"""

import pandas as pd
import tushare as ts
from concurrent.futures import ThreadPoolExecutor
import os
from pathlib import Path
from dotenv import load_dotenv
import warnings

warnings.filterwarnings('ignore')

# 加载环境变量
load_dotenv()

def init_tushare():
    """初始化tushare连接"""
    token = os.getenv('TUSHARE_TOKEN')
    if not token:
        raise ValueError("未找到TUSHARE_TOKEN，请检查.env文件")
    ts.set_token(token)
    return ts.pro_api()

def get_hs300_tickers(pro):
    """获取沪深300成分股"""
    try:
        df = pro.index_weight(index_code='399300.SZ', trade_date='')
        if df.empty:
            # 尝试获取最新交易日数据
            trade_dates = pro.trade_cal(exchange='', start_date='20240101', end_date='20241231')
            trade_dates = trade_dates[trade_dates['is_open'] == 1]['cal_date'].tolist()
            if trade_dates:
                latest_date = max(trade_dates)
                df = pro.index_weight(index_code='399300.SZ', trade_date=latest_date)
        
        if not df.empty:
            return df['con_code'].tolist()
    except Exception as e:
        print(f"获取沪深300失败: {e}")
    return []

def get_zz500_tickers(pro):
    """获取中证500成分股"""
    try:
        df = pro.index_weight(index_code='399905.SZ', trade_date='')
        if df.empty:
            # 尝试获取最新交易日数据
            trade_dates = pro.trade_cal(exchange='', start_date='20240101', end_date='20241231')
            trade_dates = trade_dates[trade_dates['is_open'] == 1]['cal_date'].tolist()
            if trade_dates:
                latest_date = max(trade_dates)
                df = pro.index_weight(index_code='399905.SZ', trade_date=latest_date)
        
        if not df.empty:
            return df['con_code'].tolist()
    except Exception as e:
        print(f"获取中证500失败: {e}")
    return []

def get_cyb_tickers(pro):
    """获取创业板指成分股"""
    try:
        df = pro.index_weight(index_code='399006.SZ', trade_date='')
        if df.empty:
            trade_dates = pro.trade_cal(exchange='', start_date='20240101', end_date='20241231')
            trade_dates = trade_dates[trade_dates['is_open'] == 1]['cal_date'].tolist()
            if trade_dates:
                latest_date = max(trade_dates)
                df = pro.index_weight(index_code='399006.SZ', trade_date=latest_date)
        
        if not df.empty:
            return df['con_code'].tolist()
    except Exception as e:
        print(f"获取创业板指失败: {e}")
    return []

def get_stock_basic_info(pro, ts_codes):
    """获取股票基本信息"""
    try:
        # 分批获取，避免API限制
        batch_size = 1000
        all_stocks = []
        
        for i in range(0, len(ts_codes), batch_size):
            batch_codes = ts_codes[i:i+batch_size]
            codes_str = ','.join(batch_codes)
            
            df = pro.stock_basic(ts_code=codes_str, list_status='L')
            if not df.empty:
                all_stocks.append(df)
        
        if all_stocks:
            combined_df = pd.concat(all_stocks, ignore_index=True)
            return combined_df[['ts_code', 'name', 'market']]
        
    except Exception as e:
        print(f"获取股票基本信息失败: {e}")
    
    return pd.DataFrame()

def filter_quality_stocks(pro, ts_codes, min_market_cap=50):
    """筛选高质量股票"""
    try:
        # 获取最新的市值数据
        trade_dates = pro.trade_cal(exchange='', start_date='20240101', end_date='20241231')
        trade_dates = trade_dates[trade_dates['is_open'] == 1]['cal_date'].tolist()
        
        if not trade_dates:
            return ts_codes
        
        latest_date = max(trade_dates)
        
        # 分批获取市值数据
        quality_stocks = []
        batch_size = 100
        
        for i in range(0, len(ts_codes), batch_size):
            batch_codes = ts_codes[i:i+batch_size]
            
            for ts_code in batch_codes:
                try:
                    # 获取股票基本信息
                    basic_info = pro.stock_basic(ts_code=ts_code, list_status='L')
                    if basic_info.empty:
                        continue
                    
                    # 获取最新日线数据查看成交量和价格
                    daily_data = pro.daily_basic(ts_code=ts_code, trade_date=latest_date)
                    if not daily_data.empty:
                        total_mv = daily_data['total_mv'].iloc[0]  # 总市值(万元)
                        if pd.notna(total_mv) and total_mv >= min_market_cap * 10000:  # 转换为万元
                            quality_stocks.append(ts_code)
                    
                except Exception:
                    continue
        
        return quality_stocks
        
    except Exception as e:
        print(f"筛选股票失败: {e}")
        return ts_codes

def main():
    """主函数"""
    print("获取A股股票代码列表...")
    
    # 初始化tushare
    try:
        pro = init_tushare()
    except Exception as e:
        print(f"Tushare初始化失败: {e}")
        return
    
    # 获取各指数成分股
    print("获取沪深300成分股...")
    hs300_tickers = get_hs300_tickers(pro)
    
    print("获取中证500成分股...")
    zz500_tickers = get_zz500_tickers(pro)
    
    print("获取创业板指成分股...")
    cyb_tickers = get_cyb_tickers(pro)
    
    # 合并去重
    all_tickers = list(set(hs300_tickers + zz500_tickers + cyb_tickers))
    
    print(f"合并后总股票数: {len(all_tickers)}")
    
    if not all_tickers:
        print("未获取到股票代码，尝试获取全部A股列表...")
        # 备用方案：获取全部A股
        try:
            stock_basic = pro.stock_basic(exchange='', list_status='L')
            all_tickers = stock_basic['ts_code'].tolist()
            print(f"获取全部A股: {len(all_tickers)} 只")
        except Exception as e:
            print(f"获取全部A股失败: {e}")
            return
    
    # 获取股票基本信息
    print("获取股票基本信息...")
    basic_info = get_stock_basic_info(pro, all_tickers)
    
    if basic_info.empty:
        # 简化版本：只包含代码
        tickers_df = pd.DataFrame({
            'Ticker': all_tickers,
            'Name': [''] * len(all_tickers)
        })
    else:
        # 转换为标准格式，保持与美股一致
        tickers_df = pd.DataFrame({
            'Ticker': basic_info['ts_code'],
            'Name': basic_info['name']
        })
    
    # 按代码排序
    tickers_df = tickers_df.sort_values('Ticker').reset_index(drop=True)
    
    # 保存到CSV
    output_path = Path("data/tickers_cn.csv")
    output_path.parent.mkdir(exist_ok=True)
    tickers_df.to_csv(output_path, index=False)
    
    print(f"A股股票代码已保存到: {output_path}")
    print(f"总计: {len(tickers_df)} 只股票")
    
    # 显示前几行预览
    print("\n前10只股票预览:")
    print(tickers_df.head(10))

if __name__ == "__main__":
    main()
