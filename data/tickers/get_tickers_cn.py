#!/usr/bin/env python3
"""
中国A股活跃股票获取工具
包含多种筛选策略：龙虎榜、热门榜、四维筛选模型等
"""

import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
import time
from typing import List, Dict, Optional
import warnings
warnings.filterwarnings('ignore')


class CNStockTickers:
    """中国A股活跃股票获取类 - 专业版筛选主涨段候选池"""
    
    def __init__(self):
        self.today = datetime.now()
        self.two_weeks_ago = self.today - timedelta(days=14)
        
    def _calculate_momentum_score(self, hist_df: pd.DataFrame) -> dict:
        """
        计算动量评分 - 关键的主涨段识别指标
        
        Args:
            hist_df: 历史行情数据
            
        Returns:
            dict: 包含各种动量指标的评分
        """
        if len(hist_df) < 20:
            return {}
            
        latest = hist_df.iloc[-1]
        
        # 1. 价格动量（近5日相对强度）
        price_5d_change = (latest['收盘'] / hist_df.iloc[-6]['收盘'] - 1) * 100 if len(hist_df) >= 6 else 0
        
        # 2. 成交量动量（相对放量程度）
        vol_ma5 = hist_df['成交量'].tail(5).mean()
        vol_ma20 = hist_df['成交量'].tail(20).mean()
        volume_ratio = vol_ma5 / vol_ma20 if vol_ma20 > 0 else 1
        
        # 3. 突破强度（相对历史高点的位置）
        high_60d = hist_df['最高'].tail(60).max()
        breakout_strength = (latest['收盘'] / high_60d - 1) * 100 if high_60d > 0 else -100
        
        # 4. 趋势稳定性（均线排列质量）
        ma5 = hist_df['收盘'].tail(5).mean()
        ma10 = hist_df['收盘'].tail(10).mean()
        ma20 = hist_df['收盘'].tail(20).mean()
        
        trend_score = 0
        if ma5 > ma10 > ma20 and latest['收盘'] > ma5:
            trend_score = 10
        elif ma5 > ma10 and latest['收盘'] > ma5:
            trend_score = 6
        elif latest['收盘'] > ma20:
            trend_score = 3
            
        # 5. 连续上涨天数
        consecutive_up_days = 0
        for i in range(len(hist_df)-1, max(0, len(hist_df)-8), -1):
            if hist_df.iloc[i]['收盘'] > hist_df.iloc[i-1]['收盘']:
                consecutive_up_days += 1
            else:
                break
                
        return {
            'price_momentum': price_5d_change,
            'volume_momentum': volume_ratio,
            'breakout_strength': breakout_strength,
            'trend_score': trend_score,
            'consecutive_up_days': consecutive_up_days,
            'total_score': (price_5d_change * 0.3 + 
                          min(volume_ratio * 10, 20) * 0.2 +
                          max(breakout_strength, 0) * 0.2 +
                          trend_score * 0.2 +
                          min(consecutive_up_days * 2, 10) * 0.1)
        }
        
    def get_lhb_tickers(self, limit: int = 50) -> pd.DataFrame:
        """
        获取龙虎榜股票 - 最权威的活跃股票来源
        
        Args:
            limit: 返回股票数量限制
            
        Returns:
            DataFrame: 包含龙虎榜股票信息
        """
        print("正在获取龙虎榜数据...")
        try:
            lhb_data = ak.stock_lhb_daily_em()
            if len(lhb_data) == 0:
                print("今日暂无龙虎榜数据")
                return pd.DataFrame()
                
            # 按涨跌幅排序，取前N只
            lhb_sorted = lhb_data.sort_values(by='涨跌幅', ascending=False).head(limit)
            
            result = lhb_sorted[['代码', '名称', '涨跌幅', '换手率', '成交额', '上榜原因']].copy()
            result['数据源'] = '龙虎榜'
            
            print(f"获取到 {len(result)} 只龙虎榜股票")
            return result
            
        except Exception as e:
            print(f"获取龙虎榜数据失败: {e}")
            return pd.DataFrame()
    
    def get_hot_rank_tickers(self, limit: int = 30) -> pd.DataFrame:
        """
        获取热门人气榜股票
        
        Args:
            limit: 返回股票数量限制
            
        Returns:
            DataFrame: 包含热门股票信息
        """
        print("正在获取热门人气榜数据...")
        try:
            # 尝试获取热门股票排名
            hot_data = ak.stock_hot_rank_em()
            
            if len(hot_data) == 0:
                print("暂无热门股票数据")
                return pd.DataFrame()
                
            # 取前N只热门股票
            hot_sorted = hot_data.head(limit)
            
            # 获取这些股票的实时行情数据
            spot_data = ak.stock_zh_a_spot_em()
            spot_dict = {row['代码']: row for _, row in spot_data.iterrows()}
            
            result_list = []
            for _, row in hot_sorted.iterrows():
                code = row['代码']
                if code in spot_dict:
                    spot_info = spot_dict[code]
                    result_list.append({
                        '代码': code,
                        '名称': row['名称'],
                        '涨跌幅': spot_info['涨跌幅'],
                        '换手率': spot_info['换手率'],
                        '成交额': spot_info['成交额'],
                        '热度排名': row.get('序号', 0),
                        '数据源': '热门榜'
                    })
            
            result = pd.DataFrame(result_list)
            print(f"获取到 {len(result)} 只热门股票")
            return result
            
        except Exception as e:
            print(f"获取热门股票数据失败: {e}")
            return pd.DataFrame()
    
    def get_active_by_turnover(self, min_turnover: float = 10.0, 
                              min_amount: float = 1.0, limit: int = 50) -> pd.DataFrame:
        """
        基于换手率和成交额获取活跃股票
        
        Args:
            min_turnover: 最小换手率(%)
            min_amount: 最小成交额(亿元)
            limit: 返回股票数量限制
            
        Returns:
            DataFrame: 包含活跃股票信息
        """
        print("正在根据换手率和成交额筛选活跃股票...")
        try:
            # 获取所有A股实时数据
            all_stocks = ak.stock_zh_a_spot_em()
            
            # 筛选条件
            filtered = all_stocks[
                (all_stocks['换手率'] >= min_turnover) &
                (all_stocks['成交额'] >= min_amount * 100000000) &  # 转换为元
                (~all_stocks['名称'].str.contains('ST')) &  # 排除ST股
                (~all_stocks['代码'].str.startswith('8'))  # 排除北交所
            ].copy()
            
            # 按换手率排序
            result = filtered.sort_values(by='换手率', ascending=False).head(limit)
            result = result[['代码', '名称', '涨跌幅', '换手率', '成交额', '总市值']].copy()
            result['数据源'] = '换手率筛选'
            
            print(f"获取到 {len(result)} 只高换手率股票")
            return result
            
        except Exception as e:
            print(f"获取高换手率股票失败: {e}")
            return pd.DataFrame()
    
    def get_candidate_pool_advanced(self, 
                                   min_market_cap: float = 50.0,
                                   ma_short: int = 60,
                                   ma_long: int = 120,
                                   volume_avg_days: int = 20,
                                   volume_multi: float = 2.0,
                                   breakout_days: int = 60,
                                   hot_sector_count: int = 5,
                                   max_stocks_to_analyze: int = 200) -> pd.DataFrame:
        """
        四维筛选模型：候选池 = (基础健康 + 趋势向上) ∩ (成交量显著放大) ∩ (股价突破关键位置) ∩ (属于市场热点题材)
        
        Args:
            min_market_cap: 最小市值(亿元)
            ma_short: 短期均线天数
            ma_long: 长期均线天数
            volume_avg_days: 计算平均成交量的天数
            volume_multi: 成交量放大倍数
            breakout_days: 突破N日新高
            hot_sector_count: 热点板块数量
            max_stocks_to_analyze: 最大分析股票数（避免请求过多）
            
        Returns:
            DataFrame: 符合四维筛选条件的候选池
        """
        print("正在执行四维筛选模型...")
        
        # 1. 获取市场热点板块
        print("1/4 获取市场热点板块...")
        try:
            hot_sectors_df = ak.stock_board_industry_spot_em()
            hot_sectors_list = hot_sectors_df.sort_values(
                by='涨跌幅', ascending=False
            ).head(hot_sector_count)['板块名称'].tolist()
            print(f"当前热点板块: {hot_sectors_list}")
        except Exception as e:
            print(f"获取热点板块失败: {e}")
            hot_sectors_list = []
        
        # 2. 基础筛选
        print("2/4 进行基础健康筛选...")
        all_stocks_df = ak.stock_zh_a_spot_em()
        
        filtered_stocks = all_stocks_df[
            (~all_stocks_df['名称'].str.contains('ST')) &
            (~all_stocks_df['代码'].str.startswith('8')) &  # 排除北交所
            (all_stocks_df['总市值'] > min_market_cap * 100000000) &  # 转换为元
            (all_stocks_df['所属行业'].isin(hot_sectors_list))  # 热点板块筛选
        ].copy()
        
        print(f"基础筛选后剩余股票数量: {len(filtered_stocks)}")
        
        if len(filtered_stocks) == 0:
            print("基础筛选后无股票符合条件")
            return pd.DataFrame()
        
        # 限制分析数量，避免请求过多
        if len(filtered_stocks) > max_stocks_to_analyze:
            filtered_stocks = filtered_stocks.head(max_stocks_to_analyze)
            print(f"限制分析股票数量为: {max_stocks_to_analyze}")
        
        # 3. 技术分析筛选
        print("3/4 进行技术分析筛选...")
        candidate_pool = []
        start_date = (self.today - timedelta(days=200)).strftime('%Y%m%d')
        end_date = self.today.strftime('%Y%m%d')
        
        total_stocks = len(filtered_stocks)
        for idx, (index, stock) in enumerate(filtered_stocks.iterrows()):
            code = stock['代码']
            name = stock['名称']
            sector = stock['所属行业']
            
            if idx % 10 == 0:
                print(f"分析进度: {idx+1}/{total_stocks} - {name}")
            
            try:
                # 获取历史数据
                hist_df = ak.stock_zh_a_hist(
                    symbol=code, period="daily", 
                    start_date=start_date, end_date=end_date, adjust="qfq"
                )
                
                if len(hist_df) < ma_long + 5:
                    continue
                
                # 计算技术指标
                hist_df[f'MA{ma_short}'] = hist_df['收盘'].rolling(window=ma_short).mean()
                hist_df[f'MA{ma_long}'] = hist_df['收盘'].rolling(window=ma_long).mean()
                hist_df[f'Volume_MA{volume_avg_days}'] = hist_df['成交量'].rolling(window=volume_avg_days).mean()
                
                latest_data = hist_df.iloc[-1]
                
                # 趋势向上判断
                is_upward_trend = (
                    latest_data['收盘'] > latest_data[f'MA{ma_short}'] and
                    latest_data[f'MA{ma_short}'] > latest_data[f'MA{ma_long}']
                )
                
                if not is_upward_trend:
                    continue
                
                # 成交量放大判断
                if len(hist_df) >= 2:
                    avg_volume = hist_df.iloc[-2][f'Volume_MA{volume_avg_days}']
                    latest_volume = latest_data['成交量']
                    is_volume_spike = latest_volume > avg_volume * volume_multi
                else:
                    is_volume_spike = False
                
                if not is_volume_spike:
                    continue
                
                # 股价突破判断
                high_in_period = hist_df['最高'].rolling(window=breakout_days).max().shift(1).iloc[-1]
                is_breakout = latest_data['收盘'] > high_in_period
                
                if not is_breakout:
                    continue
                
                # 所有条件都满足
                candidate_pool.append({
                    '代码': code,
                    '名称': name,
                    '所属行业': sector,
                    '收盘价': latest_data['收盘'],
                    '涨跌幅': latest_data['涨跌幅'],
                    '总市值': stock['总市值'],
                    '换手率': stock['换手率'],
                    '成交额': stock['成交额'],
                    '数据源': '四维筛选'
                })
                
                # 友好访问，避免IP被封
                time.sleep(0.1)
                
            except Exception as e:
                continue
        
        # 4. 整理结果
        print("4/4 整理筛选结果...")
        if not candidate_pool:
            print("未发现符合四维筛选条件的股票")
            return pd.DataFrame()
        
        result_df = pd.DataFrame(candidate_pool)
        result_df['总市值(亿)'] = result_df['总市值'] / 100000000
        result_df = result_df.drop('总市值', axis=1)
        
        print(f"四维筛选完成，发现 {len(result_df)} 只候选股票")
        return result_df
    
    def get_all_active_tickers(self, save_to_file: bool = True) -> Dict[str, pd.DataFrame]:
        """
        获取所有活跃股票（综合多种方法）
        
        Args:
            save_to_file: 是否保存结果到文件
            
        Returns:
            Dict: 包含各种方法获取的股票列表
        """
        print("=" * 60)
        print("开始获取A股活跃股票候选池")
        print("=" * 60)
        
        results = {}
        
        # 1. 龙虎榜股票
        results['龙虎榜'] = self.get_lhb_tickers()
        
        # 2. 热门股票
        results['热门榜'] = self.get_hot_rank_tickers()
        
        # 3. 高换手率股票
        results['高换手率'] = self.get_active_by_turnover()
        
        # 4. 四维筛选候选池（简化版，减少分析数量）
        results['四维筛选'] = self.get_candidate_pool_advanced(max_stocks_to_analyze=100)
        
        # 合并所有结果
        all_dfs = [df for df in results.values() if not df.empty]
        if all_dfs:
            combined = pd.concat(all_dfs, ignore_index=True)
            
            # 去重（基于股票代码）
            combined_unique = combined.drop_duplicates(subset=['代码'], keep='first')
            results['合并去重'] = combined_unique
            
            print("\n" + "=" * 60)
            print("获取结果汇总:")
            for method, df in results.items():
                if not df.empty:
                    print(f"{method}: {len(df)} 只股票")
                else:
                    print(f"{method}: 0 只股票")
            print("=" * 60)
            
            # 保存到文件
            if save_to_file:
                timestamp = self.today.strftime('%Y%m%d_%H%M%S')
                
                for method, df in results.items():
                    if not df.empty:
                        filename = f"active_stocks_{method}_{timestamp}.csv"
                        df.to_csv(filename, index=False, encoding='utf-8-sig')
                        print(f"已保存 {method} 结果到: {filename}")
        
        return results

    def get_optimized_main_wave_candidates(self, 
                                         min_score: float = 15.0,
                                         min_market_cap: float = 30.0,
                                         max_candidates: int = 50) -> pd.DataFrame:
        """
        优化版主涨段候选池筛选 - 专业投资策略
        
        基于A股市场特点的科学筛选方法：
        1. 动量共振：价格动量 + 成交量动量
        2. 技术突破：突破关键阻力位 + 趋势确认
        3. 基本面健康：避开问题股
        4. 市场情绪：结合热点板块
        
        Args:
            min_score: 最低动量评分
            min_market_cap: 最小市值(亿元)
            max_candidates: 最大候选数量
            
        Returns:
            DataFrame: 优化筛选的主涨段候选池
        """
        print("=" * 60)
        print("执行优化版主涨段候选池筛选")
        print("=" * 60)
        
        # 1. 获取市场热点板块（更严格的筛选）
        print("1/5 获取市场热点板块...")
        try:
            hot_sectors_df = ak.stock_board_industry_spot_em()
            # 选择涨幅前3且成交额较大的板块
            hot_sectors = hot_sectors_df[
                (hot_sectors_df['涨跌幅'] > 1.0) &  # 涨幅至少1%
                (hot_sectors_df['成交额'] > 100000000)  # 成交额至少1亿
            ].sort_values(by='涨跌幅', ascending=False).head(3)['板块名称'].tolist()
            
            print(f"筛选出的热点板块: {hot_sectors}")
        except Exception as e:
            print(f"获取热点板块失败: {e}")
            hot_sectors = []
        
        # 2. 基础筛选 - 更严格的条件
        print("2/5 执行基础健康筛选...")
        all_stocks = ak.stock_zh_a_spot_em()
        
        # 基础筛选条件 - 重点排除新股和炒作股
        healthy_stocks = all_stocks[
            (~all_stocks['名称'].str.contains('ST|退|暂停|C[A-Z]')) &  # 排除ST、退市、暂停、新股C标
            (~all_stocks['代码'].str.startswith('8')) &  # 排除北交所
            (~all_stocks['代码'].str.startswith('92')) &  # 排除北交所新股
            (~all_stocks['名称'].str.contains('^[NC].*')) &  # 排除N开头和C开头的新股
            (all_stocks['总市值'] > min_market_cap * 100000000) &  # 市值筛选
            (all_stocks['总市值'] < 800 * 100000000) &  # 排除超大盘股
            (all_stocks['涨跌幅'] > -6.0) &  # 排除跌幅过大的
            (all_stocks['涨跌幅'] < 12.0) &  # 排除涨幅过大的（避免追高）
            (all_stocks['换手率'] > 1.5) &  # 基本流动性要求
            (all_stocks['换手率'] < 25.0) &  # 排除过度炒作的
            (all_stocks['成交额'] > 100000000)  # 成交额至少1亿（确保流动性）
        ].copy()
        
        print(f"基础筛选后股票数量: {len(healthy_stocks)}")
        
        if len(healthy_stocks) == 0:
            return pd.DataFrame()
        
        # 3. 如果有热点板块，优先选择热点板块的股票
        if hot_sectors:
            priority_stocks = healthy_stocks[
                healthy_stocks['所属行业'].isin(hot_sectors)
            ].copy()
            
            if len(priority_stocks) > 20:
                healthy_stocks = priority_stocks
                print(f"热点板块筛选后股票数量: {len(healthy_stocks)}")
        
        # 限制分析数量（选择换手率和涨跌幅较高的股票优先分析）
        if len(healthy_stocks) > 150:
            healthy_stocks = healthy_stocks.sort_values(
                by=['换手率', '涨跌幅'], ascending=[False, False]
            ).head(150)
            print(f"优先分析股票数量: {len(healthy_stocks)}")
        
        # 4. 技术分析和动量评分
        print("3/5 执行技术分析和动量评分...")
        candidates = []
        start_date = (self.today - timedelta(days=100)).strftime('%Y%m%d')
        end_date = self.today.strftime('%Y%m%d')
        
        total_stocks = len(healthy_stocks)
        for idx, (_, stock) in enumerate(healthy_stocks.iterrows()):
            if idx % 20 == 0:
                print(f"分析进度: {idx+1}/{total_stocks}")
            
            code = stock['代码']
            name = stock['名称']
            
            try:
                # 获取历史数据
                hist_df = ak.stock_zh_a_hist(
                    symbol=code, period="daily",
                    start_date=start_date, end_date=end_date, adjust="qfq"
                )
                
                # 严格的新股过滤：至少需要60个交易日数据
                if len(hist_df) < 60:
                    continue
                
                # 额外新股检查：如果上市时间太短，跳过
                first_date = pd.to_datetime(hist_df.iloc[0]['日期'])
                days_since_listing = (self.today - first_date).days
                if days_since_listing < 90:  # 上市不足3个月
                    continue
                
                # 检查是否是炒作模式（新股特征：极高波动率）
                price_volatility = (hist_df['最高'].max() / hist_df['最低'].min() - 1) * 100
                if price_volatility > 200:  # 波动率超过200%，可能是新股炒作
                    continue
                
                # 计算动量评分
                momentum_metrics = self._calculate_momentum_score(hist_df)
                
                if not momentum_metrics or momentum_metrics['total_score'] < min_score:
                    continue
                
                # 额外的质量检查
                latest = hist_df.iloc[-1]
                
                # 检查是否处于相对低位启动（避免追高）
                high_20d = hist_df['最高'].tail(20).max()
                current_position = latest['收盘'] / high_20d
                
                # 检查近期是否有明显放量
                recent_volume = hist_df['成交量'].tail(3).mean()
                avg_volume = hist_df['成交量'].tail(20).mean()
                volume_surge = recent_volume / avg_volume if avg_volume > 0 else 1
                
                # 主涨段特征检查
                if (current_position > 0.85 and  # 接近或创新高
                    volume_surge > 1.5 and      # 明显放量
                    momentum_metrics['trend_score'] >= 6):  # 趋势良好
                    
                    candidates.append({
                        '代码': code,
                        '名称': name,
                        '所属行业': stock['所属行业'],
                        '收盘价': latest['收盘'],
                        '涨跌幅': latest['涨跌幅'],
                        '总市值(亿)': stock['总市值'] / 100000000,
                        '换手率': stock['换手率'],
                        '成交额(万)': stock['成交额'] / 10000,
                        '动量评分': round(momentum_metrics['total_score'], 2),
                        '价格动量': round(momentum_metrics['price_momentum'], 2),
                        '成交量倍数': round(momentum_metrics['volume_momentum'], 2),
                        '突破强度': round(momentum_metrics['breakout_strength'], 2),
                        '趋势评分': momentum_metrics['trend_score'],
                        '连涨天数': momentum_metrics['consecutive_up_days'],
                        '相对位置': round(current_position * 100, 1),
                        '放量倍数': round(volume_surge, 2)
                    })
                
                time.sleep(0.05)  # 友好访问
                
            except Exception as e:
                continue
        
        # 5. 最终排序和筛选
        print("4/5 最终排序和筛选...")
        if not candidates:
            print("未发现符合条件的主涨段候选股票")
            return pd.DataFrame()
        
        result_df = pd.DataFrame(candidates)
        
        # 按动量评分排序，取前N只
        final_result = result_df.sort_values(
            by=['动量评分', '成交量倍数'], ascending=[False, False]
        ).head(max_candidates)
        
        print("5/5 完成筛选")
        print(f"最终筛选出 {len(final_result)} 只主涨段候选股票")
        
        return final_result
    
    def get_stable_main_wave_candidates(self, max_candidates: int = 40) -> pd.DataFrame:
        """
        稳健版主涨段候选池 - 专注于成熟股票的趋势启动
        
        筛选逻辑：
        1. 排除所有新股和次新股
        2. 寻找温和放量突破的成熟股票
        3. 重点关注基本面健康的中等市值股票
        4. 避开过度投机和炒作
        
        Returns:
            DataFrame: 稳健的主涨段候选池
        """
        print("=" * 60)
        print("执行稳健版主涨段候选池筛选")
        print("=" * 60)
        
        # 1. 基础筛选 - 非常严格的条件
        print("1/4 执行严格基础筛选...")
        all_stocks = ak.stock_zh_a_spot_em()
        
        # 严格的基础筛选
        stable_stocks = all_stocks[
            (~all_stocks['名称'].str.contains('ST|退|暂停|^[NC]')) &  # 排除ST和新股
            (~all_stocks['代码'].str.startswith('8')) &  # 排除北交所
            (~all_stocks['代码'].str.startswith('92')) &  # 排除北交所新股
            (all_stocks['总市值'] > 50 * 100000000) &  # 至少50亿市值
            (all_stocks['总市值'] < 500 * 100000000) &  # 不超过500亿（避免大盘股）
            (all_stocks['涨跌幅'] > -5.0) &  # 排除跌幅过大
            (all_stocks['涨跌幅'] < 8.0) &   # 排除涨幅过大（避免追高）
            (all_stocks['换手率'] > 2.0) &   # 有一定活跃度
            (all_stocks['换手率'] < 15.0) &  # 避免过度炒作
            (all_stocks['成交额'] > 200000000)  # 成交额至少2亿
        ].copy()
        
        print(f"基础筛选后股票数量: {len(stable_stocks)}")
        
        if len(stable_stocks) == 0:
            return pd.DataFrame()
        
        # 2. 历史数据验证和技术筛选
        print("2/4 历史数据验证...")
        candidates = []
        start_date = (self.today - timedelta(days=150)).strftime('%Y%m%d')
        end_date = self.today.strftime('%Y%m%d')
        
        # 限制分析数量，优先选择活跃度适中的股票
        if len(stable_stocks) > 100:
            stable_stocks = stable_stocks.sort_values(
                by=['成交额'], ascending=False
            ).head(100)
        
        total_stocks = len(stable_stocks)
        for idx, (_, stock) in enumerate(stable_stocks.iterrows()):
            if idx % 15 == 0:
                print(f"验证进度: {idx+1}/{total_stocks}")
            
            code = stock['代码']
            name = stock['名称']
            
            try:
                # 获取历史数据
                hist_df = ak.stock_zh_a_hist(
                    symbol=code, period="daily",
                    start_date=start_date, end_date=end_date, adjust="qfq"
                )
                
                # 严格的成熟度检查
                if len(hist_df) < 80:  # 至少80个交易日
                    continue
                
                # 检查上市时间（至少1年）
                first_date = pd.to_datetime(hist_df.iloc[0]['日期'])
                days_since_listing = (self.today - first_date).days
                if days_since_listing < 365:  # 上市不足1年
                    continue
                
                # 排除极端波动的股票（可能是炒作股）
                recent_60d = hist_df.tail(60)
                price_volatility = (recent_60d['最高'].max() / recent_60d['最低'].min() - 1) * 100
                if price_volatility > 80:  # 近60日波动率超过80%
                    continue
                
                # 3. 技术形态筛选
                latest = hist_df.iloc[-1]
                
                # 计算均线
                ma10 = hist_df['收盘'].tail(10).mean()
                ma20 = hist_df['收盘'].tail(20).mean()
                ma60 = hist_df['收盘'].tail(60).mean()
                
                # 趋势判断：温和上升趋势
                trend_healthy = (
                    latest['收盘'] > ma10 > ma20 and  # 短期趋势向上
                    ma20 > ma60 * 0.95  # 中期不能太弱
                )
                
                if not trend_healthy:
                    continue
                
                # 成交量分析：温和放量
                vol_ma10 = hist_df['成交量'].tail(10).mean()
                vol_ma30 = hist_df['成交量'].tail(30).mean()
                volume_ratio = vol_ma10 / vol_ma30 if vol_ma30 > 0 else 1
                
                # 寻找温和放量（不是暴涨暴跌）
                if not (1.2 <= volume_ratio <= 3.0):
                    continue
                
                # 价格位置分析
                high_60d = recent_60d['最高'].max()
                low_60d = recent_60d['最低'].min()
                current_position = (latest['收盘'] - low_60d) / (high_60d - low_60d)
                
                # 处于相对高位但非顶部（主升浪前期特征）
                if not (0.6 <= current_position <= 0.9):
                    continue
                
                # 计算综合评分
                momentum_score = (
                    latest['涨跌幅'] * 0.2 +  # 当日表现
                    (ma10/ma20 - 1) * 100 * 0.3 +  # 短期趋势强度
                    min(volume_ratio, 3.0) * 5 * 0.3 +  # 放量程度
                    current_position * 20 * 0.2  # 价格位置
                )
                
                if momentum_score < 5.0:  # 最低评分要求
                    continue
                
                candidates.append({
                    '代码': code,
                    '名称': name,
                    '所属行业': stock['所属行业'],
                    '收盘价': latest['收盘'],
                    '涨跌幅': latest['涨跌幅'],
                    '总市值(亿)': stock['总市值'] / 100000000,
                    '换手率': stock['换手率'],
                    '成交额(万)': stock['成交额'] / 10000,
                    '综合评分': round(momentum_score, 2),
                    '趋势强度': round((ma10/ma20 - 1) * 100, 2),
                    '放量倍数': round(volume_ratio, 2),
                    '价格位置': round(current_position * 100, 1),
                    '60日波动': round(price_volatility, 1),
                    '上市天数': days_since_listing
                })
                
                time.sleep(0.05)  # 友好访问
                
            except Exception as e:
                continue
        
        # 4. 最终筛选和排序
        print("3/4 最终筛选排序...")
        if not candidates:
            print("未发现符合稳健筛选条件的股票")
            return pd.DataFrame()
        
        result_df = pd.DataFrame(candidates)
        
        # 按综合评分排序
        final_result = result_df.sort_values(
            by=['综合评分', '趋势强度'], ascending=[False, False]
        ).head(max_candidates)
        
        print("4/4 筛选完成")
        print(f"稳健版筛选出 {len(final_result)} 只候选股票")
        
        return final_result
    
    def get_two_week_active_candidates(self, max_candidates: int = 30) -> pd.DataFrame:
        """
        获取近两周活跃的股票候选池
        
        通过分析近两周的交易数据，寻找：
        1. 持续活跃（多日放量）
        2. 趋势向上（累计涨幅为正）
        3. 非新股炒作
        4. 有主力资金介入迹象
        
        Returns:
            DataFrame: 近两周活跃股票候选池
        """
        print("=" * 60)
        print("获取近两周活跃股票候选池")
        print("=" * 60)
        
        # 1. 获取基础股票池
        print("1/4 获取基础股票池...")
        all_stocks = ak.stock_zh_a_spot_em()
        
        # 基础筛选 - 排除新股和问题股
        base_stocks = all_stocks[
            (~all_stocks['名称'].str.contains('ST|退|暂停|^[NC]')) &
            (~all_stocks['代码'].str.startswith('8')) &
            (~all_stocks['代码'].str.startswith('92')) &
            (all_stocks['总市值'] > 30 * 100000000) &
            (all_stocks['总市值'] < 1000 * 100000000) &
            (all_stocks['成交额'] > 50000000)  # 今日至少有5000万成交额
        ].copy()
        
        print(f"基础股票池: {len(base_stocks)}只")
        
        # 2. 分析近两周数据
        print("2/4 分析近两周交易数据...")
        candidates = []
        
        # 计算日期范围
        end_date = self.today.strftime('%Y%m%d')
        start_date = self.two_weeks_ago.strftime('%Y%m%d')
        
        # 限制分析数量（按成交额排序，优先分析活跃的）
        if len(base_stocks) > 200:
            base_stocks = base_stocks.sort_values(by='成交额', ascending=False).head(200)
        
        total_stocks = len(base_stocks)
        for idx, (_, stock) in enumerate(base_stocks.iterrows()):
            if idx % 25 == 0:
                print(f"分析进度: {idx+1}/{total_stocks}")
            
            code = stock['代码']
            name = stock['名称']
            
            try:
                # 获取近两周历史数据
                hist_df = ak.stock_zh_a_hist(
                    symbol=code, period="daily",
                    start_date=start_date, end_date=end_date, adjust="qfq"
                )
                
                if len(hist_df) < 8:  # 至少需要8个交易日数据
                    continue
                
                # 检查上市时间（排除新股）
                try:
                    first_date = pd.to_datetime(hist_df.iloc[0]['日期'])
                    days_since_listing = (self.today - first_date).days
                    if days_since_listing < 180:  # 上市不足6个月
                        continue
                except:
                    continue
                
                # 3. 计算近两周活跃度指标
                latest = hist_df.iloc[-1]
                first = hist_df.iloc[0]
                
                # 累计涨跌幅
                total_return = (latest['收盘'] / first['收盘'] - 1) * 100
                
                # 平均日换手率
                avg_turnover = stock['换手率']  # 当日换手率作为参考
                
                # 成交额稳定性（近两周平均成交额）
                avg_amount = hist_df['成交额'].mean()
                
                # 活跃天数（成交额超过平均值的天数）
                active_days = len(hist_df[hist_df['成交额'] > avg_amount * 0.8])
                
                # 价格稳定性（避免过度波动的炒作股）
                price_volatility = (hist_df['最高'].max() / hist_df['最低'].min() - 1) * 100
                
                # 近期放量情况
                recent_3days_vol = hist_df['成交量'].tail(3).mean()
                early_period_vol = hist_df['成交量'].head(5).mean()
                volume_growth = (recent_3days_vol / early_period_vol - 1) * 100 if early_period_vol > 0 else 0
                
                # 4. 筛选条件
                conditions = (
                    total_return > -10 and total_return < 50 and  # 两周涨跌幅合理
                    avg_turnover > 2.0 and avg_turnover < 25.0 and  # 换手率适中
                    avg_amount > 100000000 and  # 平均成交额超过1亿
                    active_days >= 6 and  # 至少6天活跃
                    price_volatility < 100 and  # 波动率不超过100%
                    volume_growth > -30  # 成交量不能大幅萎缩
                )
                
                if not conditions:
                    continue
                
                # 计算综合活跃度评分
                activity_score = (
                    min(max(total_return, 0), 20) * 0.25 +  # 正收益加分
                    min(avg_turnover, 15) * 0.2 +  # 换手率
                    (avg_amount / 100000000) * 0.2 +  # 成交额（亿为单位）
                    active_days * 0.15 +  # 活跃天数
                    max(volume_growth, 0) * 0.1 +  # 放量情况
                    (100 - price_volatility) * 0.1  # 稳定性
                )
                
                if activity_score < 8.0:  # 最低评分要求
                    continue
                
                candidates.append({
                    '代码': code,
                    '名称': name,
                    '最新价': latest['收盘'],
                    '两周涨跌幅': round(total_return, 2),
                    '平均换手率': avg_turnover,
                    '平均成交额(万)': round(avg_amount / 10000, 0),
                    '活跃天数': active_days,
                    '总交易日': len(hist_df),
                    '价格波动率': round(price_volatility, 1),
                    '成交量增长': round(volume_growth, 1),
                    '活跃度评分': round(activity_score, 2),
                    '总市值(亿)': round(stock['总市值'] / 100000000, 1),
                    '60日涨跌幅': stock.get('60日涨跌幅', 0),
                    '年初至今涨跌幅': stock.get('年初至今涨跌幅', 0)
                })
                
                time.sleep(0.05)  # 友好访问
                
            except Exception as e:
                continue
        
        # 4. 排序和筛选
        print("3/4 排序筛选结果...")
        if not candidates:
            print("未发现符合近两周活跃条件的股票")
            return pd.DataFrame()
        
        result_df = pd.DataFrame(candidates)
        
        # 按活跃度评分和两周收益排序
        final_result = result_df.sort_values(
            by=['活跃度评分', '两周涨跌幅'], ascending=[False, False]
        ).head(max_candidates)
        
        print("4/4 筛选完成")
        print(f"近两周活跃股票筛选出 {len(final_result)} 只候选股票")
        
        return final_result
    
    def save_to_csv(self, df: pd.DataFrame, filename: str = "tickers_cn.csv") -> str:
        """
        保存股票列表到CSV文件
        
        Args:
            df: 股票数据DataFrame
            filename: 文件名
            
        Returns:
            str: 保存的文件路径
        """
        if df.empty:
            print("没有数据可保存")
            return ""
        
        # 确保包含必要的列
        required_columns = ['代码', '名称']
        if not all(col in df.columns for col in required_columns):
            print("数据格式不正确，缺少必要列")
            return ""
        
        # 添加时间戳
        timestamp = self.today.strftime('%Y%m%d_%H%M%S')
        timestamped_filename = filename.replace('.csv', f'_{timestamp}.csv')
        
        try:
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            df.to_csv(timestamped_filename, index=False, encoding='utf-8-sig')
            
            print(f"已保存到: {filename}")
            print(f"备份文件: {timestamped_filename}")
            return filename
            
        except Exception as e:
            print(f"保存文件失败: {e}")
            return ""


def main():
    """主函数 - 演示用法"""
    ticker_getter = CNStockTickers()
    
    print("选择获取方式:")
    print("1. 龙虎榜股票")
    print("2. 热门人气榜") 
    print("3. 高换手率股票")
    print("4. 四维筛选候选池")
    print("5. 获取所有方法的结果")
    print("6. 优化版主涨段候选池")
    print("7. 稳健版主涨段候选池")
    print("8. 【推荐】近两周活跃股票候选池")
    print("9. 生成tickers_cn.csv文件")
    
    choice = input("请输入选择 (1-9): ").strip()
    
    if choice == '1':
        result = ticker_getter.get_lhb_tickers()
        print("\n龙虎榜股票:")
        print(result)
        
    elif choice == '2':
        result = ticker_getter.get_hot_rank_tickers()
        print("\n热门股票:")
        print(result)
        
    elif choice == '3':
        result = ticker_getter.get_active_by_turnover()
        print("\n高换手率股票:")
        print(result)
        
    elif choice == '4':
        result = ticker_getter.get_candidate_pool_advanced(max_stocks_to_analyze=50)
        print("\n四维筛选候选池:")
        print(result)
        
    elif choice == '5':
        results = ticker_getter.get_all_active_tickers()
        
        # 显示合并结果
        if '合并去重' in results and not results['合并去重'].empty:
            print("\n合并去重后的活跃股票列表:")
            print(results['合并去重'])
            
    elif choice == '6':
        result = ticker_getter.get_optimized_main_wave_candidates()
        print("\n优化版主涨段候选池:")
        print(result)
        
        # 询问是否保存
        if not result.empty:
            save_choice = input("\n是否保存到tickers_cn.csv? (y/n): ").strip().lower()
            if save_choice == 'y':
                ticker_getter.save_to_csv(result)
                
    elif choice == '7':
        result = ticker_getter.get_stable_main_wave_candidates()
        print("\n稳健版主涨段候选池:")
        print(result)
        
        # 询问是否保存
        if not result.empty:
            save_choice = input("\n是否保存到tickers_cn.csv? (y/n): ").strip().lower()
            if save_choice == 'y':
                ticker_getter.save_to_csv(result)
                
    elif choice == '8':
        result = ticker_getter.get_two_week_active_candidates()
        print("\n近两周活跃股票候选池:")
        print(result)
        
        # 询问是否保存
        if not result.empty:
            save_choice = input("\n是否保存到tickers_cn.csv? (y/n): ").strip().lower()
            if save_choice == 'y':
                ticker_getter.save_to_csv(result)
                
    elif choice == '9':
        print("正在生成tickers_cn.csv文件（近两周活跃数据）...")
        result = ticker_getter.get_two_week_active_candidates(max_candidates=25)
        if not result.empty:
            ticker_getter.save_to_csv(result)
        else:
            print("未找到符合条件的股票")
        
    else:
        print("无效选择")


def generate_tickers_csv():
    """
    专门用于生成tickers_cn.csv的函数 - 近两周活跃数据
    """
    ticker_getter = CNStockTickers()
    
    print("正在执行近两周活跃股票筛选并生成tickers_cn.csv...")
    result = ticker_getter.get_two_week_active_candidates(max_candidates=25)
    
    if not result.empty:
        ticker_getter.save_to_csv(result)
        print("\n筛选结果预览:")
        print(result[['代码', '名称', '两周涨跌幅', '活跃度评分', '活跃天数']].head(10))
        
        # 提供投资建议
        print("\n=== A股专家投资建议 ===")
        print("1. 以上股票是近两周持续活跃的成熟股票")
        print("2. 避开了新股炒作，重点关注基本面健康股票")
        print("3. 活跃度评分>12的股票优先考虑")
        print("4. 两周涨跌幅显示持续资金关注度")
        print("5. 建议分批建仓，单只仓位控制在3%以内")
        
    else:
        print("当前市场条件下暂无符合近两周活跃特征的股票")
        
    return result


if __name__ == "__main__":
    main()