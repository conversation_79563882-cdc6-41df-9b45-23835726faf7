#!/usr/bin/env python3
"""
A股潜力发现系统 V4.0 - 面向未来主涨段的候选池构建
专注于发现具备未来主涨段潜力的股票，而非追涨已涨股票

V4.0 核心理念转变：
- 从"追涨"转向"挖潜力"：寻找还未大涨但具备上涨潜力的股票
- 从"精选少量"转向"宽松筛选"：宁可多选不可漏选，建立充足候选池
- 从"当前表现"转向"未来可能"：关注蓄势待发而非已经爆发的股票
- 从"严格条件"转向"包容性筛选"：给更多股票进入候选池的机会

筛选哲学：
1. 基础健康 - 排除明显问题股，但不过度严格
2. 适度活跃 - 近期有资金关注迹象，但不追逐高热度
3. 价值区间 - 在合理估值区间，避免泡沫化标的
4. 技术蓄势 - 处于相对低位或整理状态，有上涨空间
5. 多元包容 - 不同类型的潜力股都给予机会

四类潜力股分类：
A类：技术形态优秀，处于突破前夜
B类：基本面改善，业绩拐点可期
C类：估值修复型，价格相对低估
D类：政策受益型，行业风口股票
"""

import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
import time
import warnings
warnings.filterwarnings('ignore')


class PotentialDiscoverySystemV4:
    """潜力发现系统V4.0"""
    
    def __init__(self):
        self.today = datetime.now()
        self.two_weeks_ago = self.today - timedelta(days=14)
        
        # V4.0 宽松筛选参数 - 专注潜力发现
        self.config = {
            # 基础健康筛选（宽松策略）
            'min_market_cap': 15 * 100000000,        # 最小市值15亿（更宽松）
            'max_market_cap': 1500 * 100000000,      # 最大市值1500亿（包含大盘股）
            'min_listing_days': 90,                  # 最少上市90天（降低门槛）
            'exclude_st': True,                      # 排除ST股
            
            # 活跃度筛选（温和要求）
            'min_avg_amount': 20000000,              # 近两周日均成交额2000万（大幅降低）
            'min_active_days': 4,                    # 近两周至少4天活跃（更宽松）
            'min_turnover': 0.5,                     # 最低换手率0.5%
            'max_turnover': 50.0,                    # 最高换手率50%（允许更高活跃度）
            
            # 潜力区间控制（关键：寻找未来机会）
            'max_two_week_gain': 40.0,               # 近两周涨幅不超过40%（允许适度上涨）
            'min_two_week_gain': -25.0,              # 近两周跌幅不超过25%（包含调整股）
            'max_60d_gain': 80.0,                    # 60日涨幅不超过80%（避免已涨过多）
            'min_60d_gain': -40.0,                   # 60日跌幅不超过40%（包含超跌股）
            
            # 价格位置（寻找蓄势股票）
            'min_position_60d': 0.2,                 # 60日内价格位置至少20%
            'max_position_60d': 0.85,                # 60日内价格位置不超过85%
            'max_volatility': 200.0,                 # 最大波动率200%（允许更高波动）
            
            # 成交量健康度
            'min_volume_growth': -50.0,              # 允许成交量萎缩50%
            'max_volume_growth': 1000.0,             # 允许成交量大幅放大
            
            # 输出控制
            'max_candidates': 500,                   # 最大候选数量500只
            'request_delay': 0.03,                   # 请求延迟
        }
    
    def get_basic_pool(self):
        """获取基础股票池"""
        print("📊 正在获取A股基础数据...")
        all_stocks = ak.stock_zh_a_spot_em()
        print(f"🔍 A股总数: {len(all_stocks)} 只")
        
        # 基础健康筛选（非常宽松）
        basic_pool = all_stocks[
            # 排除明显问题股
            (~all_stocks['名称'].str.contains('ST|退|暂停')) &
            (~all_stocks['代码'].str.startswith('8')) &
            (~all_stocks['代码'].str.startswith('92')) &
            
            # 基本市值和流动性要求
            (all_stocks['总市值'] >= self.config['min_market_cap']) &
            (all_stocks['总市值'] <= self.config['max_market_cap']) &
            (all_stocks['成交额'] >= self.config['min_avg_amount'] / 2) &  # 当日成交额要求更低
            (all_stocks['换手率'] >= self.config['min_turnover']) &
            
            # 当日表现（不追涨杀跌，但允许较大区间）
            (all_stocks['涨跌幅'] >= -9.0) &
            (all_stocks['涨跌幅'] <= 15.0)
        ].copy()
        
        print(f"✅ 基础筛选后: {len(basic_pool)} 只")
        return basic_pool
    
    def analyze_potential(self, stock_data):
        """分析股票潜力"""
        code = stock_data['代码']
        name = stock_data['名称']
        
        try:
            # 获取历史数据
            start_date = (self.today - timedelta(days=120)).strftime('%Y%m%d')
            end_date = self.today.strftime('%Y%m%d')
            
            hist_df = ak.stock_zh_a_hist(
                symbol=code, period="daily",
                start_date=start_date, end_date=end_date, adjust="qfq"
            )
            
            if len(hist_df) < 30:  # 至少30个交易日
                return None
            
            # 检查上市时间（宽松要求）
            first_date = pd.to_datetime(hist_df.iloc[0]['日期'])
            days_since_listing = (self.today - first_date).days
            if days_since_listing < self.config['min_listing_days']:
                return None
            
            # 计算各种指标
            latest = hist_df.iloc[-1]
            
            # 近两周数据
            two_week_data = hist_df.tail(10)  # 大约两周
            if len(two_week_data) < 5:
                return None
                
            first_two_week = two_week_data.iloc[0]
            two_week_return = (latest['收盘'] / first_two_week['收盘'] - 1) * 100
            
            # 60日数据
            if len(hist_df) >= 60:
                sixty_day_data = hist_df.tail(60)
                first_60d = sixty_day_data.iloc[0]
                sixty_day_return = (latest['收盘'] / first_60d['收盘'] - 1) * 100
            else:
                sixty_day_return = two_week_return
            
            # 年初至今数据
            year_start = datetime(self.today.year, 1, 1)
            year_data = hist_df[pd.to_datetime(hist_df['日期']) >= year_start]
            if len(year_data) > 0:
                ytd_return = (latest['收盘'] / year_data.iloc[0]['收盘'] - 1) * 100
            else:
                ytd_return = sixty_day_return
            
            # 检查涨跌幅是否在潜力区间
            if (two_week_return > self.config['max_two_week_gain'] or 
                two_week_return < self.config['min_two_week_gain']):
                return None
                
            if (sixty_day_return > self.config['max_60d_gain'] or 
                sixty_day_return < self.config['min_60d_gain']):
                return None
            
            # 成交量分析
            avg_amount = two_week_data['成交额'].mean()
            if avg_amount < self.config['min_avg_amount']:
                return None
            
            # 活跃天数
            active_days = len(two_week_data[two_week_data['成交额'] > avg_amount * 0.7])
            if active_days < self.config['min_active_days']:
                return None
            
            # 价格位置分析
            high_60d = hist_df['最高'].tail(60).max()
            low_60d = hist_df['最低'].tail(60).min()
            if high_60d == low_60d:
                current_position = 0.5
            else:
                current_position = (latest['收盘'] - low_60d) / (high_60d - low_60d)
            
            if (current_position < self.config['min_position_60d'] or 
                current_position > self.config['max_position_60d']):
                return None
            
            # 成交量变化
            early_vol = two_week_data['成交量'].head(5).mean()
            recent_vol = two_week_data['成交量'].tail(5).mean()
            volume_growth = (recent_vol / early_vol - 1) * 100 if early_vol > 0 else 0
            
            # 计算潜力评分（关注未来潜力）
            potential_score = self.calculate_potential_score(
                current_position, two_week_return, sixty_day_return, 
                volume_growth, active_days, avg_amount, days_since_listing
            )
            
            # 分类评估
            stock_category = self.classify_stock(
                current_position, two_week_return, sixty_day_return, 
                volume_growth, stock_data['换手率']
            )
            
            return {
                '代码': code,
                '名称': name,
                '最新价': latest['收盘'],
                '涨跌幅': latest['涨跌幅'],
                '换手率': stock_data['换手率'],
                '60日涨跌幅': round(sixty_day_return, 2),
                '年初至今涨跌幅': round(ytd_return, 2),
                '潜力评分': round(potential_score, 2),
                '成交额(万)': round(avg_amount / 10000, 0),
                '总市值(亿)': round(stock_data['总市值'] / 100000000, 1),
                '60日位置': round(current_position * 100, 1),
                '活跃天数': active_days,
                '成交量增长': round(volume_growth, 1),
                '股票分类': stock_category,
                '上市天数': days_since_listing
            }
            
        except Exception as e:
            return None
    
    def calculate_potential_score(self, position, two_week_return, sixty_day_return, 
                                 volume_growth, active_days, avg_amount, listing_days):
        """计算潜力评分 - 重点关注未来潜力而非历史表现"""
        score = 0
        
        # 1. 位置评分：中低位置加分，过高位置减分
        if position < 0.4:
            score += 15  # 低位大幅加分
        elif position < 0.6:
            score += 10  # 中位适度加分
        elif position < 0.8:
            score += 5   # 中高位小幅加分
        else:
            score -= 5   # 高位减分
        
        # 2. 涨幅适中性：不要涨太多，也不要跌太多
        abs_two_week = abs(two_week_return)
        if abs_two_week < 10:
            score += 10  # 近期表现平稳
        elif abs_two_week < 20:
            score += 5   # 近期波动适中
        else:
            score -= 5   # 近期波动过大
        
        # 3. 活跃度评分：有关注但不过热
        score += min(active_days * 1.5, 12)
        score += min(avg_amount / 50000000 * 3, 10)
        
        # 4. 成交量健康度：温和放量加分
        if 0 < volume_growth < 100:
            score += volume_growth * 0.1
        elif volume_growth >= 100:
            score += 10  # 大幅放量适度加分
        
        # 5. 上市时间加成：避免新股炒作
        if listing_days > 365:
            score += 5
        elif listing_days > 180:
            score += 3
        
        # 6. 60日表现调整：适度下跌的给机会
        if -20 < sixty_day_return < 0:
            score += 8   # 适度调整股票加分
        elif 0 <= sixty_day_return < 30:
            score += 5   # 温和上涨股票加分
        elif sixty_day_return >= 50:
            score -= 10  # 已大涨股票减分
        
        return max(score, 0)
    
    def classify_stock(self, position, two_week_return, sixty_day_return, volume_growth, turnover):
        """股票分类评估"""
        # A类：技术形态优秀
        if (0.3 <= position <= 0.7 and 
            abs(two_week_return) < 15 and 
            volume_growth > 0):
            return 'A-技术蓄势'
        
        # B类：基本面改善（成交量显著放大）
        elif volume_growth > 50 and turnover > 3:
            return 'B-资金介入'
        
        # C类：估值修复（价格相对低位）
        elif position < 0.4 and sixty_day_return < 0:
            return 'C-估值修复'
        
        # D类：其他潜力股
        else:
            return 'D-其他潜力'
    
    def run_screening(self):
        """运行潜力发现筛选"""
        print("🎯 A股潜力发现系统 V4.0")
        print("=== 面向未来主涨段的候选池构建 ===")
        print("💡 核心理念：挖潜力，不追涨")
        print("🎲 筛选策略：宽松包容，建立充足候选池")
        print("=" * 60)
        
        # 1. 获取基础池
        basic_pool = self.get_basic_pool()
        
        if len(basic_pool) == 0:
            print("❌ 基础筛选无结果")
            return pd.DataFrame()
        
        # 2. 按总市值排序，优先分析各市值段股票
        basic_pool = basic_pool.sort_values(by='总市值', ascending=False)
        
        # 3. 限制分析数量，优先活跃股票
        if len(basic_pool) > 300:
            # 按成交额排序，优先分析活跃股票
            basic_pool = basic_pool.sort_values(by='成交额', ascending=False).head(300)
            print(f"📊 优先分析前300只活跃股票")
        
        # 4. 逐个分析潜力
        print(f"🔬 开始潜力分析（预计需要{len(basic_pool) * 0.08 / 60:.1f}分钟）...")
        
        candidates = []
        total = len(basic_pool)
        
        for idx, (_, stock) in enumerate(basic_pool.iterrows()):
            if idx % 50 == 0:
                progress = (idx + 1) / total * 100
                print(f"📊 分析进度: {idx+1}/{total} ({progress:.1f}%) - {stock['名称']}")
            
            result = self.analyze_potential(stock)
            if result:
                candidates.append(result)
                if len(candidates) % 20 == 0:
                    print(f"🌟 已发现 {len(candidates)} 只潜力股")
            
            time.sleep(self.config['request_delay'])
        
        # 5. 结果处理
        if not candidates:
            print("❌ 未发现符合条件的潜力股")
            return pd.DataFrame()
        
        result_df = pd.DataFrame(candidates)
        
        # 按潜力评分排序，保留更多候选
        final_result = result_df.sort_values(
            by=['潜力评分', '活跃天数'], ascending=[False, False]
        ).head(self.config['max_candidates'])
        
        print(f"✅ 潜力发现完成，构建候选池 {len(final_result)} 只股票")
        
        # 6. 保存结果
        timestamp = self.today.strftime('%Y%m%d_%H%M%S')
        filename = f"potential_discovery_v4_{timestamp}.csv"
        final_result.to_csv(filename, index=False, encoding='utf-8-sig')
        
        print(f"💾 结果已保存到: {filename}")
        
        return final_result
    
    def analyze_results(self, result_df):
        """分析筛选结果"""
        if result_df.empty:
            return
        
        print("\n" + "=" * 60)
        print("📊 潜力发现系统分析报告")
        print("=" * 60)
        
        print(f"🎯 候选池规模: {len(result_df)} 只")
        print(f"⭐ 平均潜力评分: {result_df['潜力评分'].mean():.2f}")
        print(f"💰 平均市值: {result_df['总市值(亿)'].mean():.1f} 亿元")
        print(f"📍 平均60日位置: {result_df['60日位置'].mean():.1f}%")
        print(f"📈 平均60日涨跌幅: {result_df['60日涨跌幅'].mean():.2f}%")
        print(f"🎊 平均年初至今涨幅: {result_df['年初至今涨跌幅'].mean():.2f}%")
        
        # 分类分布
        if '股票分类' in result_df.columns:
            category_dist = result_df['股票分类'].value_counts()
            print(f"\n📊 股票分类分布:")
            for category, count in category_dist.items():
                percentage = count / len(result_df) * 100
                print(f"   {category}: {count} 只 ({percentage:.1f}%)")
        
        # 位置分布
        low_pos = len(result_df[result_df['60日位置'] < 40])
        mid_pos = len(result_df[(result_df['60日位置'] >= 40) & (result_df['60日位置'] < 70)])
        high_pos = len(result_df[result_df['60日位置'] >= 70])
        
        print(f"\n📍 价格位置分布:")
        print(f"   低位蓄势 (<40%): {low_pos} 只")
        print(f"   中位整理 (40-70%): {mid_pos} 只")
        print(f"   相对高位 (>70%): {high_pos} 只")
        
        # 投资策略
        print(f"\n💡 潜力股投资策略建议:")
        print(f"   1. 重点关注A类技术蓄势股票（突破概率高）")
        print(f"   2. 关注C类估值修复股票（安全边际高）") 
        print(f"   3. 潜力评分 > {result_df['潜力评分'].quantile(0.6):.1f} 优先考虑")
        print(f"   4. 低位股票 (<40%) 重点布局")
        print(f"   5. 分散投资，单只仓位 0.2-1%")
        print(f"   6. 设定止损 -12%，给予足够时间等待")
        
        print(f"\n🚀 主涨段预期:")
        print(f"   - 候选池较大，预期30-50%股票可能启动")
        print(f"   - 关注技术突破、放量、政策催化等信号")
        print(f"   - 预计1-3个月内陆续有股票进入主升浪")
        print(f"   - 建议动态调整，定期更新候选池")


def main():
    """主函数"""
    print("🌟 A股潜力发现系统 V4.0")
    print("📅 运行时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("🎯 目标：构建面向未来主涨段的候选池")
    
    # 创建筛选器
    system = PotentialDiscoverySystemV4()
    
    # 运行筛选
    result_df = system.run_screening()
    
    # 显示和分析结果
    if not result_df.empty:
        print("\n📋 潜力候选池预览:")
        display_columns = ['代码', '名称', '潜力评分', '60日位置', '股票分类', '60日涨跌幅']
        available_columns = [col for col in display_columns if col in result_df.columns]
        print(result_df[available_columns].head(30))
        
        # 分析结果
        system.analyze_results(result_df)
    
    print("\n✅ 潜力发现系统运行完成！")


if __name__ == "__main__":
    main()