import pandas as pd
import yfinance as yf
from concurrent.futures import ThreadPoolExecutor

# 从维基百科获取 S&P 500 成分股列表
def get_sp500_tickers():
    url = 'https://en.wikipedia.org/wiki/List_of_S%26P_500_companies'
    tables = pd.read_html(url)
    sp500_table = tables[0]
    return sp500_table['Symbol'].tolist()

# 从维基百科获取 Nasdaq 100 成分股列表
def get_nasdaq100_tickers():
    url = 'https://en.wikipedia.org/wiki/NASDAQ-100'
    tables = pd.read_html(url)
    
    for table in tables:
        if 'Ticker' in table.columns:
            return table['Ticker'].tolist()
        elif 'Symbol' in table.columns:
            return table['Symbol'].tolist()
    
    raise ValueError("Could not find a table with 'Ticker' or 'Symbol' columns")

# 定义获取股票信息的函数，包括名称和市值
def get_stock_info(ticker):
    try:
        stock = yf.Ticker(ticker)
        info = stock.info
        name = info.get('longName')
        market_cap = info.get('marketCap')
        if name is not None and market_cap is not None:
            return {'Ticker': ticker, 'Name': name, 'Market Cap': market_cap}
    except Exception:
        return None

def main():
    # 获取 S&P 500 的股票代码
    print("Fetching S&P 500 tickers...")
    sp500_tickers = get_sp500_tickers()
    
    # 获取 Nasdaq 100 的股票代码
    print("Fetching Nasdaq 100 tickers...")
    nasdaq100_tickers = get_nasdaq100_tickers()
    
    # 合并两个列表并去重
    all_tickers = list(set(sp500_tickers + nasdaq100_tickers))
    
    # 替换股票代码中的 '.' 为 '-'，与 Yahoo Finance 的格式匹配
    all_tickers = [ticker.replace('.', '-') for ticker in all_tickers]
    
    print(f"Total unique tickers: {len(all_tickers)}")
    print("Fetching stock information...")
    
    # 使用 ThreadPoolExecutor 并行获取股票信息
    with ThreadPoolExecutor(max_workers=10) as executor:
        results = list(executor.map(get_stock_info, all_tickers))
    
    # 过滤掉没有名称或市值数据的公司
    results = [result for result in results if result is not None]
    
    print(f"Successfully fetched info for {len(results)} stocks")
    
    # 将结果转换为 DataFrame
    tickers_df = pd.DataFrame(results)
    
    # 根据市值排序
    tickers_df = tickers_df.sort_values(by='Market Cap', ascending=False).reset_index(drop=True)
    
    # 将结果存储到本地的 CSV 文件，只保留 Ticker 和 Name 列
    tickers_df[['Ticker', 'Name']].to_csv("tickers_us.csv", index=False)
    print("Combined S&P 500 and Nasdaq 100 tickers saved to tickers_us.csv")
    
    # 显示前几行作为预览
    print("\nFirst 10 rows of the generated CSV:")
    print(tickers_df[['Ticker', 'Name']].head(10))

if __name__ == "__main__":
    main()