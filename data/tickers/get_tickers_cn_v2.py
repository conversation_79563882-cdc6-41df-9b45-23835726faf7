#!/usr/bin/env python3
"""
A股四维筛选模型 V2.0 - 优化版主涨段候选池筛选
基于akshare的量化选股策略实现

四维筛选逻辑：
1. 基础健康 (Healthy Basics) - 排除ST股、新股、小市值股
2. 趋势向上 (Upward Trend) - 均线多头排列，趋势确认
3. 成交量放大 (Volume Spike) - 显著放量，资金介入
4. 股价突破 (Price Breakout) - 突破关键阻力位
5. 热点题材 (Hot Sectors) - 属于市场热门板块

V2.0 改进：
- 增强新股过滤逻辑
- 优化参数设置，提高实用性
- 增加进度显示和错误处理
- 自动保存结果到CSV
- 增加投资建议和风险提示
"""

import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
import time
import warnings
warnings.filterwarnings('ignore')


class StockSelectorV2:
    """A股四维筛选模型V2.0"""
    
    def __init__(self):
        self.today = datetime.now()
        
        # V2.0 优化参数设置
        self.config = {
            # 基础健康筛选参数
            'min_market_cap': 30 * 100000000,      # 最小市值30亿（降低门槛）
            'max_market_cap': 800 * 100000000,     # 最大市值800亿（避免大盘股）
            'min_listing_days': 180,               # 最少上市180天（排除新股）
            
            # 趋势向上筛选参数
            'ma_short': 20,                        # 短期均线（调整为20日）
            'ma_long': 60,                         # 长期均线（调整为60日）
            
            # 成交量放大筛选参数
            'volume_avg_days': 20,                 # 计算平均成交量天数
            'volume_multi': 1.5,                   # 成交量放大倍数（降低要求）
            'min_amount': 100000000,               # 最小成交额1亿
            
            # 股价突破筛选参数
            'breakout_days': 30,                   # 突破30日新高（缩短周期）
            
            # 热点题材筛选参数
            'hot_sector_count': 8,                 # 热门板块数量（增加到8个）
            'min_sector_gain': 0.5,                # 板块最低涨幅0.5%
            
            # 其他参数
            'max_daily_gain': 18.0,               # 最大日涨幅（避免追高）
            'max_volatility': 150.0,              # 最大波动率（避免炒作股）
            'request_delay': 0.1,                 # 请求延迟（友好访问）
        }
    
    def get_hot_sectors(self):
        """获取市场热点板块"""
        print("🔍 正在获取市场热点板块...")
        try:
            hot_sectors_df = ak.stock_board_industry_spot_em()
            
            # V2.0 改进：筛选涨幅为正且成交额足够的板块
            hot_sectors = hot_sectors_df[
                (hot_sectors_df['涨跌幅'] >= self.config['min_sector_gain']) &
                (hot_sectors_df['成交额'] > 50000000)  # 成交额5000万以上
            ].sort_values(by='涨跌幅', ascending=False).head(self.config['hot_sector_count'])
            
            hot_sectors_list = hot_sectors['板块名称'].tolist()
            
            print(f"✅ 当前热点板块({len(hot_sectors_list)}个):")
            for i, sector in enumerate(hot_sectors_list, 1):
                gain = hot_sectors[hot_sectors['板块名称'] == sector]['涨跌幅'].iloc[0]
                print(f"   {i}. {sector} (+{gain:.2f}%)")
                
            return hot_sectors_list
            
        except Exception as e:
            print(f"❌ 获取热点板块失败: {e}")
            return []
    
    def basic_screening(self, all_stocks_df, hot_sectors_list):
        """基础健康筛选"""
        print("🏥 正在进行基础健康筛选...")
        
        # V2.0 增强版基础筛选
        filtered_stocks = all_stocks_df[
            # 排除问题股
            (~all_stocks_df['名称'].str.contains('ST|退|暂停|^[NC]')) &
            (~all_stocks_df['代码'].str.startswith('8')) &   # 排除北交所
            (~all_stocks_df['代码'].str.startswith('92')) &  # 排除北交所新股
            
            # 市值筛选
            (all_stocks_df['总市值'] >= self.config['min_market_cap']) &
            (all_stocks_df['总市值'] <= self.config['max_market_cap']) &
            
            # 流动性筛选
            (all_stocks_df['成交额'] >= self.config['min_amount']) &
            (all_stocks_df['换手率'] > 1.0) &
            
            # 涨跌幅筛选（避免追高和杀跌）
            (all_stocks_df['涨跌幅'] > -8.0) &
            (all_stocks_df['涨跌幅'] < self.config['max_daily_gain'])
        ].copy()
        
        print(f"📊 基础筛选后剩余股票数量: {len(filtered_stocks)}")
        
        # 如果有热点板块，优先筛选热点板块股票
        if hot_sectors_list:
            # 检查是否有'所属行业'列
            if '所属行业' in filtered_stocks.columns:
                hot_stocks = filtered_stocks[
                    filtered_stocks['所属行业'].isin(hot_sectors_list)
                ]
                if len(hot_stocks) > 50:  # 如果热点股票足够多，优先使用
                    filtered_stocks = hot_stocks
                    print(f"🔥 热点板块筛选后股票数量: {len(filtered_stocks)}")
        
        return filtered_stocks
    
    def technical_analysis(self, stock_data):
        """技术分析 - 四维筛选核心逻辑"""
        code = stock_data['代码']
        name = stock_data['名称']
        
        try:
            # 获取历史数据
            start_date = (self.today - timedelta(days=250)).strftime('%Y%m%d')
            end_date = self.today.strftime('%Y%m%d')
            
            hist_df = ak.stock_zh_a_hist(
                symbol=code, period="daily", 
                start_date=start_date, end_date=end_date, adjust="qfq"
            )
            
            if len(hist_df) < self.config['ma_long'] + 10:
                return None
            
            # V2.0 新增：检查上市时间（严格排除新股）
            first_date = pd.to_datetime(hist_df.iloc[0]['日期'])
            days_since_listing = (self.today - first_date).days
            if days_since_listing < self.config['min_listing_days']:
                return None
            
            # V2.0 新增：检查价格波动率（避免炒作股）
            price_volatility = (hist_df['最高'].max() / hist_df['最低'].min() - 1) * 100
            if price_volatility > self.config['max_volatility']:
                return None
            
            # 计算技术指标
            hist_df[f'MA{self.config["ma_short"]}'] = hist_df['收盘'].rolling(window=self.config['ma_short']).mean()
            hist_df[f'MA{self.config["ma_long"]}'] = hist_df['收盘'].rolling(window=self.config['ma_long']).mean()
            hist_df[f'Volume_MA{self.config["volume_avg_days"]}'] = hist_df['成交量'].rolling(window=self.config['volume_avg_days']).mean()
            
            latest_data = hist_df.iloc[-1]
            
            # 1. 趋势向上筛选
            is_upward_trend = (
                latest_data['收盘'] > latest_data[f'MA{self.config["ma_short"]}'] and
                latest_data[f'MA{self.config["ma_short"]}'] > latest_data[f'MA{self.config["ma_long"]}']
            )
            
            if not is_upward_trend:
                return None
            
            # 2. 成交量显著放大筛选
            if len(hist_df) >= 2:
                avg_volume = hist_df.iloc[-2][f'Volume_MA{self.config["volume_avg_days"]}']
                latest_volume = latest_data['成交量']
                is_volume_spike = latest_volume > avg_volume * self.config['volume_multi']
            else:
                is_volume_spike = False
            
            if not is_volume_spike:
                return None
            
            # 3. 股价突破关键位置筛选
            high_in_period = hist_df['最高'].rolling(window=self.config['breakout_days']).max().shift(1).iloc[-1]
            is_breakout = latest_data['收盘'] > high_in_period
            
            if not is_breakout:
                return None
            
            # V2.0 新增：计算综合评分
            trend_strength = (latest_data[f'MA{self.config["ma_short"]}'] / latest_data[f'MA{self.config["ma_long"]}'] - 1) * 100
            volume_ratio = latest_volume / avg_volume if avg_volume > 0 else 1
            breakout_strength = (latest_data['收盘'] / high_in_period - 1) * 100
            
            comprehensive_score = (
                trend_strength * 0.3 +
                min(volume_ratio * 5, 15) * 0.3 +
                min(breakout_strength * 2, 10) * 0.4
            )
            
            return {
                '代码': code,
                '名称': name,
                '所属行业': stock_data.get('所属行业', '未知'),
                '收盘价': latest_data['收盘'],
                '涨跌幅': latest_data['涨跌幅'],
                '总市值(亿)': round(stock_data['总市值'] / 100000000, 2),
                '换手率': stock_data['换手率'],
                '成交额(万)': round(stock_data['成交额'] / 10000, 0),
                '趋势强度': round(trend_strength, 2),
                '放量倍数': round(volume_ratio, 2),
                '突破强度': round(breakout_strength, 2),
                '综合评分': round(comprehensive_score, 2),
                '上市天数': days_since_listing,
                '价格波动率': round(price_volatility, 1)
            }
            
        except Exception as e:
            return None
    
    def run_screening(self, max_candidates=30, save_to_csv=True):
        """运行四维筛选"""
        print("🚀 开始A股四维筛选模型V2.0")
        print("=" * 60)
        
        # 1. 获取热点板块
        hot_sectors_list = self.get_hot_sectors()
        
        # 2. 获取所有A股数据
        print("\n📈 正在获取所有A股列表...")
        all_stocks_df = ak.stock_zh_a_spot_em()
        print(f"📊 A股总数: {len(all_stocks_df)} 只")
        
        # 3. 基础筛选
        filtered_stocks = self.basic_screening(all_stocks_df, hot_sectors_list)
        
        if len(filtered_stocks) == 0:
            print("❌ 基础筛选后无股票符合条件")
            return pd.DataFrame()
        
        # 4. 技术分析筛选
        print(f"\n🔬 开始技术分析筛选（预计需要{len(filtered_stocks) * 0.15 / 60:.1f}分钟）...")
        
        # 限制分析数量，按成交额排序优先分析活跃股票
        if len(filtered_stocks) > 150:
            filtered_stocks = filtered_stocks.sort_values(by='成交额', ascending=False).head(150)
            print(f"⚡ 限制分析数量为: {len(filtered_stocks)} 只（按成交额排序）")
        
        candidate_pool = []
        total_stocks = len(filtered_stocks)
        
        for index, (_, stock) in enumerate(filtered_stocks.iterrows()):
            # 进度显示
            if index % 20 == 0:
                progress = (index + 1) / total_stocks * 100
                print(f"📊 分析进度: {index+1}/{total_stocks} ({progress:.1f}%) - {stock['名称']}")
            
            # 技术分析
            result = self.technical_analysis(stock)
            if result:
                candidate_pool.append(result)
                print(f"✅ 发现候选股票: {result['名称']} (评分: {result['综合评分']})")
            
            # 友好访问
            time.sleep(self.config['request_delay'])
        
        # 5. 结果处理
        if not candidate_pool:
            print("\n❌ 未发现符合四维筛选条件的股票")
            return pd.DataFrame()
        
        result_df = pd.DataFrame(candidate_pool)
        
        # 按综合评分排序
        final_result = result_df.sort_values(
            by=['综合评分', '涨跌幅'], ascending=[False, False]
        ).head(max_candidates)
        
        print(f"\n🎯 四维筛选完成，发现 {len(final_result)} 只优质候选股票")
        
        # 6. 保存结果
        if save_to_csv and not final_result.empty:
            timestamp = self.today.strftime('%Y%m%d_%H%M%S')
            filename = f"tickers_cn_v2_{timestamp}.csv"
            final_result.to_csv(filename, index=False, encoding='utf-8-sig')
            
            # 同时保存为标准文件名
            final_result.to_csv("tickers_cn.csv", index=False, encoding='utf-8-sig')
            print(f"💾 结果已保存到: {filename} 和 tickers_cn.csv")
        
        return final_result
    
    def analyze_results(self, result_df):
        """分析筛选结果"""
        if result_df.empty:
            return
        
        print("\n" + "=" * 60)
        print("📊 四维筛选结果分析")
        print("=" * 60)
        
        print(f"🎯 筛选出优质股票: {len(result_df)} 只")
        print(f"📈 平均综合评分: {result_df['综合评分'].mean():.2f}")
        print(f"💰 平均市值: {result_df['总市值(亿)'].mean():.1f} 亿元")
        print(f"🔄 平均换手率: {result_df['换手率'].mean():.2f}%")
        print(f"📊 平均涨跌幅: {result_df['涨跌幅'].mean():.2f}%")
        
        # 行业分布
        if '所属行业' in result_df.columns:
            industry_dist = result_df['所属行业'].value_counts().head(5)
            print(f"\n🏭 主要行业分布:")
            for industry, count in industry_dist.items():
                print(f"   {industry}: {count} 只")
        
        # 投资建议
        print(f"\n💡 A股专家投资建议:")
        print(f"   1. 重点关注综合评分 > {result_df['综合评分'].quantile(0.7):.1f} 的股票")
        print(f"   2. 优先考虑趋势强度 > 2% 的股票（趋势确立）")
        print(f"   3. 关注放量倍数 > 2.0 的股票（资金介入明显）")
        print(f"   4. 建议分批建仓，单只股票仓位控制在 2-3%")
        print(f"   5. 设置止损位 -8%，密切关注成交量变化")
        
        print("\n⚠️ 风险提示:")
        print("   - 股市有风险，投资需谨慎")
        print("   - 本筛选结果仅供参考，不构成投资建议")
        print("   - 请结合自身风险承受能力理性投资")


def main():
    """主函数"""
    print("🎯 A股四维筛选模型 V2.0")
    print("📅 运行时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 创建筛选器
    selector = StockSelectorV2()
    
    # 运行筛选
    result_df = selector.run_screening(max_candidates=25, save_to_csv=True)
    
    # 显示结果
    if not result_df.empty:
        print("\n📋 筛选结果预览:")
        display_columns = ['代码', '名称', '涨跌幅', '综合评分', '趋势强度', '所属行业']
        available_columns = [col for col in display_columns if col in result_df.columns]
        print(result_df[available_columns].head(15))
        
        # 分析结果
        selector.analyze_results(result_df)
    
    print("\n✅ 四维筛选完成！")


if __name__ == "__main__":
    main()