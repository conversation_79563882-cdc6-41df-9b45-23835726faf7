#!/usr/bin/env python3
"""
基于Tushare的A股数据获取系统
==========================
功能: A股市场多时间周期、前复权数据获取
特点: 基于Tushare API、批量高效获取、多周期支持、标准化输出
架构: Provider模式 + Manager编排
"""

import os
import sys
import time
import warnings
import argparse
import logging
from abc import ABC, abstractmethod
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any, Union
from dataclasses import dataclass
import concurrent.futures

import pandas as pd
import numpy as np
from dotenv import load_dotenv

# Tushare数据源依赖
import tushare as ts

# 交易日历依赖（可选）
try:
    import pandas_market_calendars as mcal
    HAS_MARKET_CALENDAR = True
except ImportError:
    HAS_MARKET_CALENDAR = False

try:
    import pytz
    HAS_PYTZ = True
except ImportError:
    HAS_PYTZ = False

# 配置
warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.WARNING, format='%(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

# ==================== 配置系统 ====================

# A股市场配置
A_SHARE_CONFIG = {
    'provider': 'TushareProvider',
    'ticker_file': 'data/tickers/tickers_cn.csv',
    'trading_calendar': 'SSE',
    'timezone': 'Asia/Shanghai',
    'market_close_hour': 15,  # A股收盘时间
    'start_date': '2015-01-01',
    'supported_timeframes': ['1d', '1wk', '1mo'],  # 免费版本只支持日线、周线、月线
    # 注意：分钟级数据(15min, 60min等)需要TuShare Pro付费版本
    'api_params': {
        'token': os.getenv('TUSHARE_TOKEN'),
        'adj': 'qfq',  # 前复权
        'timeout': 30,
        'retry_count': 3,
        'retry_delay': 1  # 重试延迟（秒）
    },
    # Tushare API频率限制配置 (基于Pro版本限制)
    'rate_limits': {
        'daily': 120,        # 日线数据每分钟最大请求数 (Pro版本通常限制)
        'intraday': 60,      # 分钟线数据每分钟最大请求数 (分钟数据限制更严)
        'basic': 200,        # 基础数据每分钟最大请求数
        'batch_size': 20,    # 批量请求股票数量 (降低以避免超限)
        'batch_delay': 1.0   # 批次间延迟 (增加延迟)
    }
}

# 全局配置
GLOBAL_CONFIG = {
    'MAX_WORKERS': int(os.getenv('MAX_WORKERS', '4')),
    'MAX_RETRIES': int(os.getenv('MAX_RETRIES', '3')),
    'VALIDATION_SAMPLE_SIZE': int(os.getenv('VALIDATION_SAMPLE_SIZE', '10')),
    'DATA_DIR': os.getenv('DATA_DIR', 'data'),
    'MIN_VOLUME': int(os.getenv('MIN_VOLUME_A_SHARE', '100000')),
    'MIN_PRICE': float(os.getenv('MIN_PRICE_A_SHARE', '1.0'))
}

# ==================== 数据结构 ====================

@dataclass
class DataQualityMetrics:
    """数据质量指标"""
    total_stocks: int = 0
    successful_updates: int = 0
    failed_updates: int = 0
    up_to_date: int = 0
    validation_passed: int = 0
    validation_failed: int = 0
    
    @property
    def success_rate(self) -> float:
        if self.total_stocks == 0:
            return 0.0
        return (self.successful_updates + self.up_to_date) / self.total_stocks * 100
    
    @property
    def validation_rate(self) -> float:
        total = self.validation_passed + self.validation_failed
        return self.validation_passed / total * 100 if total > 0 else 0.0

@dataclass
class DataFetchRequest:
    """数据获取请求"""
    symbol: str
    start_date: str
    end_date: str
    timeframe: str
    market: str = 'cn'
    is_full_download: bool = False

# ==================== 交易日历管理 ====================

class TradingCalendar:
    """A股交易日历管理器"""
    
    def __init__(self):
        self.market = 'cn'
        self.config = A_SHARE_CONFIG
        self.calendar_available = False
        self._init_calendar()
    
    def _init_calendar(self):
        """初始化交易日历"""
        if not HAS_MARKET_CALENDAR:
            return
        
        try:
            # 使用上海交易所日历
            self.market_calendar = mcal.get_calendar('XSHG')
            self.calendar_available = True
        except Exception as e:
            logger.warning(f"交易日历初始化失败: {e}")
    
    def get_market_time(self) -> datetime:
        """获取A股市场当地时间"""
        if HAS_PYTZ:
            try:
                tz = pytz.timezone(self.config['timezone'])
                return datetime.now(tz)
            except Exception:
                pass
        
        # 简化时区处理：UTC+8
        return datetime.utcnow() + timedelta(hours=8)
    
    def get_last_trading_day(self, timeframe: str = '1d') -> str:
        """获取最近交易日"""
        market_time = self.get_market_time()
        close_hour = self.config['market_close_hour']
        
        # 差异化策略：日线/周线/月线 vs 分钟线
        if timeframe in ['1d', '1wk', '1mo']:
            # 日线数据：收盘后当天算最新交易日，否则用前一日
            target_date = market_time.date() if (
                market_time.weekday() < 5 and market_time.hour >= close_hour
            ) else market_time.date() - timedelta(days=1)
        else:
            # 分钟线数据：交易时间内可获取当日数据
            if market_time.weekday() < 5 and 9 <= market_time.hour < 15:
                target_date = market_time.date()
            elif market_time.weekday() < 5 and market_time.hour >= close_hour:
                target_date = market_time.date()
            else:
                target_date = market_time.date() - timedelta(days=1)
        
        # 使用交易日历（如果可用）
        if self.calendar_available:
            try:
                start_date = target_date - timedelta(days=15)
                schedule = self.market_calendar.schedule(
                    start_date=start_date, end_date=target_date
                )
                if not schedule.empty:
                    return schedule.index[-1].strftime('%Y-%m-%d')
            except Exception:
                pass
        
        # 简化版本：回退到最近工作日
        while target_date.weekday() >= 5:
            target_date -= timedelta(days=1)
        
        return target_date.strftime('%Y-%m-%d')
    
    def should_update_intraday_data(self) -> bool:
        """判断是否应该更新分钟级别的实时数据"""
        market_time = self.get_market_time()
        
        # 交易日且在交易时间内（9:30-15:00）
        if (market_time.weekday() < 5 and 
            9 <= market_time.hour < 15 and 
            not (market_time.hour == 9 and market_time.minute < 30)):
            return True
            
        # 交易日收盘后也可以更新
        if (market_time.weekday() < 5 and 
            market_time.hour >= self.config['market_close_hour']):
            return True
            
        return False

# ==================== 数据提供商接口 ====================

class DataProvider(ABC):
    """数据提供商抽象基类"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.trading_calendar = TradingCalendar()
        self._initialize()
    
    @abstractmethod
    def _initialize(self):
        """初始化数据源连接"""
        pass
    
    @abstractmethod
    def fetch_data(self, request: DataFetchRequest) -> Optional[pd.DataFrame]:
        """获取原始数据"""
        pass
    
    def standardize(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """标准化数据格式"""
        if df.empty:
            return df
        
        # 确保必要列存在
        required_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'symbol']
        for col in required_cols:
            if col not in df.columns:
                raise ValueError(f"缺少必要列: {col}")
        
        # 选择标准列并重新排序
        standard_df = df[required_cols].copy()
        
        # 标准化数据类型
        standard_df['timestamp'] = pd.to_datetime(standard_df['timestamp'])
        price_cols = ['open', 'high', 'low', 'close']
        standard_df[price_cols] = standard_df[price_cols].astype(float)
        standard_df['volume'] = standard_df['volume'].astype(float)
        standard_df['symbol'] = standard_df['symbol'].astype(str)
        
        # 移除时区信息（统一格式）
        if standard_df['timestamp'].dt.tz is not None:
            standard_df['timestamp'] = standard_df['timestamp'].dt.tz_localize(None)
        
        # 按时间排序
        standard_df = standard_df.sort_values('timestamp').reset_index(drop=True)
        
        return standard_df
    
    def validate_data(self, df: pd.DataFrame, symbol: str) -> bool:
        """验证数据质量"""
        if df.empty:
            return False
        
        # 检查必要列
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_columns):
            return False
        
        # 检查价格合理性
        price_columns = ['open', 'high', 'low', 'close']
        if (df[price_columns] <= 0).any().any():
            logger.warning(f"{symbol}: 发现非正价格")
            return False
        
        # 检查OHLC基本逻辑
        if (df['high'] < df['low']).any():
            logger.warning(f"{symbol}: 发现高价<低价异常")
            return False
        
        return True

# ==================== Tushare数据提供商实现 ====================

class TushareProvider(DataProvider):
    """Tushare数据提供商"""
    
    def _initialize(self):
        """初始化Tushare连接"""
        token = self.config['api_params']['token']
        if not token:
            raise ValueError("未找到TUSHARE_TOKEN，请检查环境变量")
        
        # 确保token设置正确
        ts.set_token(token)
        self.pro_api = ts.pro_api(token)
        
        # 请求频率控制
        self.last_request_time = {}
        self.request_counts = {}
    
    def _rate_limit_wait(self, api_type: str):
        """频率限制控制"""
        current_time = time.time()
        
        # 初始化时间记录
        if api_type not in self.last_request_time:
            self.last_request_time[api_type] = current_time
            self.request_counts[api_type] = 0
            return
        
        # 检查是否需要等待
        time_diff = current_time - self.last_request_time[api_type]
        
        if time_diff < 60:  # 1分钟内
            limit = self.config['rate_limits'].get(api_type, 100)
            if self.request_counts[api_type] >= limit:
                wait_time = 60 - time_diff + 1
                logger.info(f"达到API频率限制，等待 {wait_time:.1f} 秒...")
                time.sleep(wait_time)
                self.request_counts[api_type] = 0
                self.last_request_time[api_type] = time.time()
        else:
            # 重置计数器
            self.request_counts[api_type] = 0
            self.last_request_time[api_type] = current_time
        
        self.request_counts[api_type] += 1
    
    def _convert_timeframe_to_freq(self, timeframe: str) -> str:
        """转换时间周期到tushare频率"""
        timeframe_map = {
            '1d': 'D',        # 日线
            '1wk': 'W',       # 周线
            '1mo': 'M'        # 月线
        }
        return timeframe_map.get(timeframe, 'D')
    
    def _convert_symbol_format(self, symbol: str) -> str:
        """转换股票代码格式为Tushare格式"""
        # 如果已经是Tushare格式(000001.SZ)，直接返回
        if '.' in symbol and (symbol.endswith('.SZ') or symbol.endswith('.SH') or symbol.endswith('.BJ')):
            return symbol
        
        # 转换6位数字代码
        if len(symbol) == 6 and symbol.isdigit():
            if symbol.startswith(('000', '001', '002', '003', '300', '301')):
                return f"{symbol}.SZ"  # 深交所
            elif symbol.startswith(('600', '601', '603', '688')):
                return f"{symbol}.SH"  # 上交所
            elif symbol.startswith(('4', '8')):
                return f"{symbol}.BJ"  # 北交所
        
        return symbol
    
    def fetch_data(self, request: DataFetchRequest) -> Optional[pd.DataFrame]:
        """获取Tushare数据"""
        symbol_tushare = self._convert_symbol_format(request.symbol)
        
        for attempt in range(GLOBAL_CONFIG['MAX_RETRIES']):
            try:
                # 转换日期格式
                start_date_ts = request.start_date.replace('-', '')
                end_date_ts = request.end_date.replace('-', '')
                
                # 根据时间周期选择API
                if request.timeframe == '1d':
                    # 使用日线API
                    self._rate_limit_wait('daily')
                    
                    df = self.pro_api.daily(
                        ts_code=symbol_tushare,
                        start_date=start_date_ts,
                        end_date=end_date_ts,
                        adj=self.config['api_params']['adj']
                    )
                    
                    if not df.empty:
                        # 标准化列名
                        df['timestamp'] = pd.to_datetime(df['trade_date'], format='%Y%m%d')
                        df = df.rename(columns={'vol': 'volume'})
                        
                elif request.timeframe in ['1wk', '1mo']:
                    # 使用周线/月线API
                    self._rate_limit_wait('daily')
                    
                    if request.timeframe == '1wk':
                        df = self.pro_api.weekly(
                            ts_code=symbol_tushare,
                            start_date=start_date_ts,
                            end_date=end_date_ts
                        )
                    else:  # 1mo
                        df = self.pro_api.monthly(
                            ts_code=symbol_tushare,
                            start_date=start_date_ts,
                            end_date=end_date_ts
                        )
                    
                    if not df.empty:
                        df['timestamp'] = pd.to_datetime(df['trade_date'], format='%Y%m%d')
                        df = df.rename(columns={'vol': 'volume'})
                        
                else:
                    # 不支持的时间周期
                    logger.warning(f"时间周期 {request.timeframe} 不被支持，当前只支持 1d, 1wk, 1mo")
                    return None
                
                if df.empty:
                    if attempt < GLOBAL_CONFIG['MAX_RETRIES'] - 1:
                        time.sleep(self.config['api_params']['retry_delay'])
                        continue
                    return None
                
                # 数据标准化
                df = df.sort_values('timestamp').reset_index(drop=True)
                df['symbol'] = request.symbol  # 使用原始symbol格式
                
                # 数据清洗
                df = df.dropna(subset=['open', 'high', 'low', 'close', 'volume'])
                
                return df
                
            except Exception as e:
                if attempt == GLOBAL_CONFIG['MAX_RETRIES'] - 1:
                    logger.error(f"{request.symbol}: Tushare获取失败 - {e}")
                    return None
                time.sleep(self.config['api_params']['retry_delay'])
        
        return None
    
    def batch_fetch_data(self, requests: List[DataFetchRequest]) -> Dict[str, Optional[pd.DataFrame]]:
        """批量获取数据"""
        results = {}
        batch_size = self.config['rate_limits']['batch_size']
        batch_delay = self.config['rate_limits']['batch_delay']
        
        print(f"Tushare批量下载: {len(requests)}只股票，批次大小: {batch_size}")
        
        # 分批处理
        for i in range(0, len(requests), batch_size):
            batch_requests = requests[i:i + batch_size]
            
            # 进度显示
            if i > 0:
                print(f"进度: {i}/{len(requests)}")
                time.sleep(batch_delay)  # 批次间延迟
            
            # 处理单个批次
            for req in batch_requests:
                try:
                    data = self.fetch_data(req)
                    results[req.symbol] = data
                except Exception as e:
                    logger.warning(f"{req.symbol}: 批量获取失败 - {e}")
                    results[req.symbol] = None
        
        return results

# ==================== 多周期数据管理器 ====================

class MultiTimeframeManager:
    """多周期数据管理器 - A股专用"""
    
    def __init__(self, timeframes: List[str] = None):
        self.market = 'cn'
        self.market_config = A_SHARE_CONFIG
        
        # 默认使用所有支持的时间周期
        if timeframes is None:
            self.timeframes = self.market_config['supported_timeframes']
        else:
            # 验证时间周期支持
            for tf in timeframes:
                if tf not in self.market_config['supported_timeframes']:
                    raise ValueError(f"A股市场不支持时间周期 {tf}")
            self.timeframes = timeframes
        
        # 创建各时间周期的数据管理器
        self.managers = {}
        for timeframe in self.timeframes:
            self.managers[timeframe] = DataManager(timeframe)
    
    def process_single_stock_all_timeframes(self, symbol: str) -> Dict[str, str]:
        """处理单只股票的所有时间周期数据"""
        results = {}
        
        try:
            # 每个时间周期独立处理
            for timeframe in self.timeframes:
                try:
                    manager = self.managers[timeframe]
                    symbol_result, status = manager.process_single_stock(symbol)
                    results[timeframe] = status
                        
                except Exception as e:
                    logger.error(f"{symbol} ({timeframe}): 处理失败 - {e}")
                    results[timeframe] = 'error'
            
            return results
            
        except Exception as e:
            logger.error(f"{symbol}: 多周期处理失败 - {e}")
            # 所有周期都失败
            for tf in self.timeframes:
                results[tf] = 'error'
            return results
    
    def run_multi_timeframe_update(self, stock_list: List[str], max_stocks: Optional[int] = None,
                                  verbose: bool = True, use_batch_mode: bool = True) -> Dict[str, DataQualityMetrics]:
        """批量更新多个时间周期的股票数据"""
        if max_stocks:
            stock_list = stock_list[:max_stocks]
        
        # 初始化指标
        metrics = {}
        for timeframe in self.timeframes:
            metrics[timeframe] = DataQualityMetrics(total_stocks=len(stock_list))
        
        if verbose:
            mode_info = "批量模式" if use_batch_mode else "单个模式"
            print(f"A股多周期数据更新开始 ({mode_info}) | 周期: {', '.join(self.timeframes)} | 股票: {len(stock_list)}只")
            print("基于Tushare API进行数据获取")
        
        if use_batch_mode:
            # 批量模式 - 每个周期独立批量下载
            for timeframe in self.timeframes:
                if verbose:
                    print(f"\n批量获取周期: {timeframe}")
                
                manager = self.managers[timeframe]
                timeframe_metrics = manager.run_batch_update(
                    stock_list, None, verbose=verbose, use_batch_mode=True
                )
                metrics[timeframe] = timeframe_metrics
                
                if verbose:
                    print(f"周期 {timeframe} 完成: {timeframe_metrics.successful_updates}成功 + {timeframe_metrics.up_to_date}最新 | 成功率: {timeframe_metrics.success_rate:.1f}%")
        else:
            # 单个股票并行模式
            if verbose:
                print("使用单个股票并行模式")
            
            max_workers = GLOBAL_CONFIG['MAX_WORKERS']
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_symbol = {
                    executor.submit(self.process_single_stock_all_timeframes, symbol): symbol 
                    for symbol in stock_list
                }
                
                for i, future in enumerate(concurrent.futures.as_completed(future_to_symbol), 1):
                    symbol = future_to_symbol[future]
                    results = future.result()
                    
                    # 更新各周期的指标
                    for timeframe, status in results.items():
                        metric = metrics[timeframe]
                        if status == "success":
                            metric.successful_updates += 1
                        elif status == "skip":
                            metric.up_to_date += 1
                        else:
                            metric.failed_updates += 1
                    
                    # 显示进度
                    if verbose and (i % 50 == 0 or i == len(stock_list)):
                        print(f"处理进度: {i}/{len(stock_list)}")
        
        return metrics
    
    def load_stock_list(self) -> List[str]:
        """加载A股股票代码列表"""
        return list(self.managers.values())[0].load_stock_list()
    
    def generate_multi_timeframe_report(self, metrics_dict: Dict[str, DataQualityMetrics], 
                                       verbose: bool = True) -> Dict[str, Any]:
        """生成多周期汇总报告"""
        reports = {}
        
        for timeframe, metrics in metrics_dict.items():
            manager = self.managers[timeframe]
            validation_metrics = DataQualityMetrics()  # 简化版本
            report = manager.generate_summary_report(metrics, validation_metrics, verbose=False)
            reports[timeframe] = report
        
        if verbose:
            print("\n=== A股多周期数据更新汇总 ===")
            for timeframe, metrics in metrics_dict.items():
                local_count = 0
                # 新格式统计: data/cn/{symbol}/{timeframe}.parquet
                cn_data_dir = Path(GLOBAL_CONFIG['DATA_DIR']) / 'cn'
                if cn_data_dir.exists():
                    for symbol_dir in cn_data_dir.iterdir():
                        if symbol_dir.is_dir():
                            data_file = symbol_dir / f"{timeframe}.parquet"
                            if data_file.exists():
                                local_count += 1
                
                print(f"{timeframe:>6}: {metrics.successful_updates}新增 + {metrics.up_to_date}最新 = {local_count}只股票 | 成功率: {metrics.success_rate:.1f}%")
        
        # 保存综合报告到cn子目录
        import json
        combined_report = {
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'market': 'cn',
            'data_source': 'tushare',
            'timeframes': self.timeframes,
            'reports': reports
        }
        
        cn_dir = Path(GLOBAL_CONFIG['DATA_DIR']) / 'cn'
        cn_dir.mkdir(parents=True, exist_ok=True)
        report_file = cn_dir / "multi_timeframe_report_tushare.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(combined_report, f, indent=2, ensure_ascii=False)
        
        return combined_report

# ==================== 数据管理器 ====================

class DataManager:
    """A股数据管理器"""
    
    def __init__(self, timeframe: str):
        self.market = 'cn'
        self.timeframe = timeframe
        self.market_config = A_SHARE_CONFIG
        self.data_dir = Path(GLOBAL_CONFIG['DATA_DIR'])
        
        # 验证时间周期支持
        if timeframe not in self.market_config['supported_timeframes']:
            raise ValueError(f"A股市场不支持时间周期 {timeframe}")
        
        # 初始化Tushare数据提供商
        self.provider = TushareProvider(self.market_config)
        
        # 创建基础存储目录
        (self.data_dir / 'cn').mkdir(parents=True, exist_ok=True)
    
    def load_stock_list(self) -> List[str]:
        """加载A股股票代码列表"""
        ticker_file = Path(self.market_config['ticker_file'])
        try:
            df = pd.read_csv(ticker_file)
            # 去重并过滤无效代码
            df = df.drop_duplicates(subset=['Ticker'])
            df = df.dropna(subset=['Ticker'])
            tickers = df['Ticker'].tolist()
            
            logger.info(f"成功加载A股股票列表: {len(tickers)}只股票 (已去重)")
            return tickers
        except Exception as e:
            logger.error(f"加载A股股票列表失败: {e}")
            return []
    
    def check_existing_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """检查本地数据状态"""
        # 新格式: data/cn/{symbol}/{timeframe}.parquet
        data_file = self.data_dir / 'cn' / symbol / f"{self.timeframe}.parquet"
        
        if not data_file.exists():
            return None
        
        try:
            df = pd.read_parquet(data_file)
            if df.empty:
                return None
            
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            return {
                'symbol': symbol,
                'data_points': len(df),
                'start_date': df['timestamp'].min(),
                'end_date': df['timestamp'].max(),
                'file_path': data_file
            }
        except Exception as e:
            logger.warning(f"检查{symbol}数据出错: {e}")
            return None
    
    def determine_update_strategy(self, symbol: str) -> Tuple[Optional[str], Optional[str], str]:
        """确定更新策略"""
        existing_data = self.check_existing_data(symbol)
        last_trading_day = self.provider.trading_calendar.get_last_trading_day(self.timeframe)
        
        if existing_data is None:
            # 全量下载
            start_date = self.market_config['start_date']
            return start_date, last_trading_day, "full_download"
        
        last_date = existing_data['end_date'].strftime('%Y-%m-%d')
        
        # 差异化判断：分钟线 vs 日线
        if self.timeframe in ['5min', '15min', '30min', '60min']:
            # 分钟线：交易时间内可能需要更新
            if self.provider.trading_calendar.should_update_intraday_data():
                current_trading_day = self.provider.trading_calendar.get_last_trading_day(self.timeframe)
                if last_date < current_trading_day:
                    start_date = (existing_data['end_date'] + timedelta(days=1)).strftime('%Y-%m-%d')
                    return start_date, current_trading_day, "incremental_update"
                elif last_date == current_trading_day:
                    return last_date, current_trading_day, "intraday_update"
            
            if last_date >= last_trading_day:
                return None, None, "up_to_date"
            else:
                start_date = (existing_data['end_date'] + timedelta(days=1)).strftime('%Y-%m-%d')
                return start_date, last_trading_day, "incremental_update"
        else:
            # 日线/周线/月线
            if last_date >= last_trading_day:
                return None, None, "up_to_date"
            else:
                start_date = (existing_data['end_date'] + timedelta(days=1)).strftime('%Y-%m-%d')
                return start_date, last_trading_day, "incremental_update"
    
    def merge_and_save_data(self, symbol: str, new_data: pd.DataFrame) -> bool:
        """合并和保存数据"""
        try:
            existing_data = self.check_existing_data(symbol)
            
            if existing_data is None:
                final_data = new_data.copy()
            else:
                # 合并数据
                old_df = pd.read_parquet(existing_data['file_path'])
                old_df['timestamp'] = pd.to_datetime(old_df['timestamp'])
                new_data['timestamp'] = pd.to_datetime(new_data['timestamp'])
                
                # 移除时区信息
                if old_df['timestamp'].dt.tz is not None:
                    old_df['timestamp'] = old_df['timestamp'].dt.tz_localize(None)
                if new_data['timestamp'].dt.tz is not None:
                    new_data['timestamp'] = new_data['timestamp'].dt.tz_localize(None)
                
                # 合并数据
                final_data = pd.concat([old_df, new_data], ignore_index=True)
                
                # 去重并排序
                final_data = final_data.drop_duplicates(subset=['timestamp'], keep='last')
                final_data = final_data.sort_values('timestamp').reset_index(drop=True)
            
            # 保存数据 - 新格式: data/cn/{symbol}/{timeframe}.parquet
            symbol_dir = self.data_dir / 'cn' / symbol
            symbol_dir.mkdir(parents=True, exist_ok=True)
            
            output_file = symbol_dir / f"{self.timeframe}.parquet"
            final_data.to_parquet(output_file, index=False)
            return True
            
        except Exception as e:
            logger.error(f"{symbol}: 保存失败 - {e}")
            return False
    
    def process_single_stock(self, symbol: str) -> Tuple[str, str]:
        """处理单只股票"""
        try:
            start_date, end_date, strategy = self.determine_update_strategy(symbol)
            
            if strategy == "up_to_date":
                return symbol, "skip"
            
            # 创建数据请求
            request = DataFetchRequest(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                timeframe=self.timeframe,
                market='cn',
                is_full_download=(strategy == "full_download")
            )
            
            # 获取原始数据
            raw_data = self.provider.fetch_data(request)
            if raw_data is None or raw_data.empty:
                # 对于日内更新，如果没有新数据也算正常
                if strategy == "intraday_update":
                    return symbol, "skip"
                return symbol, "error"
            
            # 标准化数据
            standardized_data = self.provider.standardize(raw_data, symbol)
            
            # 验证数据质量
            if not self.provider.validate_data(standardized_data, symbol):
                return symbol, "error"
            
            # 保存数据
            if self.merge_and_save_data(symbol, standardized_data):
                return symbol, "success"
            else:
                return symbol, "error"
                
        except Exception as e:
            logger.error(f"{symbol}: 处理失败 - {e}")
            return symbol, "error"
    
    def batch_process_stocks(self, stock_list: List[str], batch_size: int = 50) -> Dict[str, str]:
        """批量处理股票数据"""
        results = {}
        
        # 分析更新策略
        stock_strategies = {}
        for symbol in stock_list:
            start_date, end_date, strategy = self.determine_update_strategy(symbol)
            stock_strategies[symbol] = {
                'start_date': start_date,
                'end_date': end_date,
                'strategy': strategy
            }
        
        # 按更新策略分组
        strategy_groups = {
            'up_to_date': [],
            'full_download': [],
            'incremental_update': [],
            'intraday_update': []
        }
        
        for symbol, info in stock_strategies.items():
            strategy_groups[info['strategy']].append(symbol)
        
        # 处理不需要更新的股票
        for symbol in strategy_groups['up_to_date']:
            results[symbol] = 'skip'
        
        # 批量处理需要更新的股票
        for strategy_type in ['full_download', 'incremental_update', 'intraday_update']:
            symbols_to_process = strategy_groups[strategy_type]
            if not symbols_to_process:
                continue
            
            # 按相同参数进一步分组
            param_groups = {}
            for symbol in symbols_to_process:
                info = stock_strategies[symbol]
                key = (info['start_date'], info['end_date'], strategy_type)
                if key not in param_groups:
                    param_groups[key] = []
                param_groups[key].append(symbol)
            
            # 批量获取每个参数组
            for (start_date, end_date, strategy), symbols in param_groups.items():
                batch_results = self._process_batch_group(symbols, start_date, end_date, strategy, batch_size)
                results.update(batch_results)
        
        return results
    
    def _process_batch_group(self, symbols: List[str], start_date: str, end_date: str, 
                           strategy: str, batch_size: int) -> Dict[str, str]:
        """处理单个批次组的股票"""
        results = {}
        
        # 创建批量请求
        requests = []
        for symbol in symbols:
            requests.append(DataFetchRequest(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                timeframe=self.timeframe,
                market='cn',
                is_full_download=(strategy == "full_download")
            ))
        
        # 批量获取数据
        batch_data_dict = self.provider.batch_fetch_data(requests)
        
        # 处理批量结果
        for symbol in symbols:
            try:
                raw_data = batch_data_dict.get(symbol)
                
                if raw_data is None or raw_data.empty:
                    # 对于日内更新，如果没有新数据也算正常
                    if strategy == "intraday_update":
                        results[symbol] = "skip"
                    else:
                        results[symbol] = "error"
                    continue
                
                # 标准化数据
                standardized_data = self.provider.standardize(raw_data, symbol)
                
                # 验证数据质量
                if not self.provider.validate_data(standardized_data, symbol):
                    results[symbol] = "error"
                    continue
                
                # 保存数据
                if self.merge_and_save_data(symbol, standardized_data):
                    results[symbol] = "success"
                else:
                    results[symbol] = "error"
                    
            except Exception as e:
                logger.error(f"{symbol}: 批量处理失败 - {e}")
                results[symbol] = "error"
        
        return results
    
    def run_batch_update(self, stock_list: List[str], max_stocks: Optional[int] = None,
                        verbose: bool = True, use_batch_mode: bool = True) -> DataQualityMetrics:
        """批量更新股票数据"""
        last_trading_day = self.provider.trading_calendar.get_last_trading_day()
        
        if max_stocks:
            stock_list = stock_list[:max_stocks]
            if verbose:
                print(f"测试模式: {max_stocks}只股票")
        
        if verbose:
            mode_info = "批量模式" if use_batch_mode else "单个模式"
            print(f"A股数据更新开始 ({mode_info}) | 周期: {self.timeframe} | 交易日: {last_trading_day} | 股票: {len(stock_list)}只")
        
        metrics = DataQualityMetrics(total_stocks=len(stock_list))
        
        if use_batch_mode and hasattr(self.provider, 'batch_fetch_data'):
            # 使用批量模式
            batch_size = self.market_config['rate_limits']['batch_size']
            results = self.batch_process_stocks(stock_list, batch_size)
            
            # 统计结果
            for symbol, status in results.items():
                if status == "success":
                    metrics.successful_updates += 1
                elif status == "skip":
                    metrics.up_to_date += 1
                else:
                    metrics.failed_updates += 1
            
            if verbose:
                print(f"批量处理完成: {len(stock_list)}只股票")
        else:
            # 使用单个股票并行模式
            max_workers = GLOBAL_CONFIG['MAX_WORKERS']
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_symbol = {executor.submit(self.process_single_stock, symbol): symbol 
                                  for symbol in stock_list}
                
                for i, future in enumerate(concurrent.futures.as_completed(future_to_symbol), 1):
                    symbol, status = future.result()
                    
                    if status == "success":
                        metrics.successful_updates += 1
                    elif status == "skip":
                        metrics.up_to_date += 1
                    else:
                        metrics.failed_updates += 1
                    
                    # 显示进度
                    if verbose and (i % 50 == 0 or i == len(stock_list)):
                        print(f"处理进度: {i}/{len(stock_list)}")
        
        return metrics
    
    def generate_summary_report(self, update_metrics: DataQualityMetrics, 
                              validation_metrics: DataQualityMetrics, 
                              verbose: bool = True) -> Dict[str, Any]:
        """生成汇总报告"""
        if verbose:
            # 统计本地数据量 - 新格式: data/cn/{symbol}/{timeframe}.parquet
            local_count = 0
            cn_data_dir = self.data_dir / 'cn'
            if cn_data_dir.exists():
                for symbol_dir in cn_data_dir.iterdir():
                    if symbol_dir.is_dir():
                        data_file = symbol_dir / f"{self.timeframe}.parquet"
                        if data_file.exists():
                            local_count += 1
            
            print(f"更新完成: {update_metrics.successful_updates}新增 + {update_metrics.up_to_date}最新 = {local_count}只股票 (A股/{self.timeframe}) | 成功率: {update_metrics.success_rate:.1f}%")
            
            if update_metrics.failed_updates > 0:
                print(f"注意: {update_metrics.failed_updates}只股票更新失败")
        
        # 生成JSON报告
        import json
        report = {
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'market': 'cn',
            'data_source': 'tushare',
            'timeframe': self.timeframe,
            'last_trading_day': self.provider.trading_calendar.get_last_trading_day(),
            'data_format': 'standardized_ohlcv',
            'storage_path': f"data/cn/{{symbol}}/{self.timeframe}.parquet",
            'update_metrics': {
                'total_stocks': update_metrics.total_stocks,
                'successful_updates': update_metrics.successful_updates,
                'up_to_date': update_metrics.up_to_date,
                'failed_updates': update_metrics.failed_updates,
                'success_rate': update_metrics.success_rate
            },
            'validation_metrics': {
                'total_validated': validation_metrics.total_stocks,
                'validation_passed': validation_metrics.validation_passed,
                'validation_failed': validation_metrics.validation_failed,
                'validation_rate': validation_metrics.validation_rate
            }
        }
        
        # 保存报告到cn子目录
        cn_dir = self.data_dir / 'cn'
        cn_dir.mkdir(parents=True, exist_ok=True)
        report_file = cn_dir / f"data_quality_report_tushare_{self.timeframe.replace('min', 'm')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return report

# ==================== 主程序入口 ====================

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='基于Tushare的A股数据获取系统')
    
    # 时间周期参数
    timeframe_group = parser.add_mutually_exclusive_group(required=True)
    timeframe_group.add_argument('--timeframe', 
                               help='单个时间周期 (如: 1d, 60min, 15min)')
    timeframe_group.add_argument('--multi-timeframe', action='store_true',
                               help='一次获取所有支持的时间周期')
    timeframe_group.add_argument('--timeframes', nargs='+',
                               help='指定多个时间周期 (如: --timeframes 1d 60min 15min)')
    
    # 可选参数
    parser.add_argument('--max-stocks', type=int, help='最大处理股票数量')
    parser.add_argument('--workers', type=int, help='并行线程数')
    parser.add_argument('--quiet', action='store_true', help='静默模式')
    parser.add_argument('--batch-mode', action='store_true', default=True, help='使用批量下载模式 (默认开启)')
    parser.add_argument('--disable-batch', action='store_true', help='禁用批量下载模式，使用单个股票模式')
    
    args = parser.parse_args()
    
    # 确定使用的时间周期
    market_config = A_SHARE_CONFIG
    
    if args.timeframe:
        # 单周期模式
        if args.timeframe not in market_config['supported_timeframes']:
            print(f"错误: A股市场不支持时间周期 {args.timeframe}")
            print(f"支持的时间周期: {', '.join(market_config['supported_timeframes'])}")
            
            # 对分钟级数据给出特别提示
            minute_timeframes = ['5min', '15min', '30min', '60min', '1h']
            if args.timeframe in minute_timeframes:
                print(f"\n💡 提示: 分钟级数据 ({args.timeframe}) 需要TuShare Pro付费版本")
                print("   免费版本仅支持: 1d(日线), 1wk(周线), 1mo(月线)")
                print("   如需分钟数据，请考虑升级到TuShare Pro版本")
            return
        use_multi_timeframe = False
        target_timeframes = [args.timeframe]
    
    elif args.multi_timeframe:
        # 所有支持的时间周期
        use_multi_timeframe = True
        target_timeframes = market_config['supported_timeframes']
    
    elif args.timeframes:
        # 指定的多个时间周期
        unsupported_timeframes = []
        minute_timeframes = ['5min', '15min', '30min', '60min', '1h']
        
        for tf in args.timeframes:
            if tf not in market_config['supported_timeframes']:
                unsupported_timeframes.append(tf)
        
        if unsupported_timeframes:
            print(f"错误: A股市场不支持以下时间周期: {', '.join(unsupported_timeframes)}")
            print(f"支持的时间周期: {', '.join(market_config['supported_timeframes'])}")
            
            # 检查是否包含分钟级数据
            minute_in_unsupported = [tf for tf in unsupported_timeframes if tf in minute_timeframes]
            if minute_in_unsupported:
                print(f"\n💡 提示: 分钟级数据 ({', '.join(minute_in_unsupported)}) 需要TuShare Pro付费版本")
                print("   免费版本仅支持: 1d(日线), 1wk(周线), 1mo(月线)")
                print("   如需分钟数据，请考虑升级到TuShare Pro版本")
            return
        use_multi_timeframe = True
        target_timeframes = args.timeframes
    
    # 确定批量模式设置
    use_batch_mode = args.batch_mode and not args.disable_batch
    
    # 覆盖全局配置
    if args.workers:
        GLOBAL_CONFIG['MAX_WORKERS'] = args.workers
    
    try:
        if use_multi_timeframe:
            # 多周期模式
            manager = MultiTimeframeManager(target_timeframes)
            
            # 加载股票列表
            stock_list = manager.load_stock_list()
            if not stock_list:
                print("无法加载A股股票列表，程序退出")
                return
            
            # 运行多周期批量更新
            metrics_dict = manager.run_multi_timeframe_update(
                stock_list, args.max_stocks, verbose=not args.quiet, use_batch_mode=use_batch_mode
            )
            
            # 生成多周期汇总报告
            manager.generate_multi_timeframe_report(
                metrics_dict, verbose=not args.quiet
            )
        
        else:
            # 单周期模式
            manager = DataManager(args.timeframe)
            
            # 加载股票列表
            stock_list = manager.load_stock_list()
            if not stock_list:
                print("无法加载A股股票列表，程序退出")
                return
            
            # 运行批量更新
            update_metrics = manager.run_batch_update(
                stock_list, args.max_stocks, verbose=not args.quiet, use_batch_mode=use_batch_mode
            )
            
            # 生成汇总报告
            validation_metrics = DataQualityMetrics()  # 简化版本
            manager.generate_summary_report(
                update_metrics, validation_metrics, verbose=not args.quiet
            )
        
        # 系统状态
        if not args.quiet:
            print("A股数据获取系统运行完成")
    
    except Exception as e:
        print(f"系统错误: {e}")
        if not args.quiet:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()