#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
from datetime import datetime, timedelta
import pandas as pd
import yfinance as yf
from enum import Enum
import logging
from typing import List, Tuple, Optional
import argparse

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Market(Enum):
    """市场枚举类"""
    US = "us"  # 美股市场
    CN = "cn"  # A股市场

class TimeFrame(Enum):
    """时间周期枚举"""
    DAILY = ("daily", "1d")
    WEEKLY = ("weekly", "1wk")
    MONTHLY = ("monthly", "1mo")
    HOUR_1 = ("1h", "1h")
    MIN_15 = ("15min", "15m")
    MIN_30 = ("30min", "30m")

    def __init__(self, file_suffix, yfinance_interval):
        self.file_suffix = file_suffix
        self.yfinance_interval = yfinance_interval

class StockInfo:
    """股票信息类"""
    def __init__(self, code: str, name: str, market: Market):
        self.code = code  # yfinance格式代码，如 AAPL, 000001.SS
        self.name = name
        self.market = market

    @property
    def directory_code(self) -> str:
        """获取用于目录名的代码"""
        if self.market == Market.CN:
            # A股：去掉后缀，保留6位数字
            if '.' in self.code:
                return self.code.split('.')[0].zfill(6)
            return self.code.zfill(6)
        else:
            # 美股：直接使用代码
            return self.code

class YFinanceDownloader:
    def __init__(self):
        """
        初始化YFinanceDownloader
        """
        self.base_path = "data"
        self.max_retries = 3
        self.retry_delay = 1  # 重试间隔（秒）

    def _create_directory_structure(self, market: Market, directory_code: str):
        """
        创建数据存储目录结构
        :param market: 市场类型
        :param directory_code: 用于目录的股票代码
        :return: 股票数据目录路径
        """
        market_dir = os.path.join(self.base_path, market.value)
        stock_dir = os.path.join(market_dir, directory_code)
        os.makedirs(stock_dir, exist_ok=True)
        return stock_dir

    def _check_need_update(self, stock_info: StockInfo, timeframe: TimeFrame, 
                          start_date: str = None, end_date: str = None) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        检查是否需要更新数据
        :param stock_info: 股票信息
        :param timeframe: 时间周期
        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: (是否需要更新, 开始日期, 结束日期)
        """
        try:
            file_path = os.path.join(
                self.base_path, 
                stock_info.market.value, 
                stock_info.directory_code, 
                f"{timeframe.file_suffix}.csv"
            )
            
            if not end_date:
                end_date = datetime.now().strftime("%Y-%m-%d")
                
            # 如果文件不存在，需要从头开始下载
            if not os.path.exists(file_path):
                return True, start_date or "2020-01-01", end_date
                
            # 读取最后记录时间
            df = pd.read_csv(file_path)
            if df.empty:
                return True, start_date or "2020-01-01", end_date
                
            # 获取最后一条记录的时间
            df['Date'] = pd.to_datetime(df['Date'])
            last_time = df['Date'].max()
            
            # 转换时间为datetime对象进行比较
            end_time = pd.to_datetime(end_date)
            
            # 如果用户指定了开始日期，使用用户指定的
            if start_date:
                return True, start_date, end_date
                
            # 获取最后记录日期的下一天作为请求的开始时间
            request_start = (last_time.date() + timedelta(days=1)).strftime("%Y-%m-%d")
            
            # 如果最后记录日期大于等于结束日期，不需要更新
            if last_time.date() >= end_time.date():
                return False, None, None
                
            return True, request_start, end_date
                
        except Exception as e:
            logger.error(f"检查更新状态时发生错误: {str(e)}")
            return True, start_date or "2020-01-01", end_date

    def merge_new_data(self, existing_df: pd.DataFrame, new_df: pd.DataFrame) -> pd.DataFrame:
        """合并新旧数据"""
        if existing_df is None or existing_df.empty:
            return new_df

        # 确保时间列为datetime类型
        existing_df['Date'] = pd.to_datetime(existing_df['Date'])
        new_df['Date'] = pd.to_datetime(new_df['Date'])
        
        # 合并所有数据
        combined_df = pd.concat([existing_df, new_df])
        
        # 按时间排序并删除重复数据，保留最新的记录
        combined_df = combined_df.sort_values('Date')
        combined_df = combined_df.drop_duplicates(subset=['Date'], keep='last')
        
        return combined_df

    def _standardize_data_format(self, data: pd.DataFrame, stock_info: StockInfo) -> pd.DataFrame:
        """标准化数据格式"""
        if data.empty:
            return data
            
        # 重置索引，将时间戳变为列
        df = data.reset_index()
        
        # 确保Date列存在
        if 'Date' not in df.columns and 'Datetime' in df.columns:
            df = df.rename(columns={'Datetime': 'Date'})
        
        # 标准化列名
        df.columns = [col.title() for col in df.columns]
        
        # 添加股票代码列
        df['Symbol'] = stock_info.code
        
        # 移除时区信息（如果存在）
        if df['Date'].dt.tz is not None:
            df['Date'] = df['Date'].dt.tz_localize(None)
        
        # 只保留必要的列
        required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Symbol']
        available_columns = [col for col in required_columns if col in df.columns]
        
        if len(available_columns) == len(required_columns):
            df = df[required_columns]
        else:
            missing_cols = set(required_columns) - set(available_columns)
            logger.warning(f"{stock_info.code}: 数据列不完整，缺少: {missing_cols}")
            return pd.DataFrame()
        
        # 数据清洗
        df = df.dropna(subset=['Open', 'High', 'Low', 'Close', 'Volume'])
        
        return df

    def request_new_data(self, stock_info: StockInfo, timeframe: TimeFrame, 
                        start_date: str, end_date: str) -> pd.DataFrame:
        """请求新的股票数据"""
        for attempt in range(self.max_retries):
            try:
                ticker = yf.Ticker(stock_info.code)
                
                # 根据时间周期选择获取方式
                if timeframe.yfinance_interval in ['1h', '30m', '15m', '5m']:
                    # 高频数据：使用period="max"然后过滤
                    data = ticker.history(
                        period="max",
                        interval=timeframe.yfinance_interval,
                        auto_adjust=True,  # 前复权
                        prepost=False,
                        repair=True
                    )
                    
                    # 过滤到指定日期范围
                    if start_date and not data.empty:
                        start_dt = pd.to_datetime(start_date)
                        # 处理时区一致性
                        if data.index.tz is not None and start_dt.tz is None:
                            tz_name = 'US/Eastern' if stock_info.market == Market.US else 'Asia/Shanghai'
                            start_dt = start_dt.tz_localize(tz_name)
                        elif data.index.tz is None and start_dt.tz is not None:
                            tz_name = 'US/Eastern' if stock_info.market == Market.US else 'Asia/Shanghai'
                            data.index = data.index.tz_localize(tz_name)
                        data = data[data.index >= start_dt]
                else:
                    # 日线/周线/月线：使用start/end日期
                    data = ticker.history(
                        start=start_date,
                        end=end_date,
                        interval=timeframe.yfinance_interval,
                        auto_adjust=True,  # 前复权
                        prepost=False,
                        repair=True
                    )
                
                if data.empty:
                    if attempt == self.max_retries - 1:
                        logger.warning(f"{stock_info.code}: 指定日期范围无数据")
                    else:
                        time.sleep(self.retry_delay)
                        continue
                    return pd.DataFrame()
                
                # 标准化数据格式
                return self._standardize_data_format(data, stock_info)
                
            except Exception as e:
                if attempt == self.max_retries - 1:
                    logger.error(f"{stock_info.code}: 获取数据失败 - {e}")
                    return pd.DataFrame()
                time.sleep(self.retry_delay)
        
        return pd.DataFrame()

    def download_history_data(self, stock_info: StockInfo, timeframe: TimeFrame, 
                          start_date: str = None, end_date: str = None) -> bool:
        """下载历史股票数据"""
        try:
            # 创建目录结构
            stock_dir = self._create_directory_structure(stock_info.market, stock_info.directory_code)
            file_path = os.path.join(stock_dir, f"{timeframe.file_suffix}.csv")
            
            # 下载新数据
            logger.info(f"请求 {stock_info.code} 的 {timeframe.file_suffix} 数据: {start_date} 到 {end_date}")
            new_df = self.request_new_data(stock_info, timeframe, start_date, end_date)
            if new_df.empty:
                logger.warning(f"未获取到 {stock_info.code} 的新数据")
                return False
            
            # 读取现有数据
            existing_df = None
            if os.path.exists(file_path):
                existing_df = pd.read_csv(file_path)
                
                # 合并数据
                original_count = len(existing_df)
                final_df = self.merge_new_data(existing_df, new_df)
                
                # 检查是否有新数据或更新
                if len(final_df) > original_count:
                    logger.info(f"新增 {len(final_df) - original_count} 条记录")
                else:
                    # 检查是否有数据被更新
                    existing_df['Date'] = pd.to_datetime(existing_df['Date'])
                    new_df['Date'] = pd.to_datetime(new_df['Date'])
                    
                    # 找出重叠的时间点
                    overlap_times = set(existing_df['Date']).intersection(set(new_df['Date']))
                    if overlap_times:
                        logger.info(f"更新了 {len(overlap_times)} 条现有记录")
                    else:
                        logger.info(f"{stock_info.code} 没有新的数据需要更新")
                        return True
            else:
                final_df = new_df
                logger.info(f"新文件，写入 {len(new_df)} 条记录")
            
            # 保存更新后的数据
            final_df.to_csv(file_path, index=False)
            logger.info(f"成功更新 {stock_info.code} 的 {timeframe.file_suffix} 数据，总计 {len(final_df)} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"下载数据时发生错误: {str(e)}")
            return False

    def load_stock_list(self, csv_path: str, market: Market) -> List[StockInfo]:
        """
        从CSV文件加载股票列表
        :param csv_path: CSV文件路径
        :param market: 市场类型
        :return: 股票信息列表
        """
        try:
            df = pd.read_csv(csv_path)
            stock_list = []
            for _, row in df.iterrows():
                code = row['Ticker']
                # A股特殊处理：确保使用正确的yfinance格式
                if market == Market.CN:
                    code = code.replace('.SH', '.SS')
                stock_list.append(StockInfo(code=code, name=row['Name'], market=market))
            return stock_list
        except Exception as e:
            logger.error(f"加载股票列表失败: {str(e)}")
            return []

def test_single_stock(stock_code: str, market: Market, start_date: str = None, end_date: str = None):
    """
    测试单只股票的数据下载
    
    :param stock_code: 股票代码
    :param market: 市场类型
    :param start_date: 开始日期
    :param end_date: 结束日期
    :return: 是否成功下载数据
    """
    downloader = YFinanceDownloader()
    stock_info = StockInfo(code=stock_code, name=stock_code, market=market)
    
    # 定义需要下载的时间周期
    timeframes = [
        TimeFrame.DAILY,    # 日线
        TimeFrame.HOUR_1,   # 1小时线
        TimeFrame.MIN_30,   # 30分钟线
        TimeFrame.MIN_15,   # 15分钟线
    ]
    
    success = True  # 标记是否成功下载所有数据
    
    try:
        # 下载每种类型的数据
        for timeframe in timeframes:
            logger.info(f"开始处理 {stock_info.code} 的 {timeframe.file_suffix} 数据")
            retry_count = 0
            max_retries = 3
            tf_success = False  # 标记当前时间周期是否成功下载
            
            while retry_count < max_retries and not tf_success:
                try:
                    # 检查是否需要更新
                    need_update, update_start, update_end = downloader._check_need_update(
                        stock_info, timeframe, start_date, end_date
                    )
                    
                    if not need_update:
                        logger.info(f"{stock_info.code} 的 {timeframe.file_suffix} 数据已是最新，跳过下载")
                        tf_success = True
                        break  # 跳出当前循环，处理下一个时间周期
                        
                    tf_success = downloader.download_history_data(
                        stock_info=stock_info,
                        timeframe=timeframe,
                        start_date=update_start,
                        end_date=update_end
                    )
                    
                    if tf_success:
                        time.sleep(0.5)  # 成功下载后短暂等待
                except Exception as e:
                    logger.error(f"下载失败，重试次数 {retry_count + 1}/{max_retries}: {str(e)}")
                    retry_count += 1
                    time.sleep(2 * (retry_count + 1))
            
            # 如果当前时间周期下载失败，标记整体下载失败
            if not tf_success:
                success = False
                logger.warning(f"{stock_info.code} 的 {timeframe.file_suffix} 数据下载失败")
            
            logger.info("--------")  # 添加分隔符
            
    except Exception as e:
        logger.error(f"处理 {stock_info.code} 时发生错误: {str(e)}")
        success = False
    
    logger.info(f"完成 {stock_info.code} 的数据下载，状态: {'成功' if success else '失败'}")
    return success

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='下载yfinance股票数据')
    parser.add_argument('--code', type=str, help='股票代码（如AAPL, 000001.SS）')
    parser.add_argument('--market', type=str, choices=['us', 'cn'], help='市场类型（us或cn）')
    parser.add_argument('--start', type=str, help='开始日期（YYYY-MM-DD格式）')
    parser.add_argument('--end', type=str, help='结束日期（YYYY-MM-DD格式，可选）')
    parser.add_argument('--all', type=str, choices=['us', 'cn'], help='下载指定市场的所有股票数据')
    parser.add_argument('--batch-size', type=int, default=50, help='批量处理的股票数量')
    
    args = parser.parse_args()
    
    if args.all:
        market = Market.US if args.all == 'us' else Market.CN
        downloader = YFinanceDownloader()
        csv_file = f'data/tickers_{args.all}.csv'
        stock_list = downloader.load_stock_list(csv_file, market)
        if not stock_list:
            logger.error(f"没有找到{args.all}市场的股票列表或列表为空")
            return

        total_stocks = len(stock_list)
        updated_count = 0
        skipped_count = 0
        error_count = 0
        
        # 分批处理股票列表
        batch_size = args.batch_size
        
        for batch_idx in range(0, total_stocks, batch_size):
            batch_end = min(batch_idx + batch_size, total_stocks)
            logger.info(f"开始处理第 {batch_idx//batch_size + 1} 批股票 ({batch_idx+1}-{batch_end}/{total_stocks})")
            
            # 处理当前批次的股票
            for i in range(batch_idx, batch_end):
                stock_info = stock_list[i]
                logger.info(f"开始处理第 {i+1}/{total_stocks} 只股票: {stock_info.code}")
                
                try:
                    # 下载数据
                    success = test_single_stock(
                        stock_code=stock_info.code,
                        market=market,
                        start_date=args.start,
                        end_date=args.end
                    )
                    
                    if success:
                        updated_count += 1
                    else:
                        error_count += 1
                        logger.warning(f"{stock_info.code} 数据下载失败")
                    
                    # 每只股票处理完后等待一小段时间
                    if i < batch_end - 1:
                        time.sleep(1)
                
                except Exception as e:
                    error_count += 1
                    logger.error(f"处理 {stock_info.code} 时发生错误: {str(e)}")
                    time.sleep(2)
            
            # 每批次处理完后暂停
            if batch_end < total_stocks:
                logger.info(f"第 {batch_idx//batch_size + 1} 批处理完成，暂停10秒后继续...")
                time.sleep(10)
        
        logger.info(f"数据下载完成！总计 {total_stocks} 只股票，"
                   f"更新 {updated_count} 只，跳过 {skipped_count} 只，失败 {error_count} 只")
    
    elif args.code and args.market:
        market = Market.US if args.market == 'us' else Market.CN
        test_single_stock(
            stock_code=args.code,
            market=market,
            start_date=args.start,
            end_date=args.end
        )
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
