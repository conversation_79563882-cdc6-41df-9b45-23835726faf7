#!/usr/bin/env python3
"""
BaoStock数据获取模块

支持获取A股的以下时间周期数据：
- 5分钟、15分钟、30分钟、60分钟
- 日线、周线、月线（计划中）

特点：
- 免费无限制访问
- 支持分钟级数据
- 数据质量良好
- 自动增量更新

作者: AI Assistant
日期: 2025-08-22
"""

import os
import sys
import logging
import argparse
import time
from pathlib import Path
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from typing import List, Optional, Dict, Any, Tuple
import pandas as pd
import baostock as bs
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class DataQualityMetrics:
    """数据质量指标"""
    total_records: int = 0
    valid_records: int = 0
    missing_data_days: int = 0
    data_completeness: float = 0.0
    first_date: Optional[str] = None
    last_date: Optional[str] = None
    has_volume: bool = False
    has_amount: bool = False

# BaoStock配置 - 与data.py完全对齐
BAOSTOCK_CONFIG = {
    'name': 'BaoStock',
    'description': 'BaoStock免费金融数据接口',
    'ticker_file': 'data/tickers/tickers_cn.csv',
    'supported_timeframes': ['1d', '1h', '1wk', '1mo', '15m'],  # 与data.py对齐
    'default_timeframes': ['1mo', '1wk', '1d', '1h', '15m'],  # 默认获取的时间周期
    'frequency_mapping': {
        '1d': 'd',      # 日线
        '1h': '60',     # 1小时（60分钟）
        '1wk': 'w',     # 周线
        '1mo': 'm',     # 月线
        '15m': '15'     # 15分钟
    },
    'data_limits_days': {
        '1d': 3650,     # 日线：约10年
        '1h': 730,      # 1小时：约2年  
        '15m': 60,      # 15分钟：约60天
        '1wk': 3650,    # 周线：约10年
        '1mo': 3650     # 月线：约10年
    },
    'rate_limits': {
        'requests_per_second': 5,  # 每秒请求数
        'batch_delay': 0.2,        # 批次间延迟
    },
    'retry_config': {
        'max_retries': 3,
        'retry_delay': 1.0,
    }
}

class BaoStockProvider:
    """BaoStock数据提供者"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.session_active = False
        self._initialize()
    
    def _initialize(self):
        """初始化BaoStock连接"""
        try:
            lg = bs.login()
            if lg.error_code == '0':
                self.session_active = True
                logger.info(f"✅ BaoStock登录成功")
            else:
                logger.error(f"❌ BaoStock登录失败: {lg.error_msg}")
                self.session_active = False
        except Exception as e:
            logger.error(f"❌ BaoStock初始化失败: {e}")
            self.session_active = False
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.session_active:
            bs.logout()
            self.session_active = False
            logger.info("BaoStock会话已关闭")
    
    def _convert_symbol(self, symbol: str) -> str:
        """转换股票代码格式：000001.SZ -> sz.000001"""
        if '.' in symbol:
            code, exchange = symbol.split('.')
            if exchange == 'SZ':
                return f'sz.{code}'
            elif exchange == 'SH':
                return f'sh.{code}'
        return symbol
    
    def _standardize_timeframe(self, timeframe: str) -> str:
        """标准化时间周期"""
        # 将15min等格式转换为标准格式
        return timeframe
    
    def fetch_data(self, symbol: str, timeframe: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """获取股票数据"""
        if not self.session_active:
            logger.error("BaoStock会话未激活")
            return None
        
        try:
            # 转换股票代码
            bs_symbol = self._convert_symbol(symbol)
            
            # 获取频率参数
            frequency = self.config['frequency_mapping'].get(timeframe)
            if not frequency:
                logger.warning(f"不支持的时间周期: {timeframe}")
                return None
            
            # 确定字段列表（分钟数据包含time，日线/周线/月线不包含）
            if timeframe in ['1h', '15m']:  # 分钟级数据
                fields = 'date,time,code,open,high,low,close,volume,amount'
            else:  # 日线、周线、月线
                fields = 'date,code,open,high,low,close,volume,amount'
            
            # 查询数据
            rs = bs.query_history_k_data_plus(
                bs_symbol,
                fields,
                start_date=start_date,
                end_date=end_date,
                frequency=frequency,
                adjustflag='2'  # 前复权
            )
            
            if rs.error_code != '0':
                logger.error(f"BaoStock查询失败 ({symbol}): {rs.error_msg}")
                return None
            
            # 解析数据
            data_list = []
            while rs.next():
                data_list.append(rs.get_row_data())
            
            if not data_list:
                logger.warning(f"无数据返回 ({symbol}, {timeframe})")
                return None
            
            # 创建DataFrame
            df = pd.DataFrame(data_list, columns=rs.fields)
            
            # 数据标准化
            df = self._standardize_data(df, timeframe, symbol)
            
            logger.info(f"✅ BaoStock获取成功: {symbol} ({timeframe}) - {len(df)} 条记录")
            return df
            
        except Exception as e:
            logger.error(f"❌ BaoStock获取失败 ({symbol}): {e}")
            return None
    
    def _standardize_data(self, df: pd.DataFrame, timeframe: str, symbol: str) -> pd.DataFrame:
        """标准化数据格式 - 与data.py完全对齐"""
        try:
            # 创建副本
            df = df.copy()
            
            # 处理时间戳
            if 'time' in df.columns:
                # 分钟数据：合并date和time
                # 修复时间格式 - BaoStock时间格式：20250821094500000
                df['time_str'] = df['time'].astype(str).str[:12]  # 取前12位：202508210945
                df['formatted_time'] = df['time_str'].str[:8] + ' ' + df['time_str'].str[8:10] + ':' + df['time_str'].str[10:12] + ':00'
                df['timestamp'] = pd.to_datetime(df['formatted_time'], format='%Y%m%d %H:%M:%S')
                df.drop(['date', 'time', 'time_str', 'formatted_time'], axis=1, inplace=True)
            else:
                # 日线/周线/月线数据：只有date
                df['timestamp'] = pd.to_datetime(df['date'], format='%Y-%m-%d')
                df.drop(['date'], axis=1, inplace=True)
            
            # 移除BaoStock的code列
            if 'code' in df.columns:
                df.drop(['code'], axis=1, inplace=True)
            
            # 转换数值列
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 添加symbol列 - 与data.py对齐
            df['symbol'] = symbol
            
            # 按时间排序
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            # 基本数据验证
            df = df.dropna(subset=['open', 'high', 'low', 'close'])
            df = df[df['open'] > 0]
            
            # 确保列顺序与data.py一致：timestamp, open, high, low, close, volume, amount, symbol
            standard_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'amount', 'symbol']
            available_columns = [col for col in standard_columns if col in df.columns]
            df = df[available_columns]
            
            logger.debug(f"数据标准化完成: {len(df)} 条有效记录")
            return df
            
        except Exception as e:
            logger.error(f"数据标准化失败: {e}")
            return pd.DataFrame()

class DataManager:
    """数据管理器"""
    
    def __init__(self, timeframe: str):
        self.timeframe = timeframe
        self.data_dir = Path(os.getenv('DATA_DIR', 'data'))
        self.config = BAOSTOCK_CONFIG
        
        # 确保目录存在
        cn_dir = self.data_dir / 'cn'
        cn_dir.mkdir(parents=True, exist_ok=True)
    
    def load_stock_list(self) -> List[str]:
        """加载股票列表"""
        try:
            ticker_file = Path(self.config['ticker_file'])
            df = pd.read_csv(ticker_file)
            
            # 数据清理
            df = df.drop_duplicates(subset=['Ticker'])
            df = df.dropna(subset=['Ticker'])
            
            stock_list = df['Ticker'].tolist()
            logger.info(f"加载股票列表: {len(stock_list)} 只股票")
            return stock_list
            
        except Exception as e:
            logger.error(f"加载股票列表失败: {e}")
            return []
    
    def check_existing_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """检查本地数据状态"""
        data_file = self.data_dir / 'cn' / symbol / f"{self.timeframe}.parquet"
        
        if not data_file.exists():
            return None
        
        try:
            df = pd.read_parquet(data_file)
            if df.empty:
                return None
            
            # 确保timestamp列是datetime类型
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            return {
                'symbol': symbol,
                'timeframe': self.timeframe,
                'data_points': len(df),
                'start_date': df['timestamp'].min(),
                'end_date': df['timestamp'].max(),
                'file_path': data_file
            }
        except Exception as e:
            logger.warning(f"检查数据状态失败 ({symbol}): {e}")
            return None
    
    def merge_and_save_data(self, symbol: str, new_data: pd.DataFrame) -> bool:
        """合并并保存数据"""
        try:
            symbol_dir = self.data_dir / 'cn' / symbol
            symbol_dir.mkdir(parents=True, exist_ok=True)
            
            output_file = symbol_dir / f"{self.timeframe}.parquet"
            
            if output_file.exists():
                # 合并数据
                existing_df = pd.read_parquet(output_file)
                combined_df = pd.concat([existing_df, new_data], ignore_index=True)
                
                # 去重并排序
                combined_df = combined_df.drop_duplicates(subset=['timestamp'], keep='last')
                combined_df = combined_df.sort_values('timestamp').reset_index(drop=True)
                
                logger.info(f"数据合并: {symbol} 新增{len(new_data)}条，总计{len(combined_df)}条")
            else:
                combined_df = new_data
                logger.info(f"新建数据文件: {symbol} {len(combined_df)}条记录")
            
            # 保存数据
            combined_df.to_parquet(output_file, index=False)
            return True
            
        except Exception as e:
            logger.error(f"保存数据失败 ({symbol}): {e}")
            return False
    
    def calculate_metrics(self, df: pd.DataFrame) -> DataQualityMetrics:
        """计算数据质量指标"""
        if df.empty:
            return DataQualityMetrics()
        
        metrics = DataQualityMetrics()
        metrics.total_records = len(df)
        metrics.valid_records = len(df.dropna(subset=['open', 'high', 'low', 'close']))
        metrics.data_completeness = metrics.valid_records / metrics.total_records if metrics.total_records > 0 else 0
        metrics.first_date = df['timestamp'].min().strftime('%Y-%m-%d %H:%M:%S')
        metrics.last_date = df['timestamp'].max().strftime('%Y-%m-%d %H:%M:%S')
        metrics.has_volume = 'volume' in df.columns and df['volume'].notna().any()
        metrics.has_amount = 'amount' in df.columns and df['amount'].notna().any()
        
        return metrics
    
    def determine_update_strategy(self, symbol: str) -> Tuple[Optional[str], Optional[str], str]:
        """确定更新策略 - 参考data.py的最佳实践"""
        existing_data = self.check_existing_data(symbol)
        today = datetime.now().strftime('%Y-%m-%d')
        
        if existing_data is None:
            # 全量下载 - 根据时间周期和数据限制确定起始日期
            start_date = '2015-01-01'  # 默认起始日期
            
            # 应用数据限制（如果有）
            if self.timeframe in self.config['data_limits_days']:
                limit_days = self.config['data_limits_days'][self.timeframe]
                calculated_start_date = (datetime.now() - timedelta(days=limit_days)).strftime('%Y-%m-%d')
                start_date = max(calculated_start_date, start_date)
            
            return start_date, today, "full_download"
        
        last_date = existing_data['end_date'].strftime('%Y-%m-%d')
        
        # 增量更新逻辑
        if self.timeframe in ['1d', '1wk', '1mo']:
            # 日线/周线/月线数据：简单日期比较
            if last_date >= today:
                return None, None, "up_to_date"
            # 从下一天开始增量更新
            start_date = (existing_data['end_date'] + timedelta(days=1)).strftime('%Y-%m-%d')
        else:
            # 分钟级数据（1h, 15m）：更精细的检查
            if last_date > today:
                return None, None, "up_to_date"
            elif last_date == today:
                # 检查当日数据完整性 - 简化版本，如果是今天的数据，重新下载当天确保完整
                prev_day = existing_data['end_date'] - timedelta(days=1)
                start_date = prev_day.strftime('%Y-%m-%d')
            else:
                # 从现有数据的下一天开始
                start_date = (existing_data['end_date'] + timedelta(days=1)).strftime('%Y-%m-%d')
        
        return start_date, today, "incremental_update"
    
    def validate_data_quality(self, df: pd.DataFrame, symbol: str, timeframe: str = None) -> bool:
        """验证数据质量 - 参考data.py的验证逻辑"""
        if df.empty:
            logger.warning(f"{symbol}: 数据为空")
            return False
        
        # 检查必要列
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.warning(f"{symbol}: 缺少必要列: {missing_columns}")
            return False
        
        # 检查价格合理性
        price_columns = ['open', 'high', 'low', 'close']
        if (df[price_columns] <= 0).any().any():
            logger.warning(f"{symbol}: 发现非正价格")
            return False
        
        # 检查OHLC逻辑
        if (df['high'] < df['low']).any():
            logger.warning(f"{symbol}: 发现高价<低价异常")
            return False
        
        # 检查OHLC范围合理性
        ohlc_invalid = (
            (df['open'] < df['low']) | (df['open'] > df['high']) |
            (df['close'] < df['low']) | (df['close'] > df['high'])
        ).sum()
        
        if ohlc_invalid > len(df) * 0.05:  # 超过5%的数据异常
            logger.warning(f"{symbol}: 发现{ohlc_invalid}条OHLC异常，占比{ohlc_invalid/len(df)*100:.1f}%")
            return False
        
        # 检查数据连续性（针对股市数据优化）
        if timeframe:  # 只有提供timeframe时才检查
            time_gaps = df['timestamp'].diff().dt.total_seconds().dropna()
            if timeframe in ['1h', '15m']:
                # 分钟数据：检查极端异常间隔（考虑股市交易时间特征）
                if timeframe == '1h':
                    # 1小时数据：超过5天的间隔才认为异常（排除周末节假日）
                    max_normal_gap = 5 * 24 * 60 * 60  # 5天
                else:  # 15m
                    # 15分钟数据：超过5天的间隔才认为异常
                    max_normal_gap = 5 * 24 * 60 * 60  # 5天
                
                extreme_gaps = (time_gaps > max_normal_gap).sum()
                if extreme_gaps > 0:
                    logger.warning(f"{symbol}: 发现{extreme_gaps}个极端长间隔(>5天)，可能存在数据缺失")
        
        logger.debug(f"{symbol}: 数据质量验证通过 - {len(df)} 条记录")
        return True

    def update_single_stock(self, symbol: str, provider: BaoStockProvider) -> Dict[str, Any]:
        """更新单只股票数据 - 使用优化的更新策略"""
        result = {
            'symbol': symbol,
            'success': False,
            'status': 'pending',
            'records_added': 0,
            'total_records': 0,
            'error': None
        }
        
        try:
            # 确定更新策略
            start_date, end_date, strategy = self.determine_update_strategy(symbol)
            
            if strategy == "up_to_date":
                existing_data = self.check_existing_data(symbol)
                result.update({
                    'success': True,
                    'status': 'up_to_date',
                    'total_records': existing_data['data_points'] if existing_data else 0
                })
                return result
            
            # 获取新数据
            new_data = provider.fetch_data(symbol, self.timeframe, start_date, end_date)
            
            if new_data is None or new_data.empty:
                existing_data = self.check_existing_data(symbol)
                result.update({
                    'success': True,
                    'status': 'no_new_data',
                    'total_records': existing_data['data_points'] if existing_data else 0
                })
                return result
            
            # 数据质量验证
            if not self.validate_data_quality(new_data, symbol, self.timeframe):
                result.update({
                    'success': False,
                    'status': 'quality_failed',
                    'error': '数据质量验证失败'
                })
                return result
            
            # 保存数据
            existing_data = self.check_existing_data(symbol)
            existing_count = existing_data['data_points'] if existing_data else 0
            
            if self.merge_and_save_data(symbol, new_data):
                result.update({
                    'success': True,
                    'status': 'updated' if strategy == 'incremental_update' else 'full_download',
                    'records_added': len(new_data),
                    'total_records': existing_count + len(new_data)
                })
                
                logger.info(f"✅ {symbol} ({self.timeframe}): {strategy} - 新增{len(new_data)}条记录")
            else:
                result.update({
                    'success': False,
                    'status': 'save_failed',
                    'error': '数据保存失败'
                })
            
        except Exception as e:
            result.update({
                'success': False,
                'status': 'error',
                'error': str(e)
            })
            logger.error(f"❌ {symbol} ({self.timeframe}): {str(e)}")
        
        return result
    
    def process_batch(self, stock_list: List[str], max_workers: int = 5) -> Dict[str, Any]:
        """批量处理股票数据"""
        logger.info(f"开始批量处理: {len(stock_list)} 只股票 ({self.timeframe})")
        
        results = []
        success_count = 0
        updated_count = 0
        
        with BaoStockProvider(self.config) as provider:
            if not provider.session_active:
                logger.error("BaoStock连接失败，无法继续")
                return {'success': False, 'error': 'BaoStock连接失败'}
            
            # 处理股票（由于BaoStock的限制，建议不要使用多线程）
            for i, symbol in enumerate(stock_list, 1):
                if i % 10 == 0:  # 每10只股票输出一次进度
                    logger.info(f"进度: {i}/{len(stock_list)}")
                
                result = self.update_single_stock(symbol, provider)
                results.append(result)
                
                if result['success']:
                    success_count += 1
                    if result['status'] == 'updated':
                        updated_count += 1
                
                # 添加延迟避免请求过频
                time.sleep(self.config['rate_limits']['batch_delay'])
        
        success_rate = success_count / len(stock_list) * 100 if stock_list else 0
        
        return {
            'success': True,
            'total_stocks': len(stock_list),
            'success_count': success_count,
            'updated_count': updated_count,
            'success_rate': success_rate,
            'results': results
        }
    
    def generate_summary_report(self, batch_result: Dict[str, Any]) -> None:
        """生成汇总报告"""
        try:
            cn_dir = self.data_dir / 'cn'
            
            # 统计本地文件
            local_count = 0
            if cn_dir.exists():
                for symbol_dir in cn_dir.iterdir():
                    if symbol_dir.is_dir():
                        data_file = symbol_dir / f"{self.timeframe}.parquet"
                        if data_file.exists():
                            local_count += 1
            
            # 生成报告
            report = {
                'data_source': 'BaoStock',
                'generation_time': datetime.now().isoformat(),
                'timeframe': self.timeframe,
                'storage_path': f"data/cn/{{symbol}}/{self.timeframe}.parquet",
                'batch_summary': {
                    'total_requested': batch_result.get('total_stocks', 0),
                    'successfully_processed': batch_result.get('success_count', 0),
                    'newly_updated': batch_result.get('updated_count', 0),
                    'success_rate_percent': round(batch_result.get('success_rate', 0), 2)
                },
                'local_storage': {
                    'total_files': local_count,
                    'storage_format': 'parquet',
                    'directory_structure': 'data/cn/{symbol}/{timeframe}.parquet'
                },
                'data_quality': {
                    'provider': 'BaoStock',
                    'adjustment': 'qfq (前复权)',
                    'includes_volume': True,
                    'includes_amount': True,
                    'data_frequency': self.timeframe
                }
            }
            
            # 保存报告
            report_file = cn_dir / f"data_quality_report_baostock_{self.timeframe.replace('min', 'm')}.json"
            import json
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📊 汇总报告已生成: {report_file}")
            
        except Exception as e:
            logger.error(f"生成汇总报告失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='BaoStock A股数据获取工具')
    parser.add_argument('--timeframe', type=str, 
                       choices=BAOSTOCK_CONFIG['supported_timeframes'],
                       help='单个时间周期')
    parser.add_argument('--timeframes', nargs='+', 
                       choices=BAOSTOCK_CONFIG['supported_timeframes'],
                       help='多个时间周期')
    parser.add_argument('--max-stocks', type=int, default=None,
                       help='限制处理的股票数量（用于测试）')
    parser.add_argument('--quiet', action='store_true',
                       help='静默模式（减少输出）')
    
    args = parser.parse_args()
    
    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)
    
    # 确定要处理的时间周期 - 默认使用所有支持的时间周期
    if args.timeframes:
        timeframes = args.timeframes
    elif args.timeframe:
        timeframes = [args.timeframe]
    else:
        # 默认获取所有时间周期：1mo 1wk 1d 1h 15m
        timeframes = BAOSTOCK_CONFIG['default_timeframes']
        print(f"🚀 默认模式: 获取所有时间周期数据 {timeframes}")
    
    # 验证分钟数据提示
    minute_timeframes = [tf for tf in timeframes if tf in ['1h', '15m']]
    if minute_timeframes:
        print(f"🎉 使用BaoStock获取分钟数据: {', '.join(minute_timeframes)}")
        print("✅ BaoStock优势: 免费、无限制、支持多种分钟级别数据")
    
    # 处理每个时间周期
    for timeframe in timeframes:
        print(f"\n{'='*50}")
        print(f"开始处理时间周期: {timeframe}")
        print(f"{'='*50}")
        
        manager = DataManager(timeframe)
        stock_list = manager.load_stock_list()
        
        if not stock_list:
            print(f"❌ 无法加载股票列表")
            continue
        
        # 限制股票数量（测试用）
        if args.max_stocks:
            stock_list = stock_list[:args.max_stocks]
            print(f"🧪 测试模式: 仅处理前 {len(stock_list)} 只股票")
        
        # 批量处理
        batch_result = manager.process_batch(stock_list)
        
        if batch_result['success']:
            print(f"\n✅ {timeframe} 处理完成:")
            print(f"   总计: {batch_result['total_stocks']} 只股票")
            print(f"   成功: {batch_result['success_count']} 只")
            print(f"   更新: {batch_result['updated_count']} 只")
            print(f"   成功率: {batch_result['success_rate']:.1f}%")
            
            # 生成报告
            manager.generate_summary_report(batch_result)
        else:
            print(f"❌ {timeframe} 处理失败: {batch_result.get('error', '未知错误')}")
    
    print(f"\n🎉 BaoStock数据获取完成！")

if __name__ == "__main__":
    main()
