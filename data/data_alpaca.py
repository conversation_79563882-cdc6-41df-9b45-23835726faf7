#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
from datetime import datetime, timedelta, timezone
import pandas as pd
import numpy as np
import alpaca_trade_api as tradeapi
import logging
from typing import List, Tuple, Optional, Dict, Union
import argparse
from enum import Enum
from alpaca_trade_api.rest import TimeFrame, TimeFrameUnit
import pytz

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Alpaca API配置
API_KEY = "AK01WHNHFBADM1OJ8QAP"
SECRET_KEY = "tMXJGIk6ot3jM5duIEHA4z2hR9Eee2NObw3Uwyr1"
BASE_URL = "https://paper-api.alpaca.markets"  # 使用paper trading API，可以根据需要修改

# 市场类型
class MarketType(Enum):
    """市场类型枚举"""
    STOCK = "stock"      # 美股
    CRYPTO = "crypto"    # 加密货币

class KLType(Enum):
    """K线类型枚举"""
    DAILY = "daily"
    HOUR_1 = "1h"
    MIN_30 = "30min"
    MIN_15 = "15min"
    MIN_5 = "5min"

    def __init__(self, timeframe):
        self.timeframe = timeframe
        self.file_suffix = timeframe.lower()
        # 添加 Alpaca API 所需的时间框架格式
        self.api_timeframe = {
            "daily": "1D",
            "1h": "1H",
            "30min": "30Min",
            "15min": "15Min",
            "5min": "5Min"
        }[timeframe]
        
    def get_crypto_timeframe(self):
        """获取加密货币API使用的时间框架格式"""
        return self.api_timeframe

class AssetInfo:
    """资产信息基类"""
    def __init__(self, code: str, name: str, market_type: MarketType):
        self.code = code
        self.name = name
        self.market_type = market_type
        self.pure_code = code

    @property
    def directory_code(self) -> str:
        return self.pure_code
        
    @property
    def is_crypto(self) -> bool:
        return self.market_type == MarketType.CRYPTO
        
    @property 
    def is_stock(self) -> bool:
        return self.market_type == MarketType.STOCK

class StockInfo(AssetInfo):
    """股票信息类"""
    def __init__(self, code: str, name: str):
        super().__init__(code, name, MarketType.STOCK)

class CryptoInfo(AssetInfo):
    """加密货币信息类"""
    def __init__(self, code: str, name: str):
        # 将BTCUSD格式转换为BTC/USD格式
        if '/' not in code:
            self.currency = code.replace('USD', '')
            self.formatted_code = f"{self.currency}/USD"
            self.original_code = code
        else:
            self.currency = code.split('/')[0]
            self.formatted_code = code
            self.original_code = f"{self.currency}USD"
            
        super().__init__(self.formatted_code, name, MarketType.CRYPTO)
        self.pure_code = self.currency
        
    @property
    def directory_code(self) -> str:
        # 返回原始的ticker格式，如BTCUSD
        return self.original_code

class AlpacaDataDownloader:
    def __init__(self):
        """初始化AlpacaDataDownloader"""
        self.api = None
        self.base_path = {
            MarketType.STOCK: "Data/US",
            MarketType.CRYPTO: "Data/Crypto"
        }
        self.last_request_time = 0
        self.request_count = 0
        self._connected = False

    def connect(self):
        """建立与Alpaca API的连接"""
        try:
            if self.api is None:
                self.api = tradeapi.REST(
                    key_id=API_KEY,
                    secret_key=SECRET_KEY,
                    base_url=BASE_URL,
                    api_version='v2'
                )
                self._connected = True
                logger.info("成功连接到Alpaca API")
            return True
        except Exception as e:
            logger.error(f"连接Alpaca API失败: {str(e)}")
            self._connected = False
            return False

    def _create_directory_structure(self, asset_info: AssetInfo):
        """创建数据存储目录结构"""
        base_dir = self.base_path[asset_info.market_type]
        asset_dir = os.path.join(base_dir, asset_info.directory_code)
        os.makedirs(asset_dir, exist_ok=True)
        return asset_dir

    def _get_last_record_date(self, file_path: str) -> Tuple[Optional[str], Optional[str]]:
        """获取数据文件中的最后一条记录时间"""
        try:
            if os.path.exists(file_path):
                df = pd.read_csv(file_path)
                if not df.empty:
                    time_col = 'time_key' if 'time_key' in df.columns else 'timestamp'
                    df[time_col] = pd.to_datetime(df[time_col])
                    last_time = df[time_col].max()
                    
                    # 获取最后记录日期的前一天作为请求的开始时间
                    # 这样可以确保获取完整的交易时段数据，并与已有数据有一天的重叠
                    # 参考futu_data.py的逻辑
                    request_start = (last_time.date() - timedelta(days=1)).strftime('%Y-%m-%d')
                    logger.info(f"本地最新数据时间: {last_time}, 将从 {request_start} 开始更新")
                    return last_time.strftime('%Y-%m-%d %H:%M:%S'), request_start
            return None, None
        except Exception as e:
            logger.error(f"读取文件{file_path}最后记录时间时出错: {str(e)}")
            return None, None

    def _transform_data(self, df: pd.DataFrame, asset_info: AssetInfo, kl_type: KLType = None) -> pd.DataFrame:
        """转换Alpaca数据格式为统一格式"""
        if df is None or df.empty:
            return pd.DataFrame()

        # 重置索引，将timestamp作为列
        df = df.reset_index()
        
        # 确保timestamp列存在
        timestamp_col = 'timestamp' if 'timestamp' in df.columns else 'time_key'
        
        # 重命名列以匹配统一格式
        renamed_cols = {
            timestamp_col: 'time_key',
            'trade_count': 'turnover',
            'vwap': 'turnover_rate'
        }
        # 仅重命名存在的列
        rename_dict = {k: v for k, v in renamed_cols.items() if k in df.columns}
        df = df.rename(columns=rename_dict)

        # 确保时间列是datetime类型，并转换为美东时间实际交易时间
        # 首先确保时间有时区信息（如果没有则假设是UTC）
        if df['time_key'].dt.tz is None:
            df['time_key'] = pd.to_datetime(df['time_key'], utc=True)
        
        # 对于日线数据，将04:00:00的UTC时间调整为16:00:00的美东收盘时间
        # 这里假设日线数据的时间戳是0400，需要转换为东部时间的1600
        if kl_type == KLType.DAILY:
            # 保持日期不变，仅修改时间部分为收盘时间16:00
            df['time_key'] = df['time_key'].apply(
                lambda x: x.replace(hour=16, minute=0, second=0)
            )
        else:
            # 对于小时和分钟数据，转换为美东时间（去除时区信息之前）
            eastern = pytz.timezone('US/Eastern')
            df['time_key'] = df['time_key'].dt.tz_convert(eastern)
        
        # 去除时区信息，保持datetime格式
        df['time_key'] = df['time_key'].dt.tz_localize(None)
        
        # 先按时间排序并去重，确保数据的连续性
        df = df.sort_values('time_key')
        df = df.drop_duplicates(subset=['time_key'], keep='first')

        # 添加必要的列
        # 对于加密货币，使用原始的ticker格式
        if asset_info.is_crypto and hasattr(asset_info, 'original_code'):
            df['code'] = asset_info.original_code
        else:
            df['code'] = asset_info.code
            
        df['name'] = asset_info.name
        
        # 计算last_close和change_rate
        df['last_close'] = df['close'].shift(1)
        df['change_rate'] = 0.0  # 初始化为0
        
        # 避免除以0错误
        mask = (df['last_close'].notna()) & (df['last_close'] != 0)
        if mask.any():
            df.loc[mask, 'change_rate'] = (df.loc[mask, 'close'] - df.loc[mask, 'last_close']) / df.loc[mask, 'last_close'] * 100
        
        # 添加其他必要的列
        df['pe_ratio'] = 0.0
        
        # 如果缺少某些列，添加默认值
        for col in ['volume', 'turnover', 'turnover_rate']:
            if col not in df.columns:
                df[col] = 0.0

        # 确保列顺序一致
        columns = [
            'code', 'name', 'time_key', 'open', 'close', 'high', 'low',
            'pe_ratio', 'turnover_rate', 'volume', 'turnover',
            'change_rate', 'last_close'
        ]
        
        # 只保留需要的列
        df = df[columns]

        return df

    def _merge_data(self, existing_df: pd.DataFrame, new_df: pd.DataFrame) -> pd.DataFrame:
        """合并新旧数据并确保不重复"""
        if existing_df is None or existing_df.empty:
            return new_df

        # 确保时间列是datetime类型
        # 新数据已经在_transform_data中处理过，这里只需要处理已有数据
        existing_df['time_key'] = pd.to_datetime(existing_df['time_key'])

        # 合并数据
        merged_df = pd.concat([existing_df, new_df])
        
        # 按时间排序并去重，保留最新的数据
        merged_df = merged_df.sort_values('time_key')
        merged_df = merged_df.drop_duplicates(subset=['time_key'], keep='last')

        # 重新计算change_rate和last_close
        merged_df['last_close'] = merged_df['close'].shift(1)
        merged_df['change_rate'] = 0.0
        mask = (merged_df['last_close'].notna()) & (merged_df['last_close'] != 0)
        if mask.any():
            merged_df.loc[mask, 'change_rate'] = (merged_df.loc[mask, 'close'] - merged_df.loc[mask, 'last_close']) / merged_df.loc[mask, 'last_close'] * 100

        return merged_df

    def _wait_for_rate_limit(self):
        """控制请求频率"""
        current_time = time.time()
        
        if current_time - self.last_request_time >= 60:
            self.request_count = 0
            self.last_request_time = current_time

        if self.request_count >= 180:
            wait_time = 60 - (current_time - self.last_request_time)
            if wait_time > 0:
                logger.info(f"达到频率限制，等待 {wait_time:.2f} 秒")
                time.sleep(wait_time)
                self.request_count = 0
                self.last_request_time = time.time()

        elapsed = current_time - self.last_request_time
        if elapsed < 0.2:
            time.sleep(0.2 - elapsed)
        
        self.request_count += 1
        self.last_request_time = time.time()

    def _check_need_update(self, asset_info: AssetInfo, kl_type: KLType, 
                      start_date: str = None, end_date: str = None) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        检查是否需要更新数据
        :param asset_info: 资产信息
        :param kl_type: K线类型
        :param start_date: 开始日期（可选）
        :param end_date: 结束日期（可选）
        :return: (是否需要更新, 请求开始日期, 请求结束日期)
        """
        try:
            # 创建目录结构
            asset_dir = self._create_directory_structure(asset_info)
            file_path = os.path.join(asset_dir, f"{kl_type.file_suffix}.csv")
            
            # 如果没有指定结束日期，使用当前日期减去30分钟（避免Alpaca API的15分钟限制）
            if not end_date:
                end_date_obj = datetime.now(timezone.utc) - timedelta(minutes=30)
                # 使用日期+时间格式，确保包含当天的数据
                end_date = end_date_obj.strftime('%Y-%m-%d %H:%M:%S')
            
            # 如果文件不存在，需要从头开始下载
            if not os.path.exists(file_path):
                logger.info(f"{asset_info.code} 的 {kl_type.value} 数据文件不存在，需要创建")
                # 默认从2019年开始下载
                request_start = start_date or "2019-01-01"
                return True, request_start, end_date
            
            # 获取最后记录的日期
            last_record_date, request_start = self._get_last_record_date(file_path)
            if not last_record_date:
                logger.warning(f"{asset_info.code} 的 {kl_type.value} 数据文件存在但无法读取最后日期，将重新下载")
                request_start = start_date or "2019-01-01"
                return True, request_start, end_date
            
            # 如果用户指定了开始日期，使用用户指定的
            if start_date:
                request_start = start_date
            
            # 简单逻辑：始终尝试更新数据，从最后记录日期的前一天开始
            # 这样可以确保数据连续性，并有一天的重叠作为安全余量
            return True, request_start, end_date
                
        except Exception as e:
            logger.error(f"检查更新状态时发生错误: {str(e)}")
            return True, start_date or "2019-01-01", end_date

    def download_data(self, asset_info: AssetInfo, kl_type: KLType,
                    start_date: str = None, end_date: str = None) -> bool:
        """
        下载特定资产和K线类型的数据
        :param asset_info: 资产信息
        :param kl_type: K线类型
        :param start_date: 开始日期（可选）
        :param end_date: 结束日期（可选）
        :return: 是否成功
        """
        if not self._connected and not self.connect():
            return False
            
        # 检查是否需要更新
        need_update, request_start, request_end = self._check_need_update(
            asset_info, kl_type, start_date, end_date
        )
        if not need_update:
            logger.info(f"{asset_info.code} 的 {kl_type.value} 数据已是最新，无需更新")
            return True
            
        # 获取API格式的时间框架
        timeframe = kl_type.get_crypto_timeframe() if asset_info.is_crypto else kl_type.api_timeframe
        
        logger.info(f"请求 {asset_info.code} 的 {kl_type.value} 数据: {request_start} 到 {request_end}")
        
        # 创建目录结构
        asset_dir = self._create_directory_structure(asset_info)
        file_path = os.path.join(asset_dir, f"{kl_type.file_suffix}.csv")
        
        # 读取现有数据（如果有）
        existing_data = None
        if os.path.exists(file_path):
            try:
                existing_data = pd.read_csv(file_path)
            except Exception as e:
                logger.error(f"读取已有数据文件出错: {str(e)}")
                
        # 添加时区信息，确保符合RFC3339格式
        start_dt = pd.to_datetime(request_start).replace(tzinfo=timezone.utc)
        
        # 确保结束时间至少比当前时间早30分钟，避免Alpaca API的15分钟限制
        end_dt = pd.to_datetime(request_end).replace(tzinfo=timezone.utc)
        current_time = datetime.now(timezone.utc)
        min_end_time = current_time - timedelta(minutes=30)
        
        # 如果结束日期只包含日期部分（没有时间），则设置为当天的23:59:59
        if end_dt.hour == 0 and end_dt.minute == 0 and end_dt.second == 0:
            end_dt = end_dt.replace(hour=23, minute=59, second=59)
            logger.info(f"调整结束时间为当天结束: {end_dt.isoformat()}")
            
        if end_dt > min_end_time:
            end_dt = min_end_time
            logger.info(f"调整结束时间以避免Alpaca API的15分钟限制: {end_dt.isoformat()}")
        
        # 格式化为RFC3339格式
        formatted_start = start_dt.isoformat()
        formatted_end = end_dt.isoformat()
        
        # 分页请求数据
        all_data = []
        page_count = 0
        
        while True:
            page_count += 1
            try:
                # 等待以符合频率限制
                self._wait_for_rate_limit()
                
                logger.info(f"获取第{page_count}页: {formatted_start} 到 {formatted_end}")
                
                # 根据资产类型选择不同的API调用方式
                if asset_info.is_stock:
                    # 股票数据
                    bars = self.api.get_bars(
                        symbol=asset_info.code,
                        timeframe=timeframe,
                        start=formatted_start,
                        end=formatted_end,
                        adjustment='all',  # 使用全部调整（股息和分割）的前复权数据
                        limit=10000  # 使用最大限制
                    ).df
                else:
                    # 加密货币数据
                    bars = self.api.get_crypto_bars(
                        symbol=asset_info.code,
                        timeframe=timeframe,
                        start=formatted_start,
                        end=formatted_end,
                        limit=10000  # 使用最大限制
                    ).df
                
                if not bars.empty:
                    all_data.append(bars)
                    logger.info(f"获取到 {len(bars)} 条记录")
                    
                    # 如果获取的数据量接近限制，需要分页
                    if len(bars) >= 9900:  # 接近10000的限制
                        # 使用最后一条记录的时间作为下一页的开始时间
                        last_time = bars.index[-1]
                        # 确保时间戳有时区信息
                        if last_time.tzinfo is None:
                            last_time = last_time.replace(tzinfo=timezone.utc)
                        # 向前移动一秒，避免重复
                        next_start = (last_time + timedelta(seconds=1))
                        formatted_start = next_start.isoformat()
                        logger.info(f"数据量接近限制，下一页从 {formatted_start} 开始")
                    else:
                        # 如果数据量不接近限制，说明已经获取完所有数据
                        break
                else:
                    # 如果没有获取到数据，可能是非交易日或数据不可用
                    logger.info(f"没有更多数据")
                    break
                    
            except Exception as e:
                logger.error(f"获取数据失败: {str(e)}")
                # 如果是第一页就失败，返回失败
                if page_count == 1 and not all_data:
                    return False
                # 否则使用已获取的数据继续处理
                break
        
        # 合并所有数据
        if not all_data:
            logger.warning(f"未获取到 {asset_info.code} 的数据")
            return True  # 返回True表示处理完成，避免无限循环
        
        df = pd.concat(all_data) if len(all_data) > 1 else all_data[0]
        
        # 转换数据格式
        df = self._transform_data(df, asset_info, kl_type)
        
        # 读取现有数据并合并
        existing_df = None
        if os.path.exists(file_path):
            existing_df = pd.read_csv(file_path)
            
            # 合并数据
            final_df = self._merge_data(existing_df, df)
            logger.info(f"合并后共有 {len(final_df)} 条记录")
        else:
            final_df = df
            logger.info(f"新文件，写入 {len(df)} 条记录")
        
        # 保存数据
        final_df.to_csv(file_path, index=False)
        logger.info(f"成功更新 {asset_info.code} 的 {kl_type.value} 数据")
        return True

def test_single_stock(stock_code: str, start_date: str = None, end_date: str = None):
    """测试单只股票的数据下载"""
    downloader = AlpacaDataDownloader()
    stock_info = StockInfo(code=stock_code, name=stock_code)
    
    kl_types = [
        KLType.DAILY,
        KLType.HOUR_1,
        KLType.MIN_30,
        KLType.MIN_15,
        KLType.MIN_5
    ]
    
    try:
        if not downloader.connect():
            logger.error("连接Alpaca API失败")
            return False
            
        for kl_type in kl_types:
            logger.info(f"开始处理 {stock_info.code} 的 {kl_type.value} 数据")
            success = downloader.download_data(
                asset_info=stock_info,
                kl_type=kl_type,
                start_date=start_date,
                end_date=end_date
            )
            if not success:
                logger.warning(f"处理 {stock_info.code} 的 {kl_type.value} 数据失败")
            logger.info("--------")  # 添加分隔线，使日志输出更清晰
            time.sleep(1)  # 添加延迟，避免API请求过于频繁
            
    finally:
        logger.info(f"完成 {stock_info.code} 的数据下载")

def load_stock_list(csv_path: str) -> List[StockInfo]:
    """从CSV文件加载股票列表
    CSV格式示例：
    Ticker,Name
    AAPL,"Apple Inc."
    NVDA,"NVIDIA Corporation"
    """
    try:
        if not os.path.exists(csv_path):
            logger.error(f"找不到股票列表文件: {csv_path}")
            return []
            
        # 读取CSV文件，自动处理带引号的字段
        df = pd.read_csv(csv_path)
        
        if df.empty:
            logger.error("股票列表文件为空")
            return []
            
        if 'Ticker' not in df.columns or 'Name' not in df.columns:
            logger.error("CSV文件格式错误，必须包含 'Ticker' 和 'Name' 列")
            return []
        
        # 创建股票信息列表
        stock_list = [StockInfo(code=row['Ticker'], name=row['Name']) for _, row in df.iterrows()]
        
        logger.info(f"成功加载 {len(stock_list)} 只股票的信息")
        logger.info("前5只股票示例:")
        for i, stock in enumerate(stock_list[:5]):
            logger.info(f"  {i+1}. {stock.code:<6} - {stock.name}")
            
        return stock_list
        
    except Exception as e:
        logger.error(f"加载股票列表失败: {str(e)}")
        logger.info("请确保CSV文件格式正确: Ticker,Name\\nAAPL,\"Apple Inc.\"\\nNVDA,\"NVIDIA Corporation\"")
        return []

def test_crypto(crypto_symbol: str, start_date: str = None, end_date: str = None):
    """测试单个加密货币的数据下载"""
    downloader = AlpacaDataDownloader()
    
    # 如果没有斜杠，转换格式
    if '/' not in crypto_symbol:
        currency = crypto_symbol.replace('USD', '')
        formatted_symbol = f"{currency}/USD"
    else:
        formatted_symbol = crypto_symbol
        currency = crypto_symbol.split('/')[0]
        
    crypto_info = CryptoInfo(code=formatted_symbol, name=currency)
    
    kl_types = [
        KLType.DAILY,
        KLType.HOUR_1,
        KLType.MIN_30,
        KLType.MIN_15,
        KLType.MIN_5
    ]
    
    try:
        if not downloader.connect():
            logger.error("连接Alpaca API失败")
            return False
            
        for kl_type in kl_types:
            logger.info(f"开始处理 {crypto_info.code} 的 {kl_type.value} 数据")
            success = downloader.download_data(
                asset_info=crypto_info,
                kl_type=kl_type,
                start_date=start_date,
                end_date=end_date
            )
            if not success:
                logger.warning(f"处理 {crypto_info.code} 的 {kl_type.value} 数据失败")
            logger.info("--------")  # 添加分隔线，使日志输出更清晰
            time.sleep(1)  # 添加延迟，避免API请求过于频繁
            
    finally:
        logger.info(f"完成 {crypto_info.code} 的数据下载")

def load_crypto_list(csv_path: str) -> List[CryptoInfo]:
    """从CSV文件加载加密货币列表"""
    try:
        df = pd.read_csv(csv_path)
        crypto_list = []
        
        for _, row in df.iterrows():
            if 'Ticker' in df.columns and 'Name' in df.columns:
                code = row['Ticker']
                name = row['Name']
            else:
                logger.error(f"CSV格式不正确，需要包含'Ticker'和'Name'列: {csv_path}")
                return []
                
            # 直接使用原始ticker作为code
            crypto_list.append(CryptoInfo(code=code, name=name))
            
        return crypto_list
    except Exception as e:
        logger.error(f"加载加密货币列表时出错: {str(e)}")
        return []

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='下载Alpaca股票和加密货币数据')
    parser.add_argument('--stock', type=str, help='股票代码（如AAPL）')
    parser.add_argument('--crypto', type=str, help='加密货币代码（如BTCUSD）')
    parser.add_argument('--start', type=str, help='开始日期（YYYY-MM-DD格式）')
    parser.add_argument('--end', type=str, help='结束日期（YYYY-MM-DD格式，可选）')
    parser.add_argument('--list', help='资产列表CSV文件路径')
    parser.add_argument('--all', choices=['US', 'Crypto'], help='下载指定市场的所有资产数据')
    
    args = parser.parse_args()
    
    # 处理结束日期，如果只提供了日期，添加时间部分
    if args.end and len(args.end) == 10:  # YYYY-MM-DD格式
        args.end = f"{args.end} 23:59:59"
    
    # 如果指定了单只股票
    if args.stock:
        test_single_stock(
            stock_code=args.stock,
            start_date=args.start,
            end_date=args.end
        )
        return
        
    # 如果指定了单个加密货币
    if args.crypto:
        test_crypto(
            crypto_symbol=args.crypto,
            start_date=args.start,
            end_date=args.end
        )
        return
        
    # 如果指定了资产列表文件
    if args.list:
        if args.list.lower().endswith('crypto.csv') or 'crypto' in args.list.lower():
            logger.info(f"正在从 {args.list} 加载加密货币列表...")
            assets_list = load_crypto_list(args.list)
            is_crypto = True
        else:
            logger.info(f"正在从 {args.list} 加载股票列表...")
            assets_list = load_stock_list(args.list)
            is_crypto = False
            
        if not assets_list:
            logger.error("资产列表为空，无法处理")
            return
            
        downloader = AlpacaDataDownloader()
        if not downloader.connect():
            logger.error("连接Alpaca API失败")
            return
            
        total_assets = len(assets_list)
        updated_count = 0
        skipped_count = 0
        
        # 遍历资产列表下载数据
        for i, asset_info in enumerate(assets_list):
            logger.info(f"开始处理第 {i+1}/{total_assets} 个资产: {asset_info.code}")
            
            # 检查是否所有周期都是最新的
            all_updated = True
            for kl_type in [KLType.DAILY, KLType.HOUR_1, KLType.MIN_30, KLType.MIN_15, KLType.MIN_5]:
                need_update, _, _ = downloader._check_need_update(asset_info, kl_type, args.start, args.end)
                if need_update:
                    all_updated = False
                    break
            
            if all_updated:
                logger.info(f"{asset_info.code} 所有周期数据都是最新的，跳过处理")
                skipped_count += 1
                continue
            
            # 根据资产类型调用不同的下载函数
            if is_crypto:
                test_crypto(asset_info.code, args.start, args.end)
            else:
                test_single_stock(asset_info.code, args.start, args.end)
                
            updated_count += 1
            
            # 添加延迟，避免API请求过于频繁
            if i < total_assets - 1:
                logger.info("等待3秒后处理下一个资产...")
                time.sleep(3)
        
        logger.info(f"数据下载完成！总计 {total_assets} 个资产，"
                   f"更新 {updated_count} 个，跳过 {skipped_count} 个")
        return
        
    # 如果指定了下载所有资产
    if args.all:
        if args.all == 'US':
            csv_file = 'Data/tickers_us.csv'
            assets_list = load_stock_list(csv_file)
            is_crypto = False
        else:  # Crypto
            csv_file = 'Data/tickers_crypto.csv'
            assets_list = load_crypto_list(csv_file)
            is_crypto = True
            
        if not assets_list:
            logger.error(f"无法找到或加载{args.all}市场的资产列表")
            return
            
        downloader = AlpacaDataDownloader()
        if not downloader.connect():
            logger.error("连接Alpaca API失败")
            return
            
        total_assets = len(assets_list)
        updated_count = 0
        skipped_count = 0
        
        # 遍历资产列表下载数据
        for i, asset_info in enumerate(assets_list):
            logger.info(f"开始处理第 {i+1}/{total_assets} 个资产: {asset_info.code}")
            
            # 检查是否所有周期都是最新的
            all_updated = True
            for kl_type in [KLType.DAILY, KLType.HOUR_1, KLType.MIN_30, KLType.MIN_15, KLType.MIN_5]:
                need_update, _, _ = downloader._check_need_update(asset_info, kl_type, args.start, args.end)
                if need_update:
                    all_updated = False
                    break
            
            if all_updated:
                logger.info(f"{asset_info.code} 所有周期数据都是最新的，跳过处理")
                skipped_count += 1
                continue
            
            # 根据资产类型调用不同的下载函数
            if is_crypto:
                test_crypto(asset_info.code, args.start, args.end)
            else:
                test_single_stock(asset_info.code, args.start, args.end)
                
            updated_count += 1
            
            # 添加延迟，避免API请求过于频繁
            if i < total_assets - 1:
                logger.info("等待3秒后处理下一个资产...")
                time.sleep(3)
        
        logger.info(f"数据下载完成！总计 {total_assets} 个资产，"
                   f"更新 {updated_count} 个，跳过 {skipped_count} 个")
        return
    
    # 默认下载比特币数据示例
    if not (args.stock or args.crypto or args.list or args.all):
        parser.print_help()
        
if __name__ == "__main__":
    main() 