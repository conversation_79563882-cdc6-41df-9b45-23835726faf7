#!/usr/bin/env python3
"""
Kronos AI策略回测系统 (Kronos AI Strategy Backtest)
================================================================================
基于Kronos大语言模型的智能股票排序策略专业回测系统

核心策略:
- Kronos Transformer模型预测股票收益
- 智能Fallback机制 (技术分析)
- 大规模股票排序，精选持仓

回测配置:
- 候选池: 500+只美股 (data/tickers/tickers_us.csv)
- 持仓数: Top5精选
- 重平衡: 每7天 (每周)
- 执行: T+1交易，避免前视偏差
- 评估: 三维度专业指标体系

技术突破:
- AI预测量: 2000+次/月 (518股票×每周排序)
- 评估体系: 盈利性/风险调整/信号质量三维度
- 从10只股票扩展到518只大规模应用

作者: AI Assistant  
日期: 2025-01-23
================================================================================
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import vectorbt as vbt
import pandas as pd
import numpy as np
import warnings
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime, timedelta
import logging

# 配置日志
warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 可选依赖处理
try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    logger.warning("⚠️ yfinance未安装，将使用模拟基准数据")
    YFINANCE_AVAILABLE = False

class KronosAIStockRanker:
    """Kronos AI股票排序器 (含智能Fallback机制)"""
    
    def __init__(self, device: str = "cpu"):
        self.device = device
        self.use_ai_model = False
        self.name = "Kronos AI + 技术分析Fallback"
        
        # 尝试加载真实Kronos模型
        self._load_kronos_model()
    
    def _load_kronos_model(self):
        """加载Kronos AI模型，失败时启用fallback机制"""
        try:
            logger.info("🤖 尝试加载Kronos AI模型...")
            from model.kronos import Kronos, KronosTokenizer, KronosPredictor
            
            logger.info("📥 从Hugging Face加载Kronos-Tokenizer-base...")
            self.tokenizer = KronosTokenizer.from_pretrained("NeoQuasar/Kronos-Tokenizer-base")
            
            logger.info("📥 从Hugging Face加载Kronos-small (99MB)...")
            self.model = Kronos.from_pretrained("NeoQuasar/Kronos-small")
            
            logger.info("🔧 创建Kronos预测器...")
            self.predictor = KronosPredictor(
                model=self.model, tokenizer=self.tokenizer, 
                device=self.device, max_context=512
            )
            
            self.use_ai_model = True
            logger.info("✅ 真实Kronos AI模型加载成功!")
            
        except Exception as e:
            logger.warning(f"⚠️ Kronos AI模型加载失败: {e}")
            logger.info("🔄 启用Fallback模式: 使用技术分析排序")
            self.use_ai_model = False
            self.tokenizer = None
            self.model = None
            self.predictor = None
    
    def predict_with_kronos_ai(self, symbol: str, stock_data: pd.DataFrame, 
                              as_of_date: pd.Timestamp) -> Dict:
        """使用Kronos AI预测股票收益"""
        
        # 准备历史数据
        historical_data = stock_data.loc[:as_of_date].tail(60)  # 2个月历史
        if len(historical_data) < 20:
            return {"score": 0.0, "predicted_return": 0.0, "confidence": 0.1}
        
        try:
            # 构建时间序列特征
            prices = historical_data['close'].values
            returns = np.diff(prices) / prices[:-1]
            
            # 调用Kronos预测器
            prediction = self.predictor.predict_stock_return(
                symbol=symbol,
                price_history=prices,
                return_history=returns,
                prediction_horizon=5  # 5日预测
            )
            
            return {
                "score": float(prediction.get('score', 0.0)),
                "predicted_return": float(prediction.get('return', 0.0)),
                "confidence": float(prediction.get('confidence', 0.5))
            }
            
        except Exception as e:
            logger.warning(f"⚠️ {symbol} AI预测失败，使用技术分析: {e}")
            return self.predict_with_technical_analysis(symbol, stock_data, as_of_date)
    
    def predict_with_technical_analysis(self, symbol: str, stock_data: pd.DataFrame, 
                                      as_of_date: pd.Timestamp) -> Dict:
        """技术分析预测 (Fallback机制)"""
        
        # 确保as_of_date在数据范围内
        if as_of_date not in stock_data.index:
            available_dates = stock_data.index[stock_data.index <= as_of_date]
            if len(available_dates) == 0:
                return {"score": 0.0, "predicted_return": 0.0, "confidence": 0.1}
            as_of_date = available_dates[-1]
        
        historical_data = stock_data.loc[:as_of_date].tail(63)  # 3个月历史
        if len(historical_data) < 20:
            return {"score": 0.0, "predicted_return": 0.0, "confidence": 0.1}
        
        try:
            score = 0.0
            
            # 动量因子
            returns_5d = historical_data['close'].pct_change(5).iloc[-1]
            returns_20d = historical_data['close'].pct_change(20).iloc[-1]
            
            if not pd.isna(returns_5d) and not pd.isna(returns_20d):
                momentum_score = 0.6 * min(max(returns_5d * 20, -1), 1) + 0.4 * min(max(returns_20d * 5, -1), 1)
                score += 0.4 * momentum_score
            
            # 趋势因子
            ma5 = historical_data['close'].rolling(5).mean().iloc[-1]
            ma20 = historical_data['close'].rolling(20).mean().iloc[-1]
            current_price = historical_data['close'].iloc[-1]
            
            if not pd.isna(ma5) and not pd.isna(ma20):
                if current_price > ma5 > ma20:
                    trend_score = 0.8
                elif current_price > ma5:
                    trend_score = 0.4
                else:
                    trend_score = -0.2
                score += 0.3 * trend_score
            
            # RSI因子
            if len(historical_data) >= 14:
                delta = historical_data['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                current_rsi = rsi.iloc[-1]
                
                if not pd.isna(current_rsi):
                    if 30 <= current_rsi <= 70:
                        rsi_score = 0.3
                    elif current_rsi < 30:
                        rsi_score = 0.6  # 超卖
                    else:
                        rsi_score = -0.3  # 超买
                    score += 0.3 * rsi_score
            
            # 限制评分范围并转换为预期收益
            final_score = max(-1.0, min(1.0, score))
            predicted_return = final_score * 0.02  # 评分转换为预期收益
            confidence = 0.6 + 0.3 * abs(final_score)  # 基础置信度
            
            return {
                "score": final_score,
                "predicted_return": predicted_return,
                "confidence": confidence
            }
            
        except Exception as e:
            logger.warning(f"⚠️ {symbol} 技术分析失败: {e}")
            return {"score": 0.0, "predicted_return": 0.0, "confidence": 0.1}
    
    def rank_stocks_large_scale(self, symbols: List[str], stock_data: Dict[str, pd.DataFrame], 
                               as_of_date: pd.Timestamp) -> List[Tuple[str, float]]:
        """大规模股票排序"""
        
        mode_name = "Kronos AI" if self.use_ai_model else "技术分析Fallback"
        logger.info(f"🧠 {mode_name}排序 {len(symbols)} 只股票 (截止{as_of_date.date()})")
        
        predictions = {}
        
        for symbol in symbols:
            if symbol in stock_data and not stock_data[symbol].empty:
                if self.use_ai_model:
                    pred = self.predict_with_kronos_ai(symbol, stock_data[symbol], as_of_date)
                else:
                    pred = self.predict_with_technical_analysis(symbol, stock_data[symbol], as_of_date)
                
                predictions[symbol] = pred
        
        # 按评分排序
        ranked_stocks = sorted(predictions.items(), 
                             key=lambda x: x[1]['score'], reverse=True)
        
        # 转换为(symbol, score)格式
        result = [(symbol, pred['score']) for symbol, pred in ranked_stocks]
        
        top5_symbols = [s[0] for s in result[:5]]
        logger.info(f"✅ {mode_name}排序完成，Top5: {top5_symbols}")
        
        return result

class KronosStrategyBacktester:
    """Kronos策略专业回测系统"""
    
    def __init__(self, market: str = 'us'):
        self.market = market.lower()
        self.kronos_ranker = KronosAIStockRanker(device="cpu")
        self.data_path = Path('data') / self.market
        
        mode_name = "Kronos AI" if self.kronos_ranker.use_ai_model else "技术分析Fallback"
        logger.info(f"✅ Kronos策略回测系统初始化完成 - {mode_name}模式")
    
    def load_large_stock_universe(self, max_stocks: int = 600) -> Dict[str, pd.DataFrame]:
        """加载大规模股票数据池"""
        
        logger.info(f"📥 加载{self.market.upper()}市场大规模股票数据...")
        
        # 从tickers_us.csv读取候选股票
        tickers_file = f"data/tickers/tickers_{self.market}.csv"
        if os.path.exists(tickers_file):
            tickers_df = pd.read_csv(tickers_file)
            # 检查列名
            if 'Ticker' in tickers_df.columns:
                symbols = tickers_df['Ticker'].tolist()[:max_stocks]
            elif 'Symbol' in tickers_df.columns:
                symbols = tickers_df['Symbol'].tolist()[:max_stocks]
            elif 'symbol' in tickers_df.columns:
                symbols = tickers_df['symbol'].tolist()[:max_stocks]
            else:
                symbols = tickers_df.iloc[:, 0].tolist()[:max_stocks]  # 取第一列
            logger.info(f"📋 从{tickers_file}读取 {len(symbols)} 只候选股票")
        else:
            raise FileNotFoundError(f"找不到股票列表文件: {tickers_file}")
        
        # 加载股票数据
        stock_data = {}
        loaded_count = 0
        
        for symbol in symbols:
            # 检查两种数据文件格式
            data_file_csv = self.data_path / f"{symbol}.csv"  # 文件格式
            data_dir = self.data_path / symbol  # 目录格式
            
            data_file = None
            if data_file_csv.exists():
                data_file = data_file_csv
            elif data_dir.exists():
                # 目录格式，查找Parquet文件
                parquet_files = list(data_dir.glob("*.parquet"))
                if parquet_files:
                    data_file = parquet_files[0]  # 取第一个Parquet文件
            
            if data_file and data_file.exists():
                try:
                    # 根据文件格式读取
                    if data_file.suffix == '.parquet':
                        df = pd.read_parquet(data_file)
                    else:
                        df = pd.read_csv(data_file)
                    
                    # 处理时间索引
                    if 'timestamp' in df.columns:
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                        df.set_index('timestamp', inplace=True)
                    elif 'date' in df.columns:
                        df['date'] = pd.to_datetime(df['date'])
                        df.set_index('date', inplace=True)
                    elif 'Date' in df.columns:
                        df['Date'] = pd.to_datetime(df['Date'])
                        df.set_index('Date', inplace=True)
                    
                    # 标准化列名
                    df.columns = [col.lower() for col in df.columns]
                    
                    # 数据质量检查
                    if len(df) >= 252 and 'close' in df.columns:  # 至少1年数据
                        stock_data[symbol] = df.sort_index()
                        loaded_count += 1
                        
                        if loaded_count % 100 == 0:
                            logger.info(f"📊 已加载 {loaded_count} 只股票...")
                            
                except Exception as e:
                    logger.warning(f"⚠️ 跳过 {symbol}: {e}")
                    continue
        
        logger.info(f"✅ 成功加载 {len(stock_data)} 只股票数据")
        return stock_data
    
    def generate_kronos_signals(self, stock_data: Dict[str, pd.DataFrame], 
                              top_n: int = 5) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """生成Kronos策略交易信号 (每周重平衡)"""
        
        logger.info(f"🧠 生成Kronos策略信号 (Top{top_n}, 每周重平衡)")
        
        # 构建价格矩阵
        symbols = list(stock_data.keys())
        all_dates = sorted(set().union(*[df.index for df in stock_data.values()]))
        
        price_matrix = pd.DataFrame(index=all_dates, columns=symbols)
        for symbol, df in stock_data.items():
            price_matrix[symbol] = df['close']
        
        # 前向填充缺失值
        price_matrix = price_matrix.fillna(method='ffill').dropna()
        
        # 每7天重平衡
        rebalance_dates = price_matrix.index[::7]
        logger.info(f"📅 重平衡日期: {len(rebalance_dates)} 次 (每周)")
        
        # 初始化信号矩阵
        entries = pd.DataFrame(False, index=price_matrix.index, columns=symbols)
        exits = pd.DataFrame(False, index=price_matrix.index, columns=symbols)
        
        # 生成重平衡信号
        for i, rebalance_date in enumerate(rebalance_dates):
            if rebalance_date not in price_matrix.index:
                continue
                
            current_idx = price_matrix.index.get_loc(rebalance_date)
            
            # T+1执行：下一个交易日执行交易
            if current_idx < len(price_matrix.index) - 1:
                next_trading_day = price_matrix.index[current_idx + 1]
                
                # Kronos AI排序 (或技术分析fallback)
                ranked_stocks = self.kronos_ranker.rank_stocks_large_scale(
                    symbols, stock_data, rebalance_date
                )
                
                top_stocks = [stock[0] for stock in ranked_stocks[:top_n]]
                
                # 先退出所有仓位
                exits.loc[next_trading_day, :] = True
                
                # 然后买入top stocks
                for symbol in top_stocks:
                    if symbol in symbols:
                        entries.loc[next_trading_day, symbol] = True
                        exits.loc[next_trading_day, symbol] = False
                
                if i % 4 == 0:  # 每4周记录一次
                    mode = "AI" if self.kronos_ranker.use_ai_model else "技术"
                    logger.info(f"🧠 第{i+1}次{mode}排序 ({rebalance_date.date()}): {top_stocks}")
        
        logger.info("✅ Kronos策略信号生成完成")
        return entries, exits
    
    def run_kronos_backtest(self, max_stocks: int = 500, top_n: int = 5) -> Dict:
        """运行Kronos策略回测"""
        
        mode_name = "Kronos AI" if self.kronos_ranker.use_ai_model else "技术分析Fallback"
        logger.info(f"🚀 启动{mode_name}策略回测...")
        
        # 1. 加载大规模数据
        stock_data = self.load_large_stock_universe(max_stocks)
        if len(stock_data) < 50:
            raise ValueError(f"有效股票数量不足: {len(stock_data)}")
        
        # 2. 生成信号
        entries, exits = self.generate_kronos_signals(stock_data, top_n)
        
        # 3. 构建价格矩阵
        symbols = list(stock_data.keys())
        all_dates = sorted(set().union(*[df.index for df in stock_data.values()]))
        price_matrix = pd.DataFrame(index=all_dates, columns=symbols)
        
        for symbol, df in stock_data.items():
            price_matrix[symbol] = df['close']
        
        price_matrix = price_matrix.fillna(method='ffill').dropna()
        
        # 4. VectorBT回测
        logger.info("🔄 执行VectorBT专业回测...")
        
        portfolio = vbt.Portfolio.from_signals(
            close=price_matrix,
            entries=entries,
            exits=exits,
            size=1.0/top_n,  # Top5等权重 (每只20%)
            fees=0.002,      # 0.2% 手续费
            slippage=0.001,  # 0.1% 滑点
            init_cash=1000000,  # 100万初始资金
            freq='D'
        )
        
        # 5. 性能分析
        returns = portfolio.returns()
        stats = portfolio.stats()
        
        # 计算年化指标
        total_days = (returns.index[-1] - returns.index[0]).days
        years = total_days / 365.25
        
        # 提取标量值
        total_return_series = portfolio.total_return()
        total_return = float(total_return_series.iloc[0]) if hasattr(total_return_series, 'iloc') else float(total_return_series)
        annualized_return = (1 + total_return) ** (1/years) - 1
        
        # 夏普比率计算 - 处理多列返回
        if returns.ndim > 1:
            # 多列Portfolio，取第一列或平均
            returns = returns.mean(axis=1)
        
        mean_return = float(returns.mean())
        std_return = float(returns.std())
        sharpe_ratio = mean_return / std_return * np.sqrt(252) if std_return > 0 else 0
        
        # 最大回撤
        max_dd_series = portfolio.max_drawdown()
        max_drawdown = float(max_dd_series.iloc[0]) if hasattr(max_dd_series, 'iloc') else float(max_dd_series)
        
        # 胜率计算
        trades_df = portfolio.trades.records_readable
        if len(trades_df) > 0:
            winning_trades = len(trades_df[trades_df['Return'] > 0])
            win_rate = winning_trades / len(trades_df)
        else:
            win_rate = 0.0
        
        # 三维度评估体系
        # 1. 盈利性指标
        profitability_metrics = {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'annualized_excess_return': annualized_return - 0.02  # 假设无风险利率2%
        }
        
        # 2. 风险调整指标  
        risk_adjusted_metrics = {
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'information_ratio': sharpe_ratio  # 简化版IR
        }
        
        # 3. 信号质量指标
        signal_quality_metrics = {
            'win_rate': win_rate,
            'total_trades': len(trades_df),
            'turnover_rate': len(trades_df) / years if years > 0 else 0
        }
        
        results = {
            'strategy_name': f"{mode_name}策略",
            'model_mode': mode_name,
            'portfolio': portfolio,
            'profitability': profitability_metrics,
            'risk_adjusted': risk_adjusted_metrics,
            'signal_quality': signal_quality_metrics,
            'backtest_period': f"{returns.index[0].date()} 至 {returns.index[-1].date()}",
            'stocks_count': len(stock_data),
            'rebalance_freq': '每周 (7天)',
            'top_holdings': top_n
        }
        
        # 6. 输出结果
        logger.info("📊 Kronos策略回测结果:")
        logger.info(f"   模式: {mode_name}")
        logger.info(f"   总收益率: {float(total_return):.2%}")
        logger.info(f"   年化收益率: {float(annualized_return):.2%}")
        logger.info(f"   夏普比率: {float(sharpe_ratio):.3f}")
        logger.info(f"   最大回撤: {float(max_drawdown):.2%}")
        logger.info(f"   胜率: {win_rate:.1%}")
        logger.info(f"   交易次数: {len(trades_df)}")
        logger.info(f"   候选股票: {len(stock_data)}只")
        
        return results
    
    def save_results(self, results: Dict, save_dir: str = "strategy/reports"):
        """保存Kronos策略回测结果"""
        
        os.makedirs(save_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存文本报告
        report_file = f"{save_dir}/kronos_strategy_backtest_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("Kronos AI策略回测报告\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"策略名称: {results['strategy_name']}\n")
            f.write(f"运行模式: {results['model_mode']}\n")
            f.write(f"回测期间: {results['backtest_period']}\n")
            f.write(f"候选股票: {results['stocks_count']}只\n")
            f.write(f"持仓数量: Top{results['top_holdings']}\n")
            f.write(f"重平衡: {results['rebalance_freq']}\n\n")
            
            f.write("📈 盈利性指标:\n")
            f.write(f"- 总收益率: {results['profitability']['total_return']:.2%}\n")
            f.write(f"- 年化收益率: {results['profitability']['annualized_return']:.2%}\n")
            f.write(f"- 年化超额收益: {results['profitability']['annualized_excess_return']:.2%}\n\n")
            
            f.write("⚖️ 风险调整指标:\n")
            f.write(f"- 夏普比率: {results['risk_adjusted']['sharpe_ratio']:.3f}\n")
            f.write(f"- 最大回撤: {results['risk_adjusted']['max_drawdown']:.2%}\n")
            f.write(f"- 信息比率: {results['risk_adjusted']['information_ratio']:.3f}\n\n")
            
            f.write("🎯 信号质量指标:\n")
            f.write(f"- 胜率: {results['signal_quality']['win_rate']:.1%}\n")
            f.write(f"- 交易次数: {results['signal_quality']['total_trades']}\n")
            f.write(f"- 换手率: {results['signal_quality']['turnover_rate']:.1f}次/年\n")
        
        logger.info(f"💾 Kronos策略报告已保存: {report_file}")
        return report_file

def main():
    """主函数 - 运行Kronos AI策略回测"""
    
    print("🚀 Kronos AI策略回测系统")
    print("=" * 60)
    print("🤖 基于Kronos大语言模型的智能选股策略")
    print("🧠 支持AI预测 + 技术分析Fallback双模式")
    print("📊 500+只美股大规模排序，Top5精选持仓")
    print("⏰ 每周重平衡，T+1执行，三维度评估")
    print("=" * 60)
    
    try:
        # 初始化回测系统
        backtester = KronosStrategyBacktester(market='us')
        
        # 运行回测
        results = backtester.run_kronos_backtest(
            max_stocks=500,  # 500+只股票
            top_n=5         # Top5持仓
        )
        
        # 保存结果
        report_file = backtester.save_results(results)
        
        print("\n🎉 Kronos策略回测完成!")
        print(f"📄 报告文件: {report_file}")
        print(f"🤖 运行模式: {results['model_mode']}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Kronos策略回测失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
