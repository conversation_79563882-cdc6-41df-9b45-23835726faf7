#!/usr/bin/env python3
"""
量化策略运行器
=============

统一的策略运行入口，可以运行任何已注册的策略。

使用方法:
    cd strategy
    python run_strategies.py [strategy_name]
    
    # 列出所有策略
    python run_strategies.py --list
    
    # 运行三位一体策略
    python run_strategies.py trinity
    
    # 不指定策略名时，默认运行trinity
    python run_strategies.py
"""

import sys
import os
import argparse
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategy import AVAILABLE_STRATEGIES, list_strategies, get_strategy_info


def print_banner():
    """打印程序横幅"""
    print("=" * 80)
    print("🚀 量化策略运行器 v1.0")
    print("=" * 80)
    print("⏰ 运行时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("-" * 80)


def run_strategy(strategy_name):
    """运行指定策略"""
    strategy_info = get_strategy_info(strategy_name)
    
    if not strategy_info:
        print(f"❌ 错误: 未知策略 '{strategy_name}'")
        print("\n📋 可用策略:")
        list_strategies()
        return 1
    
    print(f"📈 运行策略: {strategy_info['name']}")
    print(f"📝 描述: {strategy_info['description']}")
    print("-" * 80)
    
    try:
        # 动态导入策略模块
        module_name = strategy_info['module']
        strategy_module = __import__(module_name, fromlist=['run_scanner'])
        
        # 运行策略扫描器
        if hasattr(strategy_module, 'run_scanner'):
            results = strategy_module.run_scanner(data_root='../data/cn', save_results=True)
            print("✅ 策略运行完成!")
            return 0
        else:
            print(f"❌ 错误: 策略模块 {module_name} 缺少 run_scanner 函数")
            return 1
            
    except ImportError as e:
        print(f"❌ 错误: 无法导入策略模块 {strategy_info['module']}: {e}")
        return 1
    except Exception as e:
        print(f"💥 策略运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='量化策略运行器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python run_strategies.py --list      # 列出所有策略
  python run_strategies.py trinity     # 运行三位一体策略
  python run_strategies.py            # 默认运行trinity策略
        """
    )
    
    parser.add_argument(
        'strategy', 
        nargs='?', 
        default='trinity',
        help='要运行的策略名称 (默认: trinity)'
    )
    
    parser.add_argument(
        '--list', '-l',
        action='store_true',
        help='列出所有可用策略'
    )
    
    args = parser.parse_args()
    
    try:
        print_banner()
        
        if args.list:
            list_strategies()
            return 0
        
        return run_strategy(args.strategy)
        
    except KeyboardInterrupt:
        print("\n\n⏹️  运行被用户中断")
        return 1
    except Exception as e:
        print(f"\n💥 程序运行失败: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)