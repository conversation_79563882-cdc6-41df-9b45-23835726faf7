"""
Strategy Module - Trading Signal Generation and Portfolio Management
====================================================================

This module contains the modular quantitative trading system components:
- signals.py: ML-based trading signal generation from model predictions  
- portfolio.py: Professional portfolio management with risk control
- trinity/: Legacy trinity strategy implementation

可用策略:
- ML Signal Generation: 基于机器学习模型的交易信号生成
- Portfolio Management: 专业投资组合管理和风险控制
- trinity: 三位一体主涨段择时策略 (legacy)

使用方法:
    # ML信号生成
    python -m strategy.signals
    
    # 投资组合管理  
    python -m strategy.portfolio
    
    # Legacy策略
    cd strategy
    python run_trinity_scanner.py
"""

__version__ = "2.0.0"

# 导入主要组件
try:
    from .signals import ProfessionalSignalGenerator, TradingSignal
    from .portfolio import PortfolioManager, Position, TradeOrder, RiskMetrics
    
    __all__ = [
        'ProfessionalSignalGenerator',
        'TradingSignal', 
        'PortfolioManager',
        'Position',
        'TradeOrder',
        'RiskMetrics'
    ]
except ImportError:
    # 如果模块还未创建，暂时忽略
    __all__ = []

# 策略注册表 (包含新的ML策略)
AVAILABLE_STRATEGIES = {
    'ml_signals': {
        'name': 'ML交易信号生成策略',
        'description': '基于XGBoost模型的机器学习交易信号生成',
        'runner': 'python -m strategy.signals',
        'module': 'strategy.signals'
    },
    'portfolio_mgmt': {
        'name': '专业投资组合管理',
        'description': '基于现代投资组合理论的风险管理和仓位优化',
        'runner': 'python -m strategy.portfolio', 
        'module': 'strategy.portfolio'
    },
    'trinity': {
        'name': '三位一体主涨段择时策略',
        'description': '基于月线MACD、日线突破、60分钟支撑的多时间框架择时策略',
        'runner': 'run_trinity_scanner.py',
        'module': 'strategy.trinity'
    }
}


def list_strategies():
    """列出所有可用策略"""
    print("📊 可用的量化策略:")
    print("=" * 50)
    for key, info in AVAILABLE_STRATEGIES.items():
        print(f"🔸 {key}: {info['name']}")
        print(f"   描述: {info['description']}")
        print(f"   运行: {info['runner']}")
        print()


def get_strategy_info(strategy_name):
    """获取策略信息"""
    return AVAILABLE_STRATEGIES.get(strategy_name)