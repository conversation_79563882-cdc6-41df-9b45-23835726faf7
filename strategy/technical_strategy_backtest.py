#!/usr/bin/env python3
"""
技术策略回测系统 (Technical Strategy Backtest)
================================================================================
基于技术分析的多因子股票选择策略专业回测系统

核心策略:
- 技术分析多因子模型 (动量、趋势、成交量、波动)
- 股票排序选股，定期重平衡
- 无前视偏差，现实交易约束

回测配置:
- 候选池: 美股/A股市场
- 持仓数: Top10选股
- 重平衡: 每月一次
- 评估: 基于vectorbt专业框架

作者: AI Assistant
基于: recall/technical_stock_selection/ 策略验证
日期: 2025-01-23
================================================================================
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import vectorbt as vbt
import pandas as pd
import numpy as np
import warnings
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime, timedelta
import logging

# 配置日志
warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 可选依赖处理
try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    logger.warning("⚠️ matplotlib未安装，跳过图表功能")
    MATPLOTLIB_AVAILABLE = False

try:
    import quantstats as qs
    QUANTSTATS_AVAILABLE = True
except ImportError:
    logger.warning("⚠️ quantstats未安装，使用基础分析")
    QUANTSTATS_AVAILABLE = False

class TechnicalAnalysisRanker:
    """技术分析股票排序器"""
    
    def __init__(self):
        self.name = "技术分析多因子选股"
        logger.info("🔧 初始化技术分析排序器")
    
    def calculate_technical_score(self, symbol: str, data: pd.DataFrame, 
                                as_of_date: pd.Timestamp) -> float:
        """计算技术分析评分"""
        
        # 确保as_of_date在数据范围内
        if as_of_date not in data.index:
            available_dates = data.index[data.index <= as_of_date]
            if len(available_dates) == 0:
                return 0.0
            as_of_date = available_dates[-1]
        
        # 需要足够的历史数据
        historical_data = data.loc[:as_of_date].tail(63)  # 3个月历史
        if len(historical_data) < 20:
            return 0.0
        
        try:
            score = 0.0
            
            # 1. 动量因子 (40%权重)
            returns_5d = historical_data['close'].pct_change(5).iloc[-1]
            returns_20d = historical_data['close'].pct_change(20).iloc[-1]
            
            momentum_score = 0.0
            if not pd.isna(returns_5d) and not pd.isna(returns_20d):
                # 5日动量得分
                momentum_5d = min(max(returns_5d * 20, -1), 1)  # 归一化到[-1,1]
                # 20日动量得分  
                momentum_20d = min(max(returns_20d * 5, -1), 1)  # 归一化到[-1,1]
                momentum_score = 0.6 * momentum_5d + 0.4 * momentum_20d
            
            score += 0.4 * momentum_score
            
            # 2. 趋势因子 (30%权重)
            ma5 = historical_data['close'].rolling(5).mean().iloc[-1]
            ma20 = historical_data['close'].rolling(20).mean().iloc[-1]
            current_price = historical_data['close'].iloc[-1]
            
            trend_score = 0.0
            if not pd.isna(ma5) and not pd.isna(ma20):
                # 多头排列
                if current_price > ma5 > ma20:
                    trend_score = 0.8
                elif current_price > ma5:
                    trend_score = 0.4
                elif ma5 > ma20:
                    trend_score = 0.2
                else:
                    trend_score = -0.3
            
            score += 0.3 * trend_score
            
            # 3. 成交量因子 (20%权重)
            volume_score = 0.0
            if 'volume' in historical_data.columns:
                avg_volume_20d = historical_data['volume'].rolling(20).mean()
                current_volume = historical_data['volume'].iloc[-1]
                volume_ratio = current_volume / avg_volume_20d.iloc[-1] if avg_volume_20d.iloc[-1] > 0 else 1
                
                # 成交量放大得分
                if volume_ratio > 1.5:
                    volume_score = 0.6
                elif volume_ratio > 1.2:
                    volume_score = 0.3
                elif volume_ratio < 0.5:
                    volume_score = -0.3
                else:
                    volume_score = 0.0
            
            score += 0.2 * volume_score
            
            # 4. 波动控制因子 (10%权重)
            volatility_score = 0.0
            returns = historical_data['close'].pct_change().dropna()
            if len(returns) > 10:
                volatility = returns.std() * np.sqrt(252)  # 年化波动率
                # 适中波动率得分最高
                if 0.15 <= volatility <= 0.35:
                    volatility_score = 0.5
                elif volatility < 0.15:
                    volatility_score = 0.2  # 波动太小
                else:
                    volatility_score = -0.3  # 波动太大
            
            score += 0.1 * volatility_score
            
            # 限制评分范围
            final_score = max(-1.0, min(1.0, score))
            return final_score
            
        except Exception as e:
            logger.warning(f"⚠️ {symbol} 技术分析评分失败: {e}")
            return 0.0
    
    def rank_stocks(self, symbols: List[str], stock_data: Dict[str, pd.DataFrame], 
                   as_of_date: pd.Timestamp) -> List[Tuple[str, float]]:
        """对股票进行技术分析排序"""
        
        logger.info(f"📊 技术分析排序 {len(symbols)} 只股票 (截止{as_of_date.date()})")
        
        scores = []
        for symbol in symbols:
            if symbol in stock_data and not stock_data[symbol].empty:
                score = self.calculate_technical_score(symbol, stock_data[symbol], as_of_date)
                scores.append((symbol, score))
        
        # 按评分降序排序
        ranked_stocks = sorted(scores, key=lambda x: x[1], reverse=True)
        
        logger.info(f"✅ 技术分析排序完成，Top5: {[s[0] for s in ranked_stocks[:5]]}")
        return ranked_stocks

class TechnicalStrategyBacktester:
    """技术策略专业回测系统"""
    
    def __init__(self, market: str = 'us'):
        self.market = market.lower()
        self.technical_ranker = TechnicalAnalysisRanker()
        self.data_path = Path('data') / self.market
        logger.info(f"✅ 技术策略回测系统初始化完成 - {self.market.upper()}市场")
    
    def load_stock_data(self, max_stocks: int = 100) -> Dict[str, pd.DataFrame]:
        """加载股票数据"""
        
        logger.info(f"📥 加载{self.market.upper()}市场股票数据 (最多{max_stocks}只)")
        
        # 从tickers文件读取候选股票
        tickers_file = f"data/tickers/tickers_{self.market}.csv"
        if os.path.exists(tickers_file):
            tickers_df = pd.read_csv(tickers_file)
            # 检查列名
            if 'Ticker' in tickers_df.columns:
                symbols = tickers_df['Ticker'].tolist()[:max_stocks]
            elif 'Symbol' in tickers_df.columns:
                symbols = tickers_df['Symbol'].tolist()[:max_stocks]
            elif 'symbol' in tickers_df.columns:
                symbols = tickers_df['symbol'].tolist()[:max_stocks]
            else:
                symbols = tickers_df.iloc[:, 0].tolist()[:max_stocks]  # 取第一列
            logger.info(f"📋 从{tickers_file}读取 {len(symbols)} 只候选股票")
        else:
            # 备用方案：扫描数据目录
            symbols = []
            for file in self.data_path.glob("*.csv"):
                if file.stem not in ['index', 'benchmark']:
                    symbols.append(file.stem)
                    if len(symbols) >= max_stocks:
                        break
            logger.info(f"📂 从数据目录扫描 {len(symbols)} 只股票")
        
        # 加载股票数据
        stock_data = {}
        loaded_count = 0
        
        for symbol in symbols:
            # 检查两种数据文件格式
            data_file_csv = self.data_path / f"{symbol}.csv"  # 文件格式
            data_dir = self.data_path / symbol  # 目录格式
            
            data_file = None
            if data_file_csv.exists():
                data_file = data_file_csv
            elif data_dir.exists():
                # 目录格式，查找Parquet文件
                parquet_files = list(data_dir.glob("*.parquet"))
                if parquet_files:
                    data_file = parquet_files[0]  # 取第一个Parquet文件
            
            if data_file and data_file.exists():
                try:
                    # 根据文件格式读取
                    if data_file.suffix == '.parquet':
                        df = pd.read_parquet(data_file)
                    else:
                        df = pd.read_csv(data_file)
                    
                    # 处理时间索引
                    if 'timestamp' in df.columns:
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                        df.set_index('timestamp', inplace=True)
                    elif 'date' in df.columns:
                        df['date'] = pd.to_datetime(df['date'])
                        df.set_index('date', inplace=True)
                    elif 'Date' in df.columns:
                        df['Date'] = pd.to_datetime(df['Date'])
                        df.set_index('Date', inplace=True)
                    
                    # 标准化列名
                    df.columns = [col.lower() for col in df.columns]
                    
                    # 数据质量检查
                    if len(df) >= 252 and 'close' in df.columns:  # 至少1年数据
                        stock_data[symbol] = df.sort_index()
                        loaded_count += 1
                        
                        if loaded_count % 50 == 0:
                            logger.info(f"📊 已加载 {loaded_count} 只股票...")
                            
                except Exception as e:
                    logger.warning(f"⚠️ 跳过 {symbol}: {e}")
                    continue
        
        logger.info(f"✅ 成功加载 {len(stock_data)} 只股票数据")
        return stock_data
    
    def generate_technical_signals(self, stock_data: Dict[str, pd.DataFrame], 
                                 top_n: int = 10, rebalance_freq: str = 'M') -> Tuple[pd.DataFrame, pd.DataFrame]:
        """生成技术策略交易信号"""
        
        logger.info(f"📈 生成技术策略信号 (Top{top_n}, {rebalance_freq}重平衡)")
        
        # 构建价格矩阵
        symbols = list(stock_data.keys())
        all_dates = sorted(set().union(*[df.index for df in stock_data.values()]))
        
        price_matrix = pd.DataFrame(index=all_dates, columns=symbols)
        for symbol, df in stock_data.items():
            price_matrix[symbol] = df['close']
        
        # 前向填充缺失值
        price_matrix = price_matrix.fillna(method='ffill').dropna()
        
        # 设置重平衡日期
        if rebalance_freq == 'M':
            # 每月重平衡
            rebalance_dates = price_matrix.resample('ME').last().index
        elif rebalance_freq == 'W':
            # 每周重平衡
            rebalance_dates = price_matrix.index[::7]  # 每7天
        else:
            # 每月重平衡
            rebalance_dates = price_matrix.resample('ME').last().index
        
        logger.info(f"📅 重平衡日期: {len(rebalance_dates)} 次")
        
        # 初始化信号矩阵
        entries = pd.DataFrame(False, index=price_matrix.index, columns=symbols)
        exits = pd.DataFrame(False, index=price_matrix.index, columns=symbols)
        
        # 生成重平衡信号
        for i, rebalance_date in enumerate(rebalance_dates):
            if rebalance_date not in price_matrix.index:
                continue
                
            current_idx = price_matrix.index.get_loc(rebalance_date)
            
            # T+1执行：下一个交易日执行
            if current_idx < len(price_matrix.index) - 1:
                next_trading_day = price_matrix.index[current_idx + 1]
                
                # 技术分析排序
                ranked_stocks = self.technical_ranker.rank_stocks(
                    symbols, stock_data, rebalance_date
                )
                
                top_stocks = [stock[0] for stock in ranked_stocks[:top_n]]
                
                # 先退出所有仓位
                exits.loc[next_trading_day, :] = True
                
                # 然后买入top stocks
                for symbol in top_stocks:
                    if symbol in symbols:
                        entries.loc[next_trading_day, symbol] = True
                        exits.loc[next_trading_day, symbol] = False
                
                if i % 3 == 0:  # 每3个重平衡日志记录一次
                    logger.info(f"📈 第{i+1}次重平衡 ({rebalance_date.date()}): {top_stocks}")
        
        logger.info("✅ 技术策略信号生成完成")
        return entries, exits
    
    def run_technical_backtest(self, max_stocks: int = 100, top_n: int = 10, 
                             rebalance_freq: str = 'M') -> Dict:
        """运行技术策略回测"""
        
        logger.info("🚀 启动技术策略回测...")
        
        # 1. 加载数据
        stock_data = self.load_stock_data(max_stocks)
        if len(stock_data) < 10:
            raise ValueError(f"有效股票数量不足: {len(stock_data)}")
        
        # 2. 生成信号
        entries, exits = self.generate_technical_signals(stock_data, top_n, rebalance_freq)
        
        # 3. 构建价格矩阵
        symbols = list(stock_data.keys())
        all_dates = sorted(set().union(*[df.index for df in stock_data.values()]))
        price_matrix = pd.DataFrame(index=all_dates, columns=symbols)
        
        for symbol, df in stock_data.items():
            price_matrix[symbol] = df['close']
        
        price_matrix = price_matrix.fillna(method='ffill').dropna()
        
        # 4. VectorBT回测
        logger.info("🔄 执行VectorBT专业回测...")
        
        portfolio = vbt.Portfolio.from_signals(
            close=price_matrix,
            entries=entries,
            exits=exits,
            size=1.0/top_n,  # 等权重
            fees=0.002,      # 0.2% 手续费
            slippage=0.001,  # 0.1% 滑点
            init_cash=1000000,  # 100万初始资金
            freq='D'
        )
        
        # 5. 性能分析
        returns = portfolio.returns()
        stats = portfolio.stats()
        
        # 计算年化指标
        total_days = (returns.index[-1] - returns.index[0]).days
        years = total_days / 365.25
        
        # 提取标量值
        total_return_series = portfolio.total_return()
        total_return = float(total_return_series.iloc[0]) if hasattr(total_return_series, 'iloc') else float(total_return_series)
        annualized_return = (1 + total_return) ** (1/years) - 1
        
        # 夏普比率计算 - 处理多列返回
        if returns.ndim > 1:
            # 多列Portfolio，取第一列或平均
            returns = returns.mean(axis=1)
        
        mean_return = float(returns.mean())
        std_return = float(returns.std())
        sharpe_ratio = mean_return / std_return * np.sqrt(252) if std_return > 0 else 0
        
        # 最大回撤
        max_dd_series = portfolio.max_drawdown()
        max_drawdown = float(max_dd_series.iloc[0]) if hasattr(max_dd_series, 'iloc') else float(max_dd_series)
        
        # 胜率计算
        winning_trades = len(portfolio.trades.records_readable[
            portfolio.trades.records_readable['Return'] > 0
        ])
        total_trades = len(portfolio.trades.records_readable)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        results = {
            'strategy_name': self.technical_ranker.name,
            'portfolio': portfolio,
            'total_return': total_return,
            'annualized_return': annualized_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'backtest_period': f"{returns.index[0].date()} 至 {returns.index[-1].date()}",
            'stocks_count': len(stock_data),
            'rebalance_freq': rebalance_freq
        }
        
        # 6. 输出结果  
        logger.info("📊 技术策略回测结果:")
        logger.info(f"   总收益率: {float(total_return):.2%}")
        logger.info(f"   年化收益率: {float(annualized_return):.2%}")
        logger.info(f"   夏普比率: {float(sharpe_ratio):.3f}")
        logger.info(f"   最大回撤: {float(max_drawdown):.2%}")
        logger.info(f"   胜率: {win_rate:.1%}")
        logger.info(f"   交易次数: {total_trades}")
        logger.info(f"   回测期间: {results['backtest_period']}")
        
        return results
    
    def save_results(self, results: Dict, save_dir: str = "strategy/reports"):
        """保存回测结果"""
        
        os.makedirs(save_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存文本报告
        report_file = f"{save_dir}/technical_strategy_backtest_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("技术策略回测报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"策略名称: {results['strategy_name']}\n")
            f.write(f"回测期间: {results['backtest_period']}\n")
            f.write(f"股票数量: {results['stocks_count']}\n")
            f.write(f"重平衡频率: {results['rebalance_freq']}\n\n")
            
            f.write("性能指标:\n")
            f.write(f"- 总收益率: {results['total_return']:.2%}\n")
            f.write(f"- 年化收益率: {results['annualized_return']:.2%}\n")
            f.write(f"- 夏普比率: {results['sharpe_ratio']:.3f}\n")
            f.write(f"- 最大回撤: {results['max_drawdown']:.2%}\n")
            f.write(f"- 胜率: {results['win_rate']:.1%}\n")
            f.write(f"- 交易次数: {results['total_trades']}\n")
        
        logger.info(f"💾 回测报告已保存: {report_file}")
        return report_file

def main():
    """主函数 - 运行技术策略回测"""
    
    print("🚀 技术策略回测系统")
    print("=" * 50)
    print("✅ 基于技术分析的多因子选股策略")
    print("✅ 动量+趋势+成交量+波动四因子模型")
    print("✅ VectorBT专业回测框架")
    print("=" * 50)
    
    try:
        # 初始化回测系统
        backtester = TechnicalStrategyBacktester(market='us')
        
        # 运行回测
        results = backtester.run_technical_backtest(
            max_stocks=100,  # 使用100只股票测试
            top_n=10,        # Top10持仓
            rebalance_freq='M'  # 每月重平衡
        )
        
        # 保存结果
        report_file = backtester.save_results(results)
        
        print("\n🎉 技术策略回测完成!")
        print(f"📄 报告文件: {report_file}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ 技术策略回测失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
