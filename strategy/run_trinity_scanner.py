#!/usr/bin/env python3
"""
三位一体主涨段择时信号扫描器 v2.2
=================================

功能特点:
- 自动扫描A股市场，识别三位一体主涨段信号
- 结果保存到 logs/strategy_trinity/ 目录
- 增强的CSV输出，包含股票名称、市场信息、决策依据等
- 规范的文件命名，便于跟踪和管理

使用方法:
    cd strategy
    python run_trinity_scanner.py

输出文件:
    logs/strategy_trinity/trinity_exit_signals_YYYYMMDD_HHMMSS.csv      - 卖出警示
    logs/strategy_trinity/trinity_buy_signals_YYYYMMDD_HHMMSS.csv       - 标准买入
    logs/strategy_trinity/trinity_heavy_buy_signals_YYYYMMDD_HHMMSS.csv - 重仓买入  
    logs/strategy_trinity/trinity_add_position_signals_YYYYMMDD_HHMMSS.csv - 加仓机会
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategy.trinity import run_scanner


def print_banner():
    """打印程序横幅"""
    print("=" * 80)
    print("🔍 三位一体主涨段择时信号扫描器 v2.2")
    print("=" * 80)
    print("📊 扫描A股市场，识别符合三位一体策略的交易机会")
    print("📁 结果保存位置: logs/strategy_trinity/")
    print("⏰ 扫描时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("-" * 80)


def check_environment():
    """检查运行环境"""
    # 检查数据目录（从strategy目录向上一级寻找）
    data_root = '../data/cn'
    if not os.path.exists(data_root):
        print(f"❌ 错误: 数据目录 '{data_root}' 不存在")
        print("💡 请确保已下载股票数据到data/cn目录下")
        return False
    
    # 检查是否有股票数据（新的目录结构下）
    stock_count = 0
    required_timeframes = ['1mo', '1wk', '1d', '1h', '15m']
    missing_timeframes = []
    
    # 统计有多少股票有各个时间框架的数据
    timeframe_counts = {tf: 0 for tf in required_timeframes}
    
    for item in os.listdir(data_root):
        item_path = os.path.join(data_root, item)
        if os.path.isdir(item_path):
            # 检查是否有日线数据文件（确认是股票目录）
            day_file = os.path.join(item_path, '1d.parquet')
            if os.path.exists(day_file):
                stock_count += 1
                # 统计各时间框架数据的完整性
                for tf in required_timeframes:
                    tf_file = os.path.join(item_path, f'{tf}.parquet')
                    if os.path.exists(tf_file):
                        timeframe_counts[tf] += 1
    
    if stock_count == 0:
        print(f"❌ 错误: 在 '{data_root}' 中未找到股票数据")
        print("💡 请确保数据目录结构为: data/cn/{股票代码}/{时间周期}.parquet")
        return False
    
    print(f"📊 发现 {stock_count} 只股票数据")
    
    # 检查时间框架数据完整性
    for tf in required_timeframes:
        count = timeframe_counts[tf]
        if count == 0:
            missing_timeframes.append(tf)
        else:
            print(f"   {tf}: {count} 只股票")
    
    if missing_timeframes:
        print(f"⚠️  警告: 缺少以下时间框架数据: {missing_timeframes}")
        print("📝 扫描可能不完整，建议补充完整数据")
    
    # 创建日志目录
    log_dir = '../logs/strategy_trinity'
    os.makedirs(log_dir, exist_ok=True)
    print(f"✅ 日志目录已准备: {log_dir}")
    
    return True


def print_summary(results):
    """打印扫描结果总结"""
    print("\n" + "=" * 80)
    print("📈 扫描结果总结")
    print("=" * 80)
    
    total_signals = sum(len(signals) for signals in results.values())
    
    signal_counts = {
        'exit': len(results.get('exit', [])),
        'standard_buy': len(results.get('standard_buy', [])),
        'heavy_buy': len(results.get('heavy_buy', [])),
        'add_on': len(results.get('add_on', []))
    }
    
    signal_names = {
        'exit': '🚨 卖出警示信号',
        'standard_buy': '📈 标准买入信号',
        'heavy_buy': '💪 重仓买入信号',
        'add_on': '➕ 加仓机会信号'
    }
    
    for signal_type, count in signal_counts.items():
        if count > 0:
            print(f"{signal_names[signal_type]}: {count} 只股票")
    
    if total_signals == 0:
        print("📭 当前市场暂无明确信号，建议持币观望")
    else:
        print(f"\n🎯 发现 {total_signals} 个交易信号，请查看CSV文件获取详细信息")
    
    print("-" * 80)
    print("💡 使用建议:")
    print("   1. 优先关注卖出警示信号，及时止损")
    print("   2. 重仓买入信号需谨慎评估，控制风险")
    print("   3. 标准买入信号可分批建仓")
    print("   4. 加仓信号仅适用于已持仓股票")
    print("=" * 80)


def main():
    """主函数"""
    try:
        # 打印横幅
        print_banner()
        
        # 检查环境
        if not check_environment():
            return 1
        
        # 运行扫描器
        print("🚀 开始扫描...")
        results = run_scanner(data_root='../data/cn', save_results=True)
        
        # 打印总结
        print_summary(results)
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n⏹️  扫描被用户中断")
        return 1
    except Exception as e:
        print(f"\n💥 扫描过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)