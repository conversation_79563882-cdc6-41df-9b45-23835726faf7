"""
三位一体主涨段择时信号扫描器 v2.2

v2.2 重大升级内容:
🎯 买入操作精细化: 增加"回调健康度"分析，避免买在回调过深的节点
💰 加仓操作量化: 引入"风险收益比"判断，确保每次加仓都有明确的盈亏比支持
⚠️ 卖出操作前瞻性: 新增"上涨乏力"预警信号，提前识别趋势反转风险

v2.1 升级内容:
- 重仓买入条件从J-1级别(60分钟主升)升级为J+1级别(周线极强)
- 实现更大级别的时空共振，提升信号质量和可靠性
- 更新CSV输出字段: hour60_strong → weekly_macd_strong
"""

from .scanner import run_scanner
from .indicators import calculate_indicators
from .signals import (
    get_latest_spacetime_state,
    check_spacetime_deterioration,
    check_breakthrough_setup,
    check_main_rise_structure_breakdown,
    check_latest_effective_support, 
    check_15m_key_candle_confirmation,
    check_hourly_main_rise_structure,
    check_60m_breakdown,
    check_simplified_exit_signal,
    check_in_main_rise_stage,
    check_macd_divergence,
    get_pullback_state,
    calculate_risk_reward_ratio,
    check_exhaustion_alert
)
# 股票信息功能已移至 scanner.py 中，使用 tickers_cn.csv

__version__ = "2.2.0"
__all__ = [
    'run_scanner',
    'calculate_indicators',
    'get_latest_spacetime_state',
    'check_spacetime_deterioration',
    'check_breakthrough_setup',
    'check_main_rise_structure_breakdown',
    'check_latest_effective_support',
    'check_15m_key_candle_confirmation',
    'check_hourly_main_rise_structure',
    'check_60m_breakdown',
    'check_simplified_exit_signal',
    'check_in_main_rise_stage',
    'check_macd_divergence',
    'get_pullback_state',
    'calculate_risk_reward_ratio',
    'check_exhaustion_alert'
]