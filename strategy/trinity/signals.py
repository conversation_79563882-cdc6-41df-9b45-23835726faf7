"""
三位一体主涨段择时信号 - 信号识别模块
"""

import pandas as pd
import numpy as np


def get_latest_spacetime_state(df_month):
    """
    判断月线MACD时空状态
    
    Args:
        df_month: 月线DataFrame (包含DIF, DEA列)
        
    Returns:
        str: 'EXTREMELY_STRONG' 或其他状态
    """
    if df_month is None or df_month.empty or len(df_month) < 2:
        return 'INSUFFICIENT_DATA'
    
    # 获取最新数据
    latest = df_month.iloc[-1]
    
    # 检查DIF是否大于0（必要条件）
    if latest['DIF'] <= 0:
        return 'NOT_STRONG'
    
    # 极强状态判断：DIF > 0 且 DEA <= 0
    if latest['DEA'] <= 0:
        # 需要确认DIF确实是从零轴下方上穿的
        dif_cross_zero_idx = None
        for i in range(len(df_month) - 1, 0, -1):
            if df_month.iloc[i]['DIF'] > 0 and df_month.iloc[i-1]['DIF'] <= 0:
                dif_cross_zero_idx = i
                break
        
        if dif_cross_zero_idx is not None:
            return 'EXTREMELY_STRONG'
        else:
            # DIF一直大于0，但DEA<=0，也认为是极强状态
            return 'EXTREMELY_STRONG'
    
    # DEA > 0，检查是否刚刚结束极强状态进入强状态
    if latest['DEA'] > 0:
        return 'STRONG_BUT_NOT_EXTREMELY'


def check_spacetime_deterioration(df_month):
    """
    判断月线MACD时空状态是否恶化
    恶化定义：从极强状态转为非极强状态（DEA上穿零轴）
    
    Args:
        df_month: 月线DataFrame (包含DIF, DEA列)
        
    Returns:
        bool: True表示时空状态恶化
    """
    if df_month is None or df_month.empty or len(df_month) < 2:
        return False
    
    current_state = get_latest_spacetime_state(df_month)
    
    # 主要恶化标志：DEA上穿零轴（从极强状态转为强状态）
    if len(df_month) >= 2:
        latest = df_month.iloc[-1]
        prev = df_month.iloc[-2]
        
        # DEA从<=0转为>0，表示极强状态结束
        if (not pd.isna(latest['DEA']) and not pd.isna(prev['DEA']) and
            prev['DEA'] <= 0 and latest['DEA'] > 0):
            return True
    
    # 次要恶化标志：DIF跌破零轴（更严重的恶化）
    if len(df_month) >= 2:
        latest = df_month.iloc[-1]
        prev = df_month.iloc[-2]
        
        # DIF从>0转为<=0，表示趋势完全逆转
        if (not pd.isna(latest['DIF']) and not pd.isna(prev['DIF']) and
            prev['DIF'] > 0 and latest['DIF'] <= 0):
            return True
    
    return False


def check_breakthrough_setup(df_day, lookback_days=20):
    """
    判断股价是否即将突破中枢或刚刚突破前高
    改进版：更宽松的突破判断，包含即将突破的情况
    
    Args:
        df_day: 日线DataFrame
        lookback_days: 前高回看天数，默认20天(约一个月)
        
    Returns:
        tuple: (bool, str) - (是否满足突破条件, 突破类型描述)
    """
    if df_day is None or df_day.empty or len(df_day) < lookback_days + 5:
        return (False, "数据不足")
    
    current_price = df_day.iloc[-1]['close']
    current_high = df_day.iloc[-1]['high']
    
    # 计算前N天的最高点（前高）
    past_high = df_day.iloc[-(lookback_days+5):-1]['high'].max()
    
    # 情况1：已经突破前高（刚刚突破）
    if current_price > past_high:
        return (True, f"已突破{lookback_days}日前高")
    
    # 情况2：即将突破前高（距离前高5%以内）
    distance_to_high_pct = ((past_high - current_price) / current_price) * 100
    if distance_to_high_pct <= 5.0:  # 5%以内认为即将突破
        return (True, f"即将突破{lookback_days}日前高(距离{distance_to_high_pct:.1f}%)")
    
    # 情况3：最近3天的最高价接近前高（盘中已触及）
    recent_3_high = df_day.iloc[-3:]['high'].max()
    if recent_3_high >= past_high * 0.98:  # 98%以上认为即将突破
        return (True, f"近期高点接近{lookback_days}日前高")
    
    return (False, f"距离{lookback_days}日前高还有{distance_to_high_pct:.1f}%")


def check_main_rise_structure_breakdown(df_day):
    """
    判断日线主升结构是否被破坏
    破坏定义：跌破关键支撑且确认趋势逆转
    
    Args:
        df_day: 日线DataFrame
        
    Returns:
        bool: True表示主升结构被破坏
    """
    if df_day is None or df_day.empty or len(df_day) < 81:
        return False
    
    latest_close = df_day.iloc[-1]['close']
    
    # 1. 主要破坏标志：跌破过去60日重要支撑位
    # 计算过去60日的关键支撑（60日最低价的105%）
    past_60_low = df_day.iloc[-61:-1]['low'].min()
    critical_support = past_60_low * 1.05
    
    if latest_close < critical_support:
        return True
    
    # 2. 次要破坏标志：形成明显的下降趋势
    # 连续5日收盘价呈下降趋势，且最新收盘价比5日前低5%以上
    if len(df_day) >= 5:
        recent_5_closes = df_day.iloc[-5:]['close']
        close_5_days_ago = recent_5_closes.iloc[0]
        latest_close = recent_5_closes.iloc[-1]
        
        # 价格下跌超过5%且整体呈下降趋势
        price_decline_pct = (close_5_days_ago - latest_close) / close_5_days_ago
        is_declining_trend = latest_close < close_5_days_ago * 0.95
        
        if price_decline_pct > 0.05 and is_declining_trend:
            return True
    
    return False


def check_latest_effective_support(df_60m):
    """
    判断60分钟线是否在EMA_55或BBM_20_2.0上形成有效支撑
    
    Args:
        df_60m: 60分钟DataFrame
        
    Returns:
        tuple: (bool, str) - (是否有效支撑, 支撑类型)
    """
    if df_60m is None or df_60m.empty or len(df_60m) < 2:
        return (False, None)
    
    latest = df_60m.iloc[-1]
    prev = df_60m.iloc[-2] if len(df_60m) > 1 else latest
    
    # 检查EMA_55支撑
    if 'EMA_55' in df_60m.columns and not pd.isna(latest['EMA_55']):
        ema_55 = latest['EMA_55']
        
        # 最新K线low触及或下穿支撑线，但close收于支撑线上方
        if latest['low'] <= ema_55 and latest['close'] > ema_55:
            return (True, 'EMA_55')
        
        # 倒数第二根K线测试支撑，最新一根K线收阳且未创新低
        if (prev['low'] <= ema_55 and prev['close'] > ema_55 and 
            latest['close'] > latest['open'] and latest['low'] >= prev['low']):
            return (True, 'EMA_55')
    
    # 检查BBM_20_2.0支撑
    if 'BBM_20_2.0' in df_60m.columns and not pd.isna(latest['BBM_20_2.0']):
        bbm = latest['BBM_20_2.0']
        
        # 最新K线low触及或下穿支撑线，但close收于支撑线上方
        if latest['low'] <= bbm and latest['close'] > bbm:
            return (True, 'BBM_20_2.0')
        
        # 倒数第二根K线测试支撑，最新一根K线收阳且未创新低
        if (prev['low'] <= bbm and prev['close'] > bbm and 
            latest['close'] > latest['open'] and latest['low'] >= prev['low']):
            return (True, 'BBM_20_2.0')
    
    return (False, None)


def check_15m_key_candle_confirmation(df_15m, df_60m=None):
    """
    判断15分钟线关键K线确认信号
    条件：在回踩60分钟布林带时，看次级别15分钟回踩55线，如出现关键k加分
    关键K定义：出现最低价的阴K线的下一根15分钟线是阳线
    
    Args:
        df_15m: 15分钟DataFrame
        df_60m: 60分钟DataFrame (用于判断是否回踩布林带)
        
    Returns:
        bool: True表示出现关键K线确认
    """
    if df_15m is None or df_15m.empty or len(df_15m) < 5:
        return False
    
    # 检查15分钟是否有EMA_55
    if 'EMA_55' not in df_15m.columns:
        return False
    
    # 检查15分钟回踩55线
    latest_15m = df_15m.iloc[-1]
    if pd.isna(latest_15m['EMA_55']):
        return False
    
    # 15分钟回踩55线的条件：最低价触及或下穿，但收盘价在55线上方
    is_15m_pullback_ema55 = (latest_15m['low'] <= latest_15m['EMA_55'] and 
                              latest_15m['close'] > latest_15m['EMA_55'])
    
    # 或者检查最近3根K线是否有回踩55线的情况
    if not is_15m_pullback_ema55:
        recent_3 = df_15m.iloc[-3:]
        for i in range(len(recent_3)):
            candle = recent_3.iloc[i]
            if not pd.isna(candle['EMA_55']):
                if candle['low'] <= candle['EMA_55'] and candle['close'] > candle['EMA_55']:
                    is_15m_pullback_ema55 = True
                    break
    
    if not is_15m_pullback_ema55:
        return False
    
    # 获取最近5根K线寻找最低价
    recent_5 = df_15m.iloc[-5:].copy()
    recent_5['low_rank'] = recent_5['low'].rank(method='min')
    
    # 找到最低价的K线位置
    min_low_idx = recent_5['low_rank'].idxmin()
    min_low_pos = recent_5.index.get_loc(min_low_idx)
    
    # 检查最低价K线是否为阴线
    min_low_candle = recent_5.iloc[min_low_pos]
    is_bearish = min_low_candle['close'] < min_low_candle['open']
    
    if not is_bearish:
        return False
    
    # 检查下一根K线是否为阳线（如果存在）
    if min_low_pos < len(recent_5) - 1:
        next_candle = recent_5.iloc[min_low_pos + 1]
        is_next_bullish = next_candle['close'] > next_candle['open']
        return is_next_bullish
    else:
        # 如果最低价K线是最后一根，检查当前是否为阳线
        latest = df_15m.iloc[-1]
        return latest['close'] > latest['open']
    
    return False


def check_hourly_main_rise_structure(df_60m):
    """
    判断60分钟线是否满足主升结构 (J-1级别结构确认)
    用于重仓买入的备选加强信号
    
    Args:
        df_60m: 60分钟DataFrame
        
    Returns:
        bool: True表示满足60分钟主升结构
    """
    if df_60m is None or df_60m.empty or len(df_60m) < 25:
        return False
    
    # 60分钟级别使用较短的周期：最近8小时内突破前16小时高点
    recent_8_data = df_60m.iloc[-8:]  # 最近8个小时
    past_16_high = df_60m.iloc[-24:-8]['high'].max()  # 前16小时高点
    
    # 检查最近8小时内是否有突破前16小时高点
    breakthrough = False
    for i in range(len(recent_8_data)):
        if recent_8_data.iloc[i]['close'] > past_16_high:
            breakthrough = True
            break
    
    if not breakthrough:
        return False
    
    # 检查当前是否仍在合理范围内（未有效跌破突破点的92%）
    latest_close = df_60m.iloc[-1]['close']
    threshold = past_16_high * 0.92  # 60分钟级别允许8%的回调
    
    return latest_close > threshold


def check_60m_breakdown(df_60m):
    """
    判断60分钟线是否有效跌破布林带中轨BBM_20_2.0
    
    Args:
        df_60m: 60分钟DataFrame
        
    Returns:
        bool: True表示有效跌破
    """
    if df_60m is None or df_60m.empty or len(df_60m) < 2:
        return False
    
    if 'BBM_20_2.0' not in df_60m.columns:
        return False
    
    latest = df_60m.iloc[-1]
    prev = df_60m.iloc[-2]
    
    # 检查当前收盘价是否低于布林带中轨
    if pd.isna(latest['BBM_20_2.0']) or latest['close'] >= latest['BBM_20_2.0']:
        return False
    
    # 有效跌破的条件：
    # 1. 最新收盘价低于BBM_20_2.0
    # 2. 前一根K线的收盘价也低于BBM_20_2.0，或者当前K线是实体较大的阴线
    prev_below_bbm = prev['close'] < prev['BBM_20_2.0'] if not pd.isna(prev['BBM_20_2.0']) else False
    
    # 判断是否为实体较大的阴线（实体大于最近5根K线平均实体的1.5倍）
    is_big_bearish = False
    if len(df_60m) >= 5:
        recent_bodies = abs(df_60m.iloc[-5:]['close'] - df_60m.iloc[-5:]['open'])
        avg_body = recent_bodies.mean()
        current_body = abs(latest['close'] - latest['open'])
        is_big_bearish = (latest['close'] < latest['open'] and 
                         current_body > avg_body * 1.5)
    
    return prev_below_bbm or is_big_bearish


def check_in_main_rise_stage(df_month, df_day):
    """
    判断是否处于主涨段中
    主涨段定义：
    1. 月线处于极强状态（DIF > 0 且 DEA <= 0）
    2. 日线已经突破前高形成主升结构
    
    Args:
        df_month: 月线DataFrame
        df_day: 日线DataFrame
        
    Returns:
        tuple: (bool, str) - (是否在主涨段, 描述)
    """
    if df_month is None or df_month.empty or df_day is None or df_day.empty:
        return (False, "数据不足")
    
    # 条件1: 月线极强状态
    monthly_state = get_latest_spacetime_state(df_month)
    is_monthly_strong = (monthly_state == 'EXTREMELY_STRONG')
    
    if not is_monthly_strong:
        return (False, "月线非极强状态")
    
    # 条件2: 日线突破前高（更宽松的判断，包括已突破和即将突破）
    breakthrough_result = check_breakthrough_setup(df_day, lookback_days=20)
    is_breakthrough = breakthrough_result[0]
    
    if not is_breakthrough:
        return (False, "日线未形成突破结构")
    
    return (True, "处于主涨段中")


def check_simplified_exit_signal(df_month, df_day, df_60m, df_15m=None):
    """
    简化的卖出信号判断：前提在主涨段中时
    1. 60分钟跌出布林带中轨
    2. 60分钟跌破55线
    3. 15分钟或60分钟MACD出现背离
    
    Args:
        df_month: 月线DataFrame (用于判断是否在主涨段)
        df_day: 日线DataFrame (用于判断是否在主涨段)
        df_60m: 60分钟DataFrame
        df_15m: 15分钟DataFrame (可选，用于15分钟MACD背离检测)
        
    Returns:
        tuple: (bool, str) - (是否卖出, 卖出原因)
    """
    if df_60m is None or df_60m.empty or len(df_60m) < 3:
        return (False, "数据不足")
    
    # 前提条件：必须在主涨段中
    in_main_rise, main_rise_desc = check_in_main_rise_stage(df_month, df_day)
    if not in_main_rise:
        return (False, f"不在主涨段中({main_rise_desc})，无需判断卖出")
    
    reasons = []
    exit_conditions = 0
    
    latest = df_60m.iloc[-1]
    
    # 条件1: 60分钟跌出布林带中轨
    if 'BBM_20_2.0' in df_60m.columns and not pd.isna(latest['BBM_20_2.0']):
        if latest['close'] < latest['BBM_20_2.0']:
            exit_conditions += 1
            reasons.append("60分钟跌破布林带中轨")
    
    # 条件2: 60分钟跌破55线  
    if 'EMA_55' in df_60m.columns and not pd.isna(latest['EMA_55']):
        if latest['close'] < latest['EMA_55']:
            exit_conditions += 1
            reasons.append("60分钟跌破55线")
    
    # 条件3: 60分钟MACD背离
    divergence_60m, divergence_desc_60m = check_macd_divergence(df_60m)
    if divergence_60m:
        exit_conditions += 1
        reasons.append(f"60分钟{divergence_desc_60m}")
    
    # 条件3: 15分钟MACD背离 (如果提供15分钟数据)
    if df_15m is not None:
        divergence_15m, divergence_desc_15m = check_macd_divergence(df_15m)
        if divergence_15m and not divergence_60m:  # 避免重复计算
            exit_conditions += 1
            reasons.append(f"15分钟{divergence_desc_15m}")
    
    # 满足任一条件即触发卖出信号
    if exit_conditions > 0:
        return (True, " + ".join(reasons))
    
    return (False, "在主涨段中，但未满足卖出条件")


def get_pullback_state(df_60m):
    """
    分析60分钟线回调健康度，识别回调的持续时间和深度
    
    Args:
        df_60m: 60分钟DataFrame
        
    Returns:
        dict: {
            "is_supported": bool,      # 是否获得支撑
            "pullback_duration_bars": int,  # 回调持续的K线数量
            "pullback_depth_pct": float,    # 回调深度百分比
            "pullback_status": str     # 回调状态: EARLY/MIDDLE/LATE
        }
    """
    if df_60m is None or df_60m.empty or len(df_60m) < 10:
        return {
            "is_supported": False,
            "pullback_duration_bars": 0,
            "pullback_depth_pct": 0.0,
            "pullback_status": "INSUFFICIENT_DATA"
        }
    
    # 检查是否获得支撑
    support_result = check_latest_effective_support(df_60m)
    is_supported = support_result[0]
    
    # 寻找最近的高点作为回调起点
    recent_data = df_60m.iloc[-20:].copy()  # 取最近20根K线
    recent_data['high_shift'] = recent_data['high'].shift(1)
    recent_data['high_shift2'] = recent_data['high'].shift(-1)
    
    # 找到局部高点（比前后K线都高）
    local_highs = recent_data[
        (recent_data['high'] > recent_data['high_shift']) & 
        (recent_data['high'] > recent_data['high_shift2'])
    ]
    
    if local_highs.empty:
        # 如果没有找到局部高点，使用最近10根K线的最高点
        pullback_start_idx = len(df_60m) - 10 + df_60m.iloc[-10:]['high'].idxmax()
        pullback_high = df_60m.iloc[-10:]['high'].max()
    else:
        # 使用最近的局部高点
        pullback_start_idx = local_highs.index[-1]
        pullback_high = local_highs.iloc[-1]['high']
    
    # 计算回调相关指标
    current_price = df_60m.iloc[-1]['close']
    current_low = df_60m.iloc[-1]['low']
    
    # 回调持续K线数量
    pullback_duration = len(df_60m) - 1 - pullback_start_idx
    
    # 回调深度百分比
    pullback_depth_pct = ((pullback_high - current_low) / pullback_high) * 100
    
    # 判断回调状态
    if pullback_duration <= 3:
        pullback_status = "EARLY"    # 早期回调，风险较低
    elif pullback_duration <= 8:
        pullback_status = "MIDDLE"   # 中期回调，需要谨慎
    else:
        pullback_status = "LATE"     # 后期回调，风险较高
    
    return {
        "is_supported": is_supported,
        "pullback_duration_bars": max(0, pullback_duration),
        "pullback_depth_pct": round(pullback_depth_pct, 2),
        "pullback_status": pullback_status
    }


def calculate_risk_reward_ratio(df_daily, df_60m):
    """
    计算当前价位的风险收益比
    
    Args:
        df_daily: 日线DataFrame (用于计算收益边界)
        df_60m: 60分钟DataFrame (用于计算风险边界)
        
    Returns:
        dict: {
            "risk_reward_ratio": float,  # 风险收益比
            "risk_distance_pct": float,  # 风险边界距离百分比
            "reward_distance_pct": float, # 收益边界距离百分比
            "risk_price": float,         # 风险价位(止损位)
            "reward_price": float        # 收益价位(目标位)
        }
    """
    if (df_daily is None or df_daily.empty or 
        df_60m is None or df_60m.empty):
        return {
            "risk_reward_ratio": 0.0,
            "risk_distance_pct": 0.0,
            "reward_distance_pct": 0.0,
            "risk_price": 0.0,
            "reward_price": 0.0
        }
    
    current_price = df_60m.iloc[-1]['close']
    
    # 风险边界：60分钟布林带中轨 (核心止损位)
    risk_price = 0.0
    if 'BBM_20_2.0' in df_60m.columns and not pd.isna(df_60m.iloc[-1]['BBM_20_2.0']):
        risk_price = df_60m.iloc[-1]['BBM_20_2.0']
    
    # 收益边界：日线布林带上轨 (潜在阻力位)
    reward_price = 0.0
    if 'BB_upper' in df_daily.columns and not pd.isna(df_daily.iloc[-1]['BB_upper']):
        reward_price = df_daily.iloc[-1]['BB_upper']
    
    # 计算距离百分比
    if risk_price > 0:
        risk_distance_pct = ((current_price - risk_price) / current_price) * 100
    else:
        risk_distance_pct = 0.0
    
    if reward_price > current_price:
        reward_distance_pct = ((reward_price - current_price) / current_price) * 100
    else:
        reward_distance_pct = 0.0
    
    # 计算风险收益比
    if risk_distance_pct > 0:
        risk_reward_ratio = reward_distance_pct / risk_distance_pct
    else:
        risk_reward_ratio = 0.0
    
    return {
        "risk_reward_ratio": round(risk_reward_ratio, 2),
        "risk_distance_pct": round(risk_distance_pct, 2),
        "reward_distance_pct": round(reward_distance_pct, 2),
        "risk_price": round(risk_price, 2),
        "reward_price": round(reward_price, 2)
    }


def calculate_rsi(prices, period=14):
    """
    计算RSI指标
    
    Args:
        prices: 价格序列
        period: RSI周期，默认14
        
    Returns:
        Series: RSI值序列
    """
    if len(prices) < period + 1:
        return pd.Series([50] * len(prices), index=prices.index)
    
    delta = prices.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    
    return rsi


def check_macd_divergence(df, period=5):
    """
    检测MACD背离信号
    
    Args:
        df: DataFrame，需包含close, MACD列
        period: 比较周期，默认5
        
    Returns:
        tuple: (bool, str) - (是否背离, 背离类型描述)
    """
    if df is None or df.empty or len(df) < period + 2:
        return (False, "数据不足")
    
    if 'MACD' not in df.columns or 'DIF' not in df.columns:
        return (False, "缺少MACD指标")
    
    # 获取最近的数据
    recent = df.iloc[-period-1:].copy()
    
    # 价格趋势：比较最新价格和period前的价格
    current_price = recent.iloc[-1]['close']
    past_price = recent.iloc[0]['close']
    price_trend = "up" if current_price > past_price else "down"
    
    # MACD趋势：比较最新MACD和period前的MACD
    current_macd = recent.iloc[-1]['DIF']  # 使用DIF作为MACD主线
    past_macd = recent.iloc[0]['DIF']
    macd_trend = "up" if current_macd > past_macd else "down"
    
    # 顶背离：价格创新高，MACD创新低
    if price_trend == "up" and macd_trend == "down":
        # 验证是否确实是新高和相对低点
        recent_high = recent['high'].max()
        is_new_high = recent_high == recent.iloc[-1]['high'] or recent_high == recent.iloc[-2]['high']
        
        if is_new_high:
            return (True, f"MACD顶背离({period}周期)")
    
    # 底背离：价格创新低，MACD创新高
    elif price_trend == "down" and macd_trend == "up":
        # 验证是否确实是新低和相对高点
        recent_low = recent['low'].min()
        is_new_low = recent_low == recent.iloc[-1]['low'] or recent_low == recent.iloc[-2]['low']
        
        if is_new_low:
            return (True, f"MACD底背离({period}周期)")
    
    return (False, "无背离")


def check_exhaustion_alert(df_daily, df_60m):
    """
    检查上涨乏力警示信号，识别潜在的趋势反转风险
    
    Args:
        df_daily: 日线DataFrame
        df_60m: 60分钟DataFrame
        
    Returns:
        dict: {
            "is_exhaustion": bool,           # 是否出现上涨乏力
            "bb_width_ratio": float,         # 布林带宽度比率
            "rsi_60m": float,               # 60分钟RSI值
            "alert_strength": str,           # 警示强度: LOW/MEDIUM/HIGH
            "suggested_action": str          # 建议操作
        }
    """
    if (df_daily is None or df_daily.empty or len(df_daily) < 60 or
        df_60m is None or df_60m.empty or len(df_60m) < 14):
        return {
            "is_exhaustion": False,
            "bb_width_ratio": 0.0,
            "rsi_60m": 50.0,
            "alert_strength": "NONE",
            "suggested_action": "HOLD"
        }
    
    # 1. 计算日线布林带宽度
    latest_daily = df_daily.iloc[-1]
    if ('BB_upper' not in df_daily.columns or 'BB_lower' not in df_daily.columns or
        pd.isna(latest_daily['BB_upper']) or pd.isna(latest_daily['BB_lower'])):
        return {
            "is_exhaustion": False,
            "bb_width_ratio": 0.0,
            "rsi_60m": 50.0,
            "alert_strength": "NONE",
            "suggested_action": "HOLD"
        }
    
    # 当前布林带宽度
    current_bb_width = latest_daily['BB_upper'] - latest_daily['BB_lower']
    
    # 过去60天布林带宽度平均值
    past_60_bb_widths = []
    for i in range(max(0, len(df_daily) - 60), len(df_daily)):
        row = df_daily.iloc[i]
        if not pd.isna(row['BB_upper']) and not pd.isna(row['BB_lower']):
            width = row['BB_upper'] - row['BB_lower']
            past_60_bb_widths.append(width)
    
    if not past_60_bb_widths:
        avg_bb_width = current_bb_width
    else:
        avg_bb_width = np.mean(past_60_bb_widths)
    
    # 布林带宽度比率
    bb_width_ratio = current_bb_width / avg_bb_width if avg_bb_width > 0 else 1.0
    
    # 2. 计算60分钟RSI
    rsi_values = calculate_rsi(df_60m['close'])
    rsi_60m = rsi_values.iloc[-1] if not pd.isna(rsi_values.iloc[-1]) else 50.0
    
    # 3. 判断上涨乏力条件
    bb_condition = bb_width_ratio > 1.5  # 日线布林带宽度 > 60日均值 * 1.5
    rsi_condition = rsi_60m > 80         # 60分钟RSI > 80
    
    is_exhaustion = bb_condition and rsi_condition
    
    # 4. 确定警示强度
    if is_exhaustion:
        if rsi_60m > 90 and bb_width_ratio > 2.0:
            alert_strength = "HIGH"
            suggested_action = "REDUCE_POSITION_50%"
        elif rsi_60m > 85 or bb_width_ratio > 1.8:
            alert_strength = "MEDIUM"
            suggested_action = "REDUCE_POSITION_30%"
        else:
            alert_strength = "LOW"
            suggested_action = "REDUCE_POSITION_20%"
    else:
        alert_strength = "NONE"
        suggested_action = "HOLD"
    
    return {
        "is_exhaustion": is_exhaustion,
        "bb_width_ratio": round(bb_width_ratio, 2),
        "rsi_60m": round(rsi_60m, 2),
        "alert_strength": alert_strength,
        "suggested_action": suggested_action
    }