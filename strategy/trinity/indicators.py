"""
三位一体主涨段择时信号 - 技术指标计算模块
"""

import pandas as pd
import numpy as np
import pandas_ta as ta


def calculate_indicators(df):
    """
    为输入的DataFrame计算MACD(12,26,9), EMA(55), Bollinger Bands(20,2)
    
    Args:
        df: 包含OHLCV数据的DataFrame
        
    Returns:
        df: 添加了技术指标的DataFrame
    """
    if df is None or df.empty:
        return df
    
    df = df.copy()
    
    # 计算MACD(12,26,9)
    macd_data = ta.macd(df['close'], fast=12, slow=26, signal=9)
    df['MACD'] = macd_data['MACD_12_26_9']
    df['MACD_signal'] = macd_data['MACDs_12_26_9']
    df['MACD_hist'] = macd_data['MACDh_12_26_9']
    
    # DIF和DEA的别名
    df['DIF'] = df['MACD']
    df['DEA'] = df['MACD_signal']
    
    # 计算EMA(55)
    df['EMA_55'] = ta.ema(df['close'], length=55)
    
    # 计算Bollinger Bands(20,2)
    bb_data = ta.bbands(df['close'], length=20, std=2)
    df['BB_upper'] = bb_data['BBU_20_2.0']
    df['BB_middle'] = bb_data['BBM_20_2.0']
    df['BB_lower'] = bb_data['BBL_20_2.0']
    
    # 添加布林带中轨的别名
    df['BBM_20_2.0'] = df['BB_middle']
    
    return df