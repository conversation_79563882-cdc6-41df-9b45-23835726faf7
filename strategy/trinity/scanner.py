"""
三位一体主涨段择时信号 - 主扫描器模块
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
from .indicators import calculate_indicators
from .signals import (
    get_latest_spacetime_state,
    check_breakthrough_setup, 
    check_latest_effective_support,
    check_15m_key_candle_confirmation,
    check_simplified_exit_signal,
    check_in_main_rise_stage,
    check_macd_divergence
)
# 删除 stock_info 导入，改用 tickers_cn.csv


def load_stock_data(symbol, data_root='data/cn'):
    """
    加载指定股票的多时间框架数据
    
    Args:
        symbol: 股票代码
        data_root: 数据根目录
        
    Returns:
        dict: 包含各时间框架数据的字典
    """
    timeframes = {
        'month': '1mo',
        'week': '1wk', 
        'day': '1d',
        'hour': '1h',
        'min15': '15m'
    }
    
    data = {}
    
    for tf_name, tf_code in timeframes.items():
        # 新的目录结构: data/cn/{symbol}/{timeframe}.parquet
        file_path = os.path.join(data_root, symbol, f"{tf_code}.parquet")
        
        if os.path.exists(file_path):
            try:
                df = pd.read_parquet(file_path)
                if not df.empty:
                    # 确保数据按时间排序
                    if 'datetime' in df.columns:
                        df = df.sort_values('datetime').reset_index(drop=True)
                    elif 'timestamp' in df.columns:
                        df = df.sort_values('timestamp').reset_index(drop=True)
                    data[tf_name] = df
            except Exception as e:
                print(f"加载 {symbol} {tf_name} 数据失败: {e}")
                
    return data


def load_stock_tickers():
    """
    从 tickers_cn.csv 加载股票代码和名称映射
    
    Returns:
        dict: {symbol: name} 股票代码到名称的映射
    """
    ticker_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                               'data', 'tickers', 'tickers_cn.csv')
    
    if not os.path.exists(ticker_file):
        print(f"股票列表文件不存在: {ticker_file}")
        return {}
    
    try:
        df = pd.read_csv(ticker_file)
        return dict(zip(df['Ticker'], df['Name']))
    except Exception as e:
        print(f"读取股票列表失败: {e}")
        return {}


def get_stock_list(data_root='data/cn'):
    """
    从数据目录获取有数据的股票列表，并与 tickers_cn.csv 匹配
    
    Args:
        data_root: 数据根目录
        
    Returns:
        list: 股票代码列表（仅包含有数据且在 tickers 文件中的股票）
    """
    if not os.path.exists(data_root):
        print(f"数据目录不存在: {data_root}")
        return []
    
    # 加载股票名称映射
    ticker_mapping = load_stock_tickers()
    
    symbols = []
    for item in os.listdir(data_root):
        symbol_dir = os.path.join(data_root, item)
        if os.path.isdir(symbol_dir):
            # 检查是否有日线数据文件，且在 tickers 文件中存在
            day_file = os.path.join(symbol_dir, '1d.parquet')
            if os.path.exists(day_file) and item in ticker_mapping:
                symbols.append(item)
    
    return sorted(symbols)


def get_stock_name(symbol):
    """
    获取股票名称
    
    Args:
        symbol: 股票代码
        
    Returns:
        str: 股票名称，如果未找到返回代码本身
    """
    ticker_mapping = load_stock_tickers()
    return ticker_mapping.get(symbol, symbol)


def get_market_info(symbol):
    """
    获取市场信息
    
    Args:
        symbol: 股票代码
        
    Returns:
        dict: 包含市场、板块等信息
    """
    if symbol.endswith('.SZ'):
        if symbol.startswith('000'):
            market = '深交所主板'
        elif symbol.startswith('002'):
            market = '深交所中小板'
        elif symbol.startswith('300') or symbol.startswith('301'):
            market = '创业板'
        else:
            market = '深交所'
    elif symbol.endswith('.SH'):
        if symbol.startswith('688'):
            market = '科创板'
        elif symbol.startswith('60'):
            market = '上交所主板'
        else:
            market = '上交所'
    else:
        market = '未知'
    
    return {
        'market': market,
        'exchange': 'SZ' if symbol.endswith('.SZ') else 'SH'
    }


def analyze_stock_signals(symbol, data):
    """
    分析单只股票的简化Trinity策略信号
    
    买入信号：
    1. 月线进入极强，或者极强延续 
    2. 股价即将突破中枢或刚刚突破前高（近一个月前高）
    3. 60分钟回踩55线或布林带中轨
    4. 加分项：15分钟关键K线确认
    
    卖出信号：
    前提在主涨段中时
    1. 60分钟跌出布林带中轨
    2. 60分钟跌破55线
    3. 15分钟或60分钟MACD出现背离
    
    Args:
        symbol: 股票代码
        data: 包含各时间框架数据的字典 {timeframe: DataFrame}
        
    Returns:
        dict: {
            'symbol': str,
            'signals': [signal_types],  # 'buy' 或 'sell'
            'details': {各种判断条件和数值}
        }
    """
    result = {
        'symbol': symbol,
        'signals': [],
        'details': {}
    }
    
    # 检查必要的数据是否存在
    required_tfs = ['month', 'day', 'hour', 'min15']
    for tf in required_tfs:
        if tf not in data or data[tf].empty:
            result['details']['error'] = f'缺少{tf}数据'
            return result
    
    try:
        # 计算各时间框架的技术指标
        for tf_name, df in data.items():
            data[tf_name] = calculate_indicators(df)
        
        # 优先检查卖出信号 - 必须在主涨段中才判断卖出
        exit_signal, exit_reason = check_simplified_exit_signal(
            data['month'], data['day'], data['hour'], data['min15']
        )
        if exit_signal:
            result['signals'].append('sell')
            result['details']['exit_reason'] = exit_reason
            
            # 添加主涨段信息
            in_main_rise, main_rise_desc = check_in_main_rise_stage(data['month'], data['day'])
            result['details']['in_main_rise'] = in_main_rise
            result['details']['main_rise_desc'] = main_rise_desc
            return result
        
        # 检查买入信号的条件
        # 条件1: 月线极强状态
        is_monthly_strong = get_latest_spacetime_state(data['month']) == 'EXTREMELY_STRONG'
        result['details']['monthly_strong'] = is_monthly_strong
        
        # 条件2: 股价即将突破或刚突破前高
        breakthrough_result = check_breakthrough_setup(data['day'], lookback_days=20)  # 近一个月前高
        is_breakthrough_setup = breakthrough_result[0]
        result['details']['breakthrough_setup'] = is_breakthrough_setup
        result['details']['breakthrough_desc'] = breakthrough_result[1]
        
        # 条件3: 60分钟回踩支撑
        support_info = check_latest_effective_support(data['hour'])
        is_supported = support_info[0]
        support_type = support_info[1]
        result['details']['support_info'] = support_info
        result['details']['supported'] = is_supported
        result['details']['support_type'] = support_type
        
        # 条件4: 15分钟关键K线确认（加分项）
        # 只有在60分钟回踩布林带时才检查15分钟确认
        is_15m_confirmed = False
        if support_type == 'BBM_20_2.0':  # 只有60分钟回踩布林带时才检查15分钟确认
            is_15m_confirmed = check_15m_key_candle_confirmation(data['min15'], data['hour'])
        result['details']['15m_confirmed'] = is_15m_confirmed
        
        # 买入信号判断：前三个条件必须满足
        if is_monthly_strong and is_breakthrough_setup and is_supported:
            result['signals'].append('buy')
            
            # 确定仓位大小
            if is_15m_confirmed:
                result['details']['position_size'] = '60%'
                result['details']['signal_strength'] = 'STRONG'
            else:
                result['details']['position_size'] = '50%'
                result['details']['signal_strength'] = 'NORMAL'
            
            # 买入理由
            buy_reasons = [
                "月线极强状态",
                breakthrough_result[1],
                f"60分钟回踩{support_type}获支撑"
            ]
            if is_15m_confirmed:
                buy_reasons.append("15分钟关键K线确认")
            
            result['details']['buy_reason'] = " + ".join(buy_reasons)
        
    except Exception as e:
        result['details']['error'] = f'信号分析失败: {str(e)}'
    
    return result


def format_signal_reason(details):
    """
    格式化信号触发逻辑说明
    
    Args:
        details: 信号详情字典
        
    Returns:
        str: 格式化的触发逻辑说明
    """
    reasons = []
    
    if details.get('monthly_strong'):
        reasons.append('月线极强')
    
    if details.get('daily_setup'):
        reasons.append('日线主升结构')
        
    if details.get('support_type'):
        if details['support_type'] == 'EMA_55':
            reasons.append('60分钟回踩55EMA支撑')
        elif details['support_type'] == 'BBM_20_2.0':
            reasons.append('60分钟回踩布林带中轨支撑')
    
    if details.get('weekly_strong'):
        reasons.append('周线同时进入极强时空状态')
    
    if details.get('hourly_structure'):
        reasons.append('60分钟形成主升段结构')
    
    return ' + '.join(reasons)


def convert_numpy_types(obj):
    """
    递归转换numpy类型为Python原生类型，用于JSON序列化
    """
    if isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    else:
        return obj


def save_simplified_results(signals, stats, output_dir='../logs/strategy_trinity'):
    """
    保存简化的Trinity策略信号结果到CSV文件
    
    Args:
        signals: 信号字典 {'buy': [...], 'sell': [...]}
        stats: 扫描统计信息
        output_dir: 输出目录
        
    Returns:
        str: CSV文件路径
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    current_time = datetime.now()
    date_str = current_time.strftime("%Y%m%d")
    time_str = current_time.strftime("%H%M%S")
    
    # 简化文件名
    output_file = os.path.join(output_dir, f"trinity_signals_simplified_{date_str}_{time_str}.csv")
    
    # 准备CSV数据
    csv_data = []
    
    # 定义简化的字段
    columns = [
        'timestamp', 'symbol', 'name', 'market', 'signal_type', 'action', 
        'position_size', 'support_type', 'signal_strength',
        'monthly_strong', 'breakthrough_setup', 'supported', '15m_confirmed',
        'reason', 'notes'
    ]
    
    for signal_type, signal_list in signals.items():
        if signal_list:
            for signal in signal_list:
                symbol = signal['symbol']
                details = signal['details']
                market_info = get_market_info(symbol)
                
                row = {
                    'timestamp': current_time.strftime("%Y-%m-%d %H:%M:%S"),
                    'symbol': symbol,
                    'name': get_stock_name(symbol),
                    'market': market_info['market'],
                    'signal_type': signal_type,
                }
                
                # 初始化所有字段
                for col in columns:
                    if col not in row:
                        row[col] = ''
                
                if signal_type == 'buy':
                    row.update({
                        'action': '买入',
                        'position_size': details.get('position_size', '50%'),
                        'support_type': details.get('support_type', ''),
                        'signal_strength': details.get('signal_strength', 'NORMAL'),
                        'monthly_strong': 'Y' if details.get('monthly_strong') else 'N',
                        'breakthrough_setup': 'Y' if details.get('breakthrough_setup') else 'N',
                        'supported': 'Y' if details.get('supported') else 'N',
                        '15m_confirmed': 'Y' if details.get('15m_confirmed') else 'N',
                        'reason': details.get('buy_reason', ''),
                        'notes': f"建议{details.get('position_size', '50%')}仓位，支撑：{details.get('support_type', '')}"
                    })
                    
                elif signal_type == 'sell':
                    row.update({
                        'action': '卖出',
                        'position_size': '',
                        'support_type': '',
                        'signal_strength': '',
                        'monthly_strong': '',
                        'breakthrough_setup': '',
                        'supported': '',
                        '15m_confirmed': '',
                        'reason': details.get('exit_reason', ''),
                        'notes': '建议立即卖出或减仓'
                    })
                
                csv_data.append(row)
    
    # 保存CSV
    if csv_data:
        df = pd.DataFrame(csv_data)
        df = df.reindex(columns=columns)
        
        # 按信号类型排序，卖出优先
        signal_order = {'sell': 1, 'buy': 2}
        df['signal_order'] = df['signal_type'].map(signal_order)
        df = df.sort_values(['signal_order', 'symbol']).drop('signal_order', axis=1)
        
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    return output_file


def save_unified_results(signals, stats, output_dir='../logs/strategy_trinity'):
    """
    保存统一的Trinity策略信号结果到单个CSV文件
    
    Trinity策略包含5种信号类型:
    1. exit - 卖出信号 (多级别验证)
    2. take_profit_alert - 减仓预警 (上涨乏力警示)  
    3. standard_buy - 标准买入 (三位一体条件)
    4. heavy_buy - 重仓买入 (加强条件)
    5. add_on - 加仓信号 (支撑+风险收益比)
    
    Args:
        signals: 信号字典 {signal_type: [signal_list]}
        stats: 扫描统计信息
        output_dir: 输出目录
        
    Returns:
        str: 统一CSV文件路径
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    current_time = datetime.now()
    date_str = current_time.strftime("%Y%m%d")
    time_str = current_time.strftime("%H%M%S")
    
    # 统一文件名
    unified_file = os.path.join(output_dir, f"trinity_signals_{date_str}_{time_str}.csv")
    
    # 准备统一的CSV数据
    unified_data = []
    
    # 定义优化的统一字段顺序 - 按业务逻辑分组
    unified_columns = [
        # 基础信息 (所有信号)
        'timestamp', 'date', 'symbol', 'name', 'market', 
        'signal_type', 'action', 'priority', 'risk_level',
        
        # 操作信息 (买入/加仓信号)
        'position_size', 'support_type', 'reason',
        
        # 三位一体条件 (买入信号)
        'monthly_macd_strong', 'daily_breakout', 'weekly_macd_strong', 'hourly_structure', 'min15_confirmed',
        
        # 回调分析 (买入信号)
        'pullback_duration_bars', 'pullback_depth_pct', 'pullback_status',
        
        # 风险收益分析 (加仓信号)
        'risk_reward_ratio', 'risk_distance_pct', 'reward_distance_pct',
        
        # 技术指标 (预警信号)
        'alert_strength', 'suggested_action', 'bb_width_ratio', 'rsi_60m',
        
        # 备注说明 (所有信号)
        'notes'
    ]
    
    for signal_type, signal_list in signals.items():
        if signal_list:
            for signal in signal_list:
                symbol = signal['symbol']
                details = signal['details']
                market_info = get_market_info(symbol)
                
                # 基础行数据
                row = {
                    'timestamp': current_time.strftime("%Y-%m-%d %H:%M:%S"),
                    'date': date_str,
                    'symbol': symbol,
                    'name': get_stock_name(symbol),
                    'market': market_info['market'],
                    'signal_type': signal_type,
                }
                
                # 初始化所有字段为空值
                for col in unified_columns:
                    if col not in row:
                        row[col] = ''
                
                # 根据信号类型填充特定字段
                if signal_type == 'exit':
                    row.update({
                        'action': '卖出',
                        'priority': 'HIGH',
                        'reason': details.get('exit_reason', ''),
                        'risk_level': '高风险',
                        'notes': '建议立即卖出或减仓'
                    })
                    
                elif signal_type in ['standard_buy', 'heavy_buy']:
                    position_pct = details.get('position_size', '50%')
                    support_type = details.get('support_type', '')
                    
                    row.update({
                        'action': '买入' if signal_type == 'standard_buy' else '重仓买入',
                        'priority': 'MEDIUM' if signal_type == 'standard_buy' else 'HIGH',
                        'position_size': position_pct,
                        'support_type': support_type,
                        'monthly_macd_strong': 'Y' if details.get('monthly_strong') else 'N',
                        'daily_breakout': 'Y' if details.get('daily_setup') else 'N',
                        'min15_confirmed': 'Y' if details.get('15m_confirmed') else 'N',
                        'weekly_macd_strong': 'Y' if details.get('weekly_strong') else 'N',
                        'hourly_structure': 'Y' if details.get('hourly_structure') else 'N',
                        'pullback_duration_bars': details.get('pullback_duration_bars', 0),
                        'pullback_depth_pct': details.get('pullback_depth_pct', 0.0),
                        'pullback_status': details.get('pullback_status', ''),
                        'reason': format_signal_reason(details),
                        'risk_level': '中等风险' if signal_type == 'standard_buy' else '积极进取',
                        'notes': f"建议{position_pct}仓位，支撑：{support_type}，回调{details.get('pullback_duration_bars', 0)}根K线"
                    })
                    
                elif signal_type == 'add_on':
                    position_pct = details.get('position_size', '20%')
                    add_reason = details.get('add_on_reason', '')
                    
                    row.update({
                        'action': '加仓',
                        'priority': 'LOW',
                        'position_size': position_pct,
                        'reason': add_reason,
                        'risk_reward_ratio': details.get('risk_reward_ratio', 0.0),
                        'risk_distance_pct': details.get('risk_distance_pct', 0.0),
                        'reward_distance_pct': details.get('reward_distance_pct', 0.0),
                        'risk_level': '低风险',
                        'notes': f"适合已持仓股票，建议加仓{position_pct}，风险收益比{details.get('risk_reward_ratio', 0.0)}"
                    })
                    
                elif signal_type == 'take_profit_alert':
                    alert_strength = details.get('alert_strength', 'LOW')
                    suggested_action = details.get('suggested_action', 'HOLD')
                    
                    row.update({
                        'action': '减仓预警',
                        'priority': 'HIGH' if alert_strength == 'HIGH' else 'MEDIUM',
                        'alert_strength': alert_strength,
                        'suggested_action': suggested_action,
                        'bb_width_ratio': details.get('bb_width_ratio', 0.0),
                        'rsi_60m': details.get('rsi_60m', 50.0),
                        'risk_level': '注意风险',
                        'notes': f"上涨乏力警示，建议操作：{suggested_action}，RSI={details.get('rsi_60m', 50.0)}"
                    })
                
                unified_data.append(row)
    
    # 保存统一CSV
    if unified_data:
        df = pd.DataFrame(unified_data)
        
        # 确保列顺序一致
        df = df.reindex(columns=unified_columns)
        
        # 按信号类型和优先级排序
        signal_order = {'exit': 1, 'take_profit_alert': 2, 'heavy_buy': 3, 'standard_buy': 4, 'add_on': 5}
        priority_order = {'HIGH': 1, 'MEDIUM': 2, 'LOW': 3}
        
        df['signal_order'] = df['signal_type'].map(signal_order)
        df['priority_num'] = df['priority'].map(priority_order)
        df = df.sort_values(['signal_order', 'priority_num', 'symbol']).drop(['signal_order', 'priority_num'], axis=1)
        
        df.to_csv(unified_file, index=False, encoding='utf-8-sig')
    
    return unified_file




def run_scanner(data_root='data/cn', save_results=True):
    """
    执行简化的三位一体主涨段信号扫描
    
    Args:
        data_root: 数据根目录
        save_results: 是否保存结果到文件
        
    Returns:
        dict: 扫描结果
    """
    print("开始三位一体主涨段信号扫描（简化版）...")
    
    # 获取股票列表
    symbols = get_stock_list(data_root)
    if not symbols:
        print("未找到股票数据")
        return {}
    
    print(f"发现 {len(symbols)} 只股票，开始扫描...")
    
    # 初始化结果容器
    signals = {
        'buy': [],
        'sell': []
    }
    
    scan_stats = {
        'total_scanned': 0,
        'data_loaded': 0,
        'signal_found': 0,
        'monthly_strong_count': 0,
        'breakthrough_count': 0,
        'support_count': 0,
        'all_three_conditions': 0
    }
    
    # 遍历股票列表
    for i, symbol in enumerate(symbols):
        scan_stats['total_scanned'] += 1
        
        if (i + 1) % 50 == 0:
            print(f"扫描进度: {i + 1}/{len(symbols)}")
        
        # 加载股票数据
        data = load_stock_data(symbol, data_root)
        
        # 检查是否有足够的数据
        if len(data) < 4:  # 需要至少4个时间框架的数据
            continue
            
        scan_stats['data_loaded'] += 1
        
        # 分析信号
        result = analyze_stock_signals(symbol, data)
        
        # 统计各条件满足情况
        details = result['details']
        if details.get('monthly_strong'):
            scan_stats['monthly_strong_count'] += 1
        if details.get('breakthrough_setup'):
            scan_stats['breakthrough_count'] += 1
        if details.get('supported'):
            scan_stats['support_count'] += 1
        
        # 统计所有三个条件同时满足的情况
        if (details.get('monthly_strong') and details.get('breakthrough_setup') and 
            details.get('supported')):
            scan_stats['all_three_conditions'] += 1
        
        if result['signals']:
            scan_stats['signal_found'] += 1
            
            for signal_type in result['signals']:
                signal_info = {
                    'symbol': symbol,
                    'details': result['details']
                }
                signals[signal_type].append(signal_info)
    
    # 打印扫描结果
    print_scan_results(signals, scan_stats)
    
    # 保存结果到文件
    if save_results:
        try:
            # 保存简化的信号文件
            output_file = save_simplified_results(signals, scan_stats)
            
            print(f"\n📁 扫描结果已保存到: {output_file}")
        except Exception as e:
            print(f"\n⚠️  保存结果时出错: {e}")
            import traceback
            traceback.print_exc()
    
    return signals


def print_scan_results(signals, stats):
    """
    打印格式化的简化扫描结果
    
    Args:
        signals: 信号字典 {'buy': [...], 'sell': [...]}
        stats: 扫描统计信息
    """
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
    
    print(f"\n--- 三位一体主涨段信号扫描报告 (简化版) ({current_time}) ---\n")
    
    # 卖出信号
    if signals['sell']:
        print("[🚨] 卖出信号:")
        for signal in signals['sell']:
            symbol = signal['symbol']
            stock_name = get_stock_name(symbol)
            reason = signal['details'].get('exit_reason', '触发卖出信号')
            print(f"  - {symbol} ({stock_name}): {reason}")
        print()
    
    # 买入信号
    if signals['buy']:
        print("[📈] 买入信号:")
        for signal in signals['buy']:
            symbol = signal['symbol']
            stock_name = get_stock_name(symbol)
            details = signal['details']
            position = details.get('position_size', '50%')
            strength = details.get('signal_strength', 'NORMAL')
            reason = details.get('buy_reason', '')
            confirmed = "✓" if details.get('15m_confirmed') else ""
            
            print(f"  - {symbol} ({stock_name}):")
            print(f"    - 仓位建议: {position} [{strength}] {confirmed}")
            print(f"    - 触发逻辑: {reason}")
        print()
    
    # 统计信息
    total_signals = sum(len(signals[key]) for key in signals)
    print(f"扫描完成，共扫描{stats['total_scanned']}只股票，")
    print(f"成功加载数据{stats['data_loaded']}只，发现{total_signals}个交易信号。")
    
    # 条件分布统计
    print(f"\n--- 买入条件分布统计 ---")
    print(f"月线极强状态: {stats.get('monthly_strong_count', 0)}只 ({stats.get('monthly_strong_count', 0)/stats['data_loaded']*100:.1f}%)")
    print(f"即将/刚突破前高: {stats.get('breakthrough_count', 0)}只 ({stats.get('breakthrough_count', 0)/stats['data_loaded']*100:.1f}%)")
    print(f"60分钟支撑: {stats.get('support_count', 0)}只 ({stats.get('support_count', 0)/stats['data_loaded']*100:.1f}%)")
    print(f"三个条件都满足: {stats.get('all_three_conditions', 0)}只")