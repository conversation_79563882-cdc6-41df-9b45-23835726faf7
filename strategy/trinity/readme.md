# 三位一体策略深度解析

### **三位一体交易系统量化逻辑梳理**

**核心决策框架：** 严格遵循 **时空 → 结构 → 均线** 的分析优先级。

*   **时空 (J+2级别)**: 定大局，判断是否值得操作。
*   **结构 (J级别)**: 定方向，判断趋势是否正在发生。
*   **均线 (J-1级别)**: 定买点，找到高概率的精确介入位置。
*   **辅助 (J-2级别)**: 定细节，用于信号确认，提升胜率。

*(注：J代表交易级别，例如我们关注日线级别的交易机会，则J=日线)*

---

#### **I. 时空系统 (Spacetime System) - 状态筛选**

**目标**: 过滤出处于“极强”状态的股票，这是唯一值得重仓操作的宏观背景。

*   **观测级别**: `J+2` (例如，做日线交易，看月线状态)。
*   **核心指标**: `MACD(12, 26, 9)`。
*   **量化规则**:
    1.  **“极强”状态 (Extremely Strong) 的定义**:
        *   `DIF > 0` (快线已上穿零轴)
        *   `DEA <= 0` (慢线仍在零轴下方或刚触及零轴)
        *   **状态开始**: `DIF`上穿零轴的那个K线周期。
        *   **状态结束**: `DEA`上穿零轴的那个K线周期。
        *   **核心交易理念**: 在此区间内，忽略一切小级别的顶背离信号，坚决寻找回调买入机会。

    2.  **“强”状态 (Strong) 的定义**:
        *   `DEA > 0` (慢线已上穿零轴)
        *   **核心交易理念**: “极强”状态结束，上涨动力可能衰竭，应转为高抛低吸或准备减仓，不再是主升浪的黄金时期。

    3.  **多级别共振 (高阶)**:
        *   **重仓买入的加强条件**: `J+2` 级别处于“极强”状态的同时，`J+1` 级别也进入了“极强”状态。这预示着 `J` 级别和 `J-1` 级别将同时进入主升段，股价表现会“异常强劲”。

---

#### **II. 结构系统 (Structure System) - 趋势确认**

**目标**: 在“时空极强”的前提下，确认 `J` 级别的主升浪结构已经形成。

*   **观测级别**: `J` (例如，日线)。
*   **核心指标**: K线形态，文档中提及复杂的A/B/C/D型和笔的概念，但最核心、最易量化的是**主涨段的启动特征**。
*   **量化规则**:
    1.  **主升段结构确认 (A型-五段式)**:
        *   **简化量化规则**: 最新收盘价有效突破前期重要高点。一个可行的量化方案是：`Close(J) > High(J, past N periods)`，其中 `N` 可以根据市场周期设定，例如文档启发的60个周期。
        *   **理论要点**: 主涨段（第三笔或第五笔）一定不是最短的一段，且末期常有加速特征。

    2.  **背离的特殊处理**:
        *   在`J+2`时空为“极强”时，`J`级别及以下级别的**顶背离信号应被忽略**。因为极端情绪会通过“时间换空间”的方式化解背离。

---

#### **III. 均线系统 (Moving Average System) - 精准买卖**

**目标**: 在宏观状态和中观结构都满足后，寻找微观上的高概率、高赔率的介入点和退出点。

*   **观测级别**: `J-1` (例如，60分钟) 和 `J-2` (例如，15分钟)。
*   **核心指标**: `EMA(55)` 和 `Bollinger Bands(20, 2)`。
*   **量化规则**:
    1.  **核心买入触发器 (高阶内容Point 1 & 2)**:
        *   **级别**: `J-1` (例如，60分钟图)。
        *   **条件**: 价格回调至 `BBM(20, 2.0)` (布林带中轨) 或 `EMA(55)`，并获得**有效支撑**。
        *   **有效支撑定义**:
            *   K线的最低价 `Low <= 支撑线`。
            *   K线的收盘价 `Close > 支撑线`。
            *   (进阶) 前一根K线测试支撑后，当前K线收阳且未创新低。

    2.  **辅助买入确认 (高阶内容Point 4)**:
        *   **级别**: `J-2` (例如，15分钟图)。
        *   **条件**: 出现关键K线形态（例如，在局部低点，成交量收缩的阳线）。
        *   **共振**: 如果 `J-2` 级别的 `EMA(55)` 也在此处形成共振支撑，则信号胜率大幅提高。

    3.  **核心卖出/止损触发器**:
        *   **级别**: `J-1` (例如，60分钟图)。
        *   **条件**: 价格**有效跌破** `BBM(20, 2.0)`。
        *   **有效跌破定义**: 连续两根K线的收盘价低于中轨，或一根实体较大的阴线直接跌破。
        *   **理论要点**: 主涨段的结束，必须以 `J-1` 级别的锁定解除（即趋势破坏）为标志。

---

#### **IV. 整合策略信号**

将以上三层逻辑整合，形成具体的交易信号：

1.  **标准买入信号 (Standard Buy)**:
    *   `[J+2 时空]` 处于 **“极强”** 状态。
    *   `AND`
    *   `[J 结构]` 形成 **“主升段”** 结构 (突破前高)。
    *   `AND`
    *   `[J-1 均线]` 回踩 `BBM` 或 `EMA(55)` 并获得 **“有效支撑”**。

2.  **重仓买入信号 (Heavy Buy)**:
    *   满足 **“标准买入信号”** 的所有条件。
    *   `AND`
    *   `[J+1 时空]` 也进入了 **“极强”** 状态 (这是最强的加强信号)。
    *   `OR` (备选加强信号)
    *   `[J-1 结构]` 本身也形成了自己的“主升段”结构。

3.  **加仓信号 (Add-on)**:
    *   (适用于已持仓) 在主趋势未破坏的前提下，每一次 `[J-1 均线]` 级别的有效支撑，都可视为加仓机会。

4.  **卖出/止损信号 (Exit)**:
    *   `[J-1 均线]` **有效跌破** `BBM(20, 2.0)`。

---
---

# 量化策略系统使用指南

## 📁 项目结构

```
quant-lab/
├── strategy/                           # 策略代码目录
│   ├── __init__.py                    # 策略模块初始化
│   ├── run_strategies.py              # 🚀 通用策略运行器  
│   ├── run_trinity_scanner.py         # 三位一体策略运行器
│   └── trinity/                       # 三位一体策略实现
│       ├── __init__.py
│       ├── indicators.py              # 技术指标计算
│       ├── signals.py                 # 信号识别逻辑
│       ├── scanner.py                 # 主扫描器
│       └── stock_info.py              # 股票信息管理
├── logs/                              # 策略结果输出目录
│   └── strategy_trinity/              # 三位一体策略结果
│       ├── trinity_exit_signals_*.csv
│       ├── trinity_buy_signals_*.csv
│       ├── trinity_heavy_buy_signals_*.csv
│       └── trinity_add_position_signals_*.csv
└── data/                              # 数据目录
    └── cn/                           # 中国A股数据
        ├── 1mo/, 1wk/, 1d/, 1h/, 15m/ # 各时间框架数据
```

## 🚀 使用方法

### 方法一：直接运行三位一体策略（推荐）

```bash
# 进入策略目录
cd strategy

# 运行三位一体策略
python run_trinity_scanner.py
```

### 方法二：使用通用策略运行器

```bash
# 进入策略目录
cd strategy

# 列出所有可用策略
python run_strategies.py --list

# 运行三位一体策略（默认）
python run_strategies.py trinity
# 或者
python run_strategies.py
```

> **使用建议**：如果只使用三位一体策略，推荐使用方法一；如果计划使用多种策略，推荐使用方法二。

## 📊 策略说明

### 三位一体主涨段择时策略 (trinity) v2.2

**策略原理**：
- 🗓️ **月线级别**：MACD处于"极强"状态（DIF上穿零轴到DEA上穿零轴区间）
- 📈 **日线级别**：价格突破过去60个交易日高点，形成主升结构
- ⏰ **60分钟级别**：在EMA55或布林带中轨获得有效支撑
- 🕐 **15分钟级别**：关键K线确认（局部低点阳线，成交量收缩）

**v2.2重大优化**：
1. **🎯 买入精细化**：增加"回调健康度"分析，只在EARLY/MIDDLE阶段买入，避免LATE阶段的高风险买点
2. **💰 加仓量化**：引入"风险收益比"计算，确保每次加仓都有>2.0的风险收益比支持
3. **⚠️ 卖出前瞻性**：新增"上涨乏力"预警机制，通过日线布林带扩张+60分钟RSI>80提前识别顶部风险

**信号类型**：
1. **🚨 卖出警示**：60分钟线跌破布林带中轨，建议减仓或止损
2. **📈 标准买入**：满足基础三位一体条件，建议50-60%仓位
3. **💪 重仓买入**：周线级别也极强(J+1时空共振)，建议70-80%仓位
4. **➕ 加仓机会**：适用于已持仓股票的回踩加仓点，风险收益比>2.0
5. **⚠️ 减仓预警**：上涨乏力警示，RSI>80且布林带扩张，建议减仓

## 📋 输出文件格式

策略运行后会在 `logs/strategy_trinity/` 目录生成CSV文件：

### 文件命名规范
- `trinity_exit_signals_YYYYMMDD_HHMMSS.csv` - 卖出警示信号
- `trinity_buy_signals_YYYYMMDD_HHMMSS.csv` - 标准买入信号  
- `trinity_heavy_buy_signals_YYYYMMDD_HHMMSS.csv` - 重仓买入信号
- `trinity_add_position_signals_YYYYMMDD_HHMMSS.csv` - 加仓机会信号
- `trinity_take_profit_alerts_YYYYMMDD_HHMMSS.csv` - 减仓预警信号（v2.2新增）

### CSV字段说明
| 字段 | 说明 |
|------|------|
| `timestamp` | 扫描时间戳 |
| `date` | 扫描日期 |
| `symbol` | 股票代码 |
| `name` | 股票名称 |
| `market` | 市场板块（深交所主板/创业板/科创板等）|
| `signal_type` | 信号类型 |
| `action` | 操作建议（买入/卖出/加仓）|
| `priority` | 优先级（HIGH/MEDIUM/LOW）|
| `position_size` | 建议仓位比例 |
| `support_type` | 支撑类型（EMA_55/BBM_20_2.0）|
| `monthly_macd_strong` | 月线MACD是否强势（Y/N）|
| `daily_breakout` | 日线是否突破（Y/N）|
| `min15_confirmed` | 15分钟是否确认（Y/N）|
| `weekly_macd_strong` | 周线MACD是否极强（Y/N）|
| `pullback_duration_bars` | 回调持续K线数量（v2.2新增）|
| `pullback_depth_pct` | 回调深度百分比（v2.2新增）|
| `pullback_status` | 回调状态：EARLY/MIDDLE/LATE（v2.2新增）|
| `risk_reward_ratio` | 风险收益比（v2.2新增）|
| `alert_strength` | 预警强度：LOW/MEDIUM/HIGH（v2.2新增）|
| `trigger_logic` | 触发逻辑描述 |
| `risk_level` | 风险等级 |
| `notes` | 操作备注和建议 |

## 🔄 添加新策略

要添加新的策略，请按以下步骤：

1. **创建策略目录**：
   ```bash
   mkdir strategy/your_strategy_name
   ```

2. **实现策略模块**：
   ```python
   # strategy/your_strategy_name/__init__.py
   from .scanner import run_scanner
   
   # strategy/your_strategy_name/scanner.py  
   def run_scanner(data_root='../data/cn', save_results=True):
       # 策略实现逻辑
       pass
   ```

3. **创建策略运行器**：
   ```python
   # strategy/run_your_strategy_scanner.py
   from strategy.your_strategy_name import run_scanner
   # 运行逻辑
   ```

4. **注册策略**：
   在 `strategy/__init__.py` 的 `AVAILABLE_STRATEGIES` 中添加策略信息

5. **测试运行**：
   ```bash
   cd strategy
   python run_strategies.py your_strategy_name
   ```

## 💡 最佳实践

1. **数据准备**：确保 `data/cn/` 目录包含完整的多时间框架数据
2. **定期运行**：建议每日收盘后运行策略扫描
3. **结果分析**：优先关注高优先级信号，结合风险等级制定交易计划
4. **历史跟踪**：保留历史CSV文件，便于策略效果回溯分析
5. **风险控制**：严格按照策略建议的仓位比例执行，设置止损位

## ⚠️ 风险提示

- 本策略仅供参考，不构成投资建议
- 市场有风险，投资需谨慎
- 建议结合基本面分析和风险管理原则使用
- 策略信号可能存在滞后性，请谨慎操作

## 🛠️ 技术支持

如有问题或建议，请查看：
- 策略代码注释
- CSV输出文件的详细字段信息
- 历史扫描结果进行对比分析

---

## 🔍 Trinity策略信号识别规则详解

### 📋 信号检测优先级流程

```
1. exit (卖出) - 最高优先级，发现立即返回
2. take_profit_alert (减仓预警) - 上涨乏力警示  
3. standard_buy/heavy_buy (买入) - 三位一体条件验证
4. add_on (加仓) - 支撑条件+风险收益比>2.0
```

### 🚨 **信号类型1: exit (卖出信号)**

#### 识别规则
采用**多级别对称验证**，以下任一条件满足即触发卖出：

**月线级别恶化判断：**
- `DEA`从≤0转为>0（极强状态结束）
- `DIF`从>0转为≤0（趋势完全逆转）

**日线级别结构破坏：**
- 当前价格跌破过去20日内的重要支撑位
- 具体：`current_close < min(low[-20:])`

**60分钟级别支撑失效：**
- 收盘价跌破布林带中轨：`close < BBM_20_2.0`
- 跌破EMA55：`close < EMA_55`

#### 输出格式
```csv
signal_type: exit
action: 卖出  
priority: HIGH
risk_level: 高风险
reason: 月线时空状态恶化 + 60分钟跌破关键支撑
notes: 建议立即卖出或减仓
```

---

### ⚠️ **信号类型2: take_profit_alert (减仓预警)**

#### 识别规则
**上涨乏力警示系统**，通过以下技术指标组合判断：

**基础条件（必须满足）：**
- 日线布林带扩张（BB宽度比 > 1.5）
- 60分钟RSI超买（RSI > 80）

**强度分级：**
- **HIGH强度**：RSI > 95 且 BB宽度比 > 2.5
  - 建议操作：`REDUCE_POSITION_50%`
- **MEDIUM强度**：RSI > 85 且 BB宽度比 > 2.0  
  - 建议操作：`REDUCE_POSITION_30%`
- **LOW强度**：RSI > 80 且 BB宽度比 > 1.5
  - 建议操作：`REDUCE_POSITION_20%`

#### 输出格式
```csv
signal_type: take_profit_alert
action: 减仓预警
priority: HIGH/MEDIUM (根据强度)
risk_level: 注意风险
alert_strength: HIGH/MEDIUM/LOW
suggested_action: REDUCE_POSITION_XX%
bb_width_ratio: 2.81
rsi_60m: 99.73
notes: 上涨乏力警示，建议操作：REDUCE_POSITION_50%，RSI=99.73
```

---

### 📈 **信号类型3: standard_buy (标准买入)**

#### 识别规则
**三位一体核心条件**，所有条件必须同时满足：

**1. 月线时空状态（J+2级别）：**
- `get_latest_spacetime_state(月线) == 'EXTREMELY_STRONG'`
- 即：`DIF > 0` 且 `DEA ≤ 0`

**2. 日线主升结构（J级别）：**
- `check_latest_main_rise_structure(日线) == True`  
- 即：过去20日内突破前60日高点，且未有效跌破

**3. 60分钟有效支撑（J-1级别）：**
- 价格回踩并获得支撑：
  - `EMA_55支撑`：`low <= EMA_55 and close > EMA_55`
  - `BBM支撑`：`low <= BBM_20_2.0 and close > BBM_20_2.0`

**4. 回调健康度检查：**
- 只在`EARLY`或`MIDDLE`阶段买入
- `LATE`阶段回调过深，跳过买入信号

**5. 仓位分配：**
- 15分钟确认：60%仓位
- 15分钟未确认：50%仓位

#### 输出格式
```csv
signal_type: standard_buy
action: 买入
priority: MEDIUM
risk_level: 中等风险
position_size: 50%
support_type: EMA_55
monthly_macd_strong: Y
daily_breakout: Y  
weekly_macd_strong: N
hourly_structure: N
min15_confirmed: N
pullback_duration_bars: 2
pullback_depth_pct: 0.88
pullback_status: EARLY
reason: 月线极强 + 日线主升结构 + 60分钟回踩55EMA支撑
notes: 建议50%仓位，支撑：EMA_55，回调2根K线
```

---

### 💪 **信号类型4: heavy_buy (重仓买入)**

#### 识别规则
在满足**standard_buy**全部条件基础上，增加以下**加强条件**之一：

**条件1：周线级别共振（推荐）**
- `get_latest_spacetime_state(周线) == 'EXTREMELY_STRONG'`
- J+1时空状态也进入极强，多级别共振

**条件2：60分钟主升结构（备选）**  
- `check_hourly_main_rise_structure(60分钟) == True`
- J-1级别本身也形成主升段结构

**仓位分配：**
- 15分钟确认：80%仓位
- 15分钟未确认：70%仓位

#### 输出格式
```csv
signal_type: heavy_buy
action: 重仓买入
priority: HIGH  
risk_level: 积极进取
position_size: 70%
support_type: EMA_55
monthly_macd_strong: Y
daily_breakout: Y
weekly_macd_strong: Y  # 或 hourly_structure: Y
hourly_structure: N    # 或 weekly_macd_strong: N
min15_confirmed: N
reason: 月线极强 + 日线主升结构 + 周线同时进入极强时空状态 + 60分钟回踩55EMA支撑
notes: 建议70%仓位，支撑：EMA_55，回调2根K线
```

---

### ➕ **信号类型5: add_on (加仓信号)**

#### 识别规则
**适用于已持仓股票**，在趋势未破坏前提下的回踩加仓机会：

**基础条件：**
- 有效支撑：满足60分钟支撑条件（同买入信号）
- 部分强势条件：月线极强 `OR` 日线主升结构（不要求同时满足）

**关键约束：风险收益比**
- `calculate_risk_reward_ratio() > 2.0`
- 风险距离：当前价格到支撑位距离
- 收益距离：当前价格到前期高点距离  
- 确保每次加仓都有足够安全边际

**仓位分配：**
- 固定20%仓位加仓

#### 输出格式
```csv
signal_type: add_on
action: 加仓
priority: LOW
risk_level: 低风险  
position_size: 20%
risk_reward_ratio: 6.61
risk_distance_pct: 0.85
reward_distance_pct: 5.59
reason: 回踩BBM_20_2.0获支撑，风险收益比6.61
notes: 适合已持仓股票，建议加仓20%，风险收益比6.61
```

---

### 🔄 **技术指标计算说明**

**MACD参数：**
- `MACD(12, 26, 9)`
- `DIF = EMA12 - EMA26`
- `DEA = EMA9(DIF)`

**移动平均线：**
- `EMA(55)`: 55周期指数移动平均
- `BBM_20_2.0`: 布林带中轨（20周期移动平均）

**RSI参数：**
- `RSI(14)`: 14周期相对强弱指标

**回调健康度：**
- `EARLY`: 回调≤5根K线且深度≤3%
- `MIDDLE`: 回调6-15根K线且深度3-8%  
- `LATE`: 回调>15根K线或深度>8%

**风险收益比：**
- `风险距离 = (当前价 - 支撑价) / 当前价 * 100`
- `收益距离 = (目标价 - 当前价) / 当前价 * 100`
- `风险收益比 = 收益距离 / 风险距离`

---

三位一体流程:
  1. 时空状态检查 (月线MACD极强?)
     ↓ 如果不是极强状态，直接跳过该股票
  2. 结构确认 (日线主升段?)
     ↓ 如果没有结构，考虑观察
  3. 均线买卖点 (60分钟支撑/跌破?)
     ↓ 生成具体买卖信号
  4. 辅助确认 (15分钟关键K线?)
     ↓ 调整仓位大小