# 量化策略系统 (Quantitative Trading Strategies)

## 📋 策略概览

本项目包含两套独立的股票选择策略回测系统：

### 🛠️ **技术策略** (`technical_strategy_backtest.py`)
- **核心**: 基于技术分析的多因子选股
- **特点**: 动量、趋势、成交量、波动四因子模型
- **适用**: 稳定收益，风险可控
- **验证**: 美股表现优秀 (90.46%收益，1.434夏普比率)

### 🤖 **Kronos AI策略** (`kronos_strategy_backtest.py`) 
- **核心**: Kronos大语言模型智能排序
- **特点**: AI预测 + 技术分析Fallback双模式
- **适用**: 500+只股票大规模排序
- **配置**: Top5持仓，每周重平衡，T+1执行

## 🚀 快速使用

### 运行技术策略回测
```bash
cd strategy
python technical_strategy_backtest.py
```

### 运行Kronos AI策略回测  
```bash
cd strategy
python kronos_strategy_backtest.py
```

## 📊 策略对比

| 特征 | 技术策略 | Kronos策略 |
|------|----------|------------|
| **模型复杂度** | 中等 | 高 (AI模型) |
| **候选股票** | 100只 | 500+只 |
| **持仓数量** | Top10 | Top5 |
| **重平衡** | 每月 | 每周 |
| **运行速度** | 快 | 慢 (AI模式) |
| **依赖要求** | 基础 | 高 (AI依赖) |
| **验证状态** | ✅ 已验证 | 🔄 开发中 |

## 📁 文件说明

### 核心策略文件
- `technical_strategy_backtest.py` - 技术分析策略完整回测系统
- `kronos_strategy_backtest.py` - Kronos AI策略完整回测系统
- `reports/` - 回测结果报告目录

### 相关目录
- `recall/technical_stock_selection/` - 技术分析策略开发历程
- `recall/kronos/` - Kronos AI策略开发历程

## 🎯 使用建议

- **新手用户**: 推荐从`technical_strategy_backtest.py`开始
- **AI研究**: 使用`kronos_strategy_backtest.py`进行实验
- **生产环境**: 技术策略更稳定可靠
- **研究开发**: Kronos策略更具前瞻性

---

**注意**: 两套策略代码完全独立，互不干扰，可根据需求选择使用。
