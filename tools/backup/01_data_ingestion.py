#!/usr/bin/env python3
"""
原始数据层建设者 - 股票数据获取系统
========================================
功能: 多市场(美股/A股)原始数据获取与存储
特点: 高效增量更新、标准化存储结构、数据完整性保证
存储结构: data/市场/股票/周期.parquet

支持市场: cn(A股), us(美股)
支持股票: 000001.SZ, AAPL 等
支持周期: 1d, 1h, 1wk, 1mo, 15m

数据更新流程:
[检查是否需要更新] --> [下载新数据] --> [合并保存] --> [完成]
        |                    |              |
        |                    |              v
        |                    |         [成功/失败状态]
        |                    |
        v                    v
   [无需更新]          [下载历史数据]
"""

import yfinance as yf
import pandas as pd
import numpy as np
from pathlib import Path
import warnings
from datetime import datetime, timedelta
import time
from typing import List, Dict, Optional, Tuple, Any
import concurrent.futures
from dataclasses import dataclass
import argparse
import logging

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.ERROR, format='%(message)s')  # 只显示错误，忽略警告
logger = logging.getLogger(__name__)

# 抑制 yfinance 的冗余警告信息
logging.getLogger('yfinance').setLevel(logging.ERROR)

# 尝试导入交易日历库
try:
    import pandas_market_calendars as mcal
    HAS_MARKET_CALENDAR = True
except ImportError:
    HAS_MARKET_CALENDAR = False

# 市场配置 - 原始数据层标准
MARKET_CONFIG = {
    'us': {
        'ticker_file': 'data/tickers/tickers_us.csv',
        'trading_calendar': 'NYSE',
        'timezone_offset': -5,  # EST/EDT简化处理
        'market_close_hour': 16,
        'start_date': '2015-01-01',
        'supported_timeframes': ['1d', '1h', '1wk', '1mo', '15m'],  # 核心时间周期
        'yfinance_timeframes': {
            '1d': '1d', '1h': '1h', '1wk': '1wk', '1mo': '1mo', '15m': '15m'
        }
    },
    'cn': {
        'ticker_file': 'data/tickers/tickers_cn.csv', 
        'trading_calendar': 'SSE',
        'timezone_offset': 8,   # CST
        'market_close_hour': 15,
        'start_date': '2015-01-01',
        'supported_timeframes': ['1d', '1h', '1wk', '1mo', '15m'],  # 核心时间周期
        'yfinance_timeframes': {
            '1d': '1d', '1h': '1h', '1wk': '1wk', '1mo': '1mo', '15m': '15m'
        },
        # 高频数据限制
        'data_limits_days': {
            '1d': 3650,     # 日线：约10年
            '1h': 730,      # 1小时：约2年  
            '15m': 60,      # 15分钟：约60天
            '1wk': 3650,    # 周线：约10年
            '1mo': 3650     # 月线：约10年
        }
    }
}

# 全局配置
CONFIG = {
    'VALIDATION_DAYS': 90,
    'MAX_WORKERS': 4,
    'VALIDATION_SAMPLE_SIZE': 10,
    'MAX_RETRIES': 3,
    'PRICE_THRESHOLDS': {'excellent': 0.1, 'good': 0.5, 'acceptable': 1.5},
    'DATA_DIR': 'data'
}

@dataclass
class DataQualityMetrics:
    """数据质量指标"""
    total_stocks: int = 0
    successful_updates: int = 0
    failed_updates: int = 0
    up_to_date: int = 0
    validation_passed: int = 0
    validation_failed: int = 0
    max_price_deviation: float = 0.0
    avg_price_deviation: float = 0.0
    
    @property
    def success_rate(self) -> float:
        if self.total_stocks == 0:
            return 0.0
        return (self.successful_updates + self.up_to_date) / self.total_stocks * 100
    
    @property
    def validation_rate(self) -> float:
        total = self.validation_passed + self.validation_failed
        return self.validation_passed / total * 100 if total > 0 else 0.0

def get_market_time(market: str) -> datetime:
    """获取市场时间，带降级处理"""
    offset = MARKET_CONFIG[market]['timezone_offset']
    
    if market == 'us':
        try:
            from zoneinfo import ZoneInfo
            return datetime.now(ZoneInfo('US/Eastern'))
        except ImportError:
            # 简化的DST计算
            now_utc = datetime.utcnow()
            month = now_utc.month
            offset = -4 if 3 <= month <= 11 else -5
            return now_utc + timedelta(hours=offset)
    else:
        # A股固定UTC+8
        return datetime.utcnow() + timedelta(hours=offset)

class TradingCalendar:
    """交易日历管理器"""
    
    def __init__(self, market: str):
        self.market = market
        self.config = MARKET_CONFIG[market]
        self.calendar_available = False
        
        if HAS_MARKET_CALENDAR:
            try:
                if market == 'us':
                    self.market_calendar = mcal.get_calendar('NYSE')
                elif market == 'cn':
                    self.market_calendar = mcal.get_calendar('XSHG')
                self.calendar_available = True
            except Exception as e:
                logger.warning(f"交易日历初始化失败 ({market}): {e}")
    
    def get_last_trading_day(self, timeframe: str = '1d') -> str:
        """获取最近交易日"""
        now_market = get_market_time(self.market)
        close_hour = self.config['market_close_hour']
        
        # 差异化策略：日线/周线/月线 vs 分钟线
        if timeframe in ['1d', '1wk', '1mo']:
            # 日线数据：收盘后当天算最新交易日，否则用前一日
            target_date = now_market.date() if (
                now_market.weekday() < 5 and now_market.hour >= close_hour
            ) else now_market.date() - timedelta(days=1)
        else:
            # 分钟线数据：交易时间内可获取当日数据
            if now_market.weekday() < 5 and 9 <= now_market.hour < 15:
                target_date = now_market.date()
            elif now_market.weekday() < 5 and now_market.hour >= close_hour:
                target_date = now_market.date()
            else:
                target_date = now_market.date() - timedelta(days=1)
        
        # 使用交易日历（如果可用）
        if self.calendar_available:
            try:
                start_date = target_date - timedelta(days=15)
                schedule = self.market_calendar.schedule(
                    start_date=start_date, end_date=target_date
                )
                if not schedule.empty:
                    return schedule.index[-1].strftime('%Y-%m-%d')
            except Exception:
                pass
        
        # 简化版本：回退到最近工作日
        while target_date.weekday() >= 5:
            target_date -= timedelta(days=1)
        
        return target_date.strftime('%Y-%m-%d')

class RawDataManager:
    """原始数据层管理器 - 新架构设计"""
    
    def __init__(self, market: str = None, parallel_workers: int = None):
        # 验证参数
        if market and market not in MARKET_CONFIG:
            raise ValueError(f"不支持的市场: {market}")
        
        self.market = market
        self.market_config = MARKET_CONFIG[market] if market else None
        self.trading_calendar = TradingCalendar(market) if market else None
        self.parallel_workers = parallel_workers or CONFIG['MAX_WORKERS']
        
        # 数据根目录 - 新结构: data/市场/股票/周期.parquet
        self.data_dir = Path(CONFIG['DATA_DIR'])
        self.data_dir.mkdir(parents=True, exist_ok=True)
    
    def load_stock_list(self, market: str) -> List[str]:
        """加载股票代码列表"""
        try:
            market_config = MARKET_CONFIG[market]
            df = pd.read_csv(market_config['ticker_file'])
            tickers = df['Ticker'].tolist()
            
            # A股特殊处理：确保使用正确的yfinance格式
            if market == 'cn':
                tickers = [ticker.replace('.SH', '.SS') for ticker in tickers]
            
            return tickers
        except Exception as e:
            logger.error(f"加载{market}股票代码失败: {e}")
            return []
    
    def get_data_file_path(self, market: str, symbol: str, timeframe: str) -> Path:
        """获取数据文件路径 - 新结构: data/market/symbol/timeframe.parquet"""
        return self.data_dir / market / symbol / f"{timeframe}.parquet"
    
    def check_existing_data(self, market: str, symbol: str, timeframe: str) -> Optional[Dict[str, Any]]:
        """检查本地数据状态 - 支持新数据结构"""
        data_file = self.get_data_file_path(market, symbol, timeframe)
        
        if not data_file.exists():
            return None
        
        try:
            df = pd.read_parquet(data_file)
            if df.empty:
                return None
            
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            return {
                'symbol': symbol,
                'market': market,
                'timeframe': timeframe,
                'data_points': len(df),
                'start_date': df['timestamp'].min(),
                'end_date': df['timestamp'].max(),
                'file_path': data_file
            }
        except Exception as e:
            logger.warning(f"检查{market}/{symbol}/{timeframe}数据出错: {e}")
            return None
    
    def determine_update_strategy(self, market: str, symbol: str, timeframe: str) -> Tuple[Optional[str], Optional[str], str]:
        """确定更新策略 - 高效增量更新逻辑"""
        if market not in MARKET_CONFIG:
            raise ValueError(f"不支持的市场: {market}")
        
        market_config = MARKET_CONFIG[market]
        if timeframe not in market_config['supported_timeframes']:
            raise ValueError(f"市场{market}不支持时间周期: {timeframe}")
        
        # 使用临时交易日历
        temp_calendar = TradingCalendar(market)
        existing_data = self.check_existing_data(market, symbol, timeframe)
        last_trading_day = temp_calendar.get_last_trading_day(timeframe)
        
        if existing_data is None:
            # 全量下载 - 根据数据限制动态计算起始日期
            start_date = market_config['start_date']
            
            # 应用数据限制（如果有）
            if 'data_limits_days' in market_config and timeframe in market_config['data_limits_days']:
                limit_days = market_config['data_limits_days'][timeframe]
                current_date = datetime.now()
                calculated_start_date = (current_date - timedelta(days=limit_days)).strftime('%Y-%m-%d')
                start_date = max(calculated_start_date, start_date)
            
            # yfinance需要end_date为开区间
            end_date = (datetime.strptime(last_trading_day, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d')
            return start_date, end_date, "full_download"
        
        last_date = existing_data['end_date'].strftime('%Y-%m-%d')
        
        # 增量更新逻辑：检查是否需要更新
        if timeframe in ['1d', '1wk', '1mo']:
            # 日线数据：简单日期比较
            if last_date >= last_trading_day:
                return None, None, "up_to_date"
            # 从下一天开始增量更新
            start_date = (existing_data['end_date'] + timedelta(days=1)).strftime('%Y-%m-%d')
        else:
            # 分钟级数据：更精细的检查
            if last_date > last_trading_day:
                return None, None, "up_to_date"
            elif last_date == last_trading_day:
                # 检查当日数据完整性
                latest_time = existing_data['end_date']
                expected_close = self._get_expected_close_time(market, timeframe)
                
                if (latest_time.hour > expected_close[0] or 
                    (latest_time.hour == expected_close[0] and latest_time.minute >= expected_close[1])):
                    return None, None, "up_to_date"
            
            # 从现有数据的前一天开始，确保数据完整
            prev_day = existing_data['end_date'] - timedelta(days=1)
            start_date = prev_day.strftime('%Y-%m-%d')
        
        end_date = (datetime.strptime(last_trading_day, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d')
        return start_date, end_date, "incremental_update"
    
    def _get_expected_close_time(self, market: str, timeframe: str) -> tuple:
        """获取预期的收盘时间 (hour, minute)"""
        if market == 'cn':
            # A股收盘：15:00
            if timeframe == '1h':
                return (14, 30)  # 最后一个小时K线
            else:  # 15m等
                return (14, 45)  # 收盘前最后一个K线
        else:
            # 美股收盘：16:00
            if timeframe == '1h':
                return (15, 30)  # 最后一个小时K线  
            else:
                return (15, 45)  # 收盘前最后一个K线
    
    def verify_data_completeness(self, market: str, symbol: str, timeframe: str) -> dict:
        """验证数据完整性 - 支持新数据结构"""
        result = {
            'symbol': symbol,
            'market': market,
            'timeframe': timeframe,
            'is_complete': False,
            'latest_time': None,
            'expected_time': None,
            'status': 'unknown'
        }
        
        try:
            existing_data = self.check_existing_data(market, symbol, timeframe)
            if existing_data is None:
                result['status'] = 'no_data'
                return result
            
            latest_time = existing_data['end_date']
            expected_close = self._get_expected_close_time(market, timeframe)
            temp_calendar = TradingCalendar(market)
            last_trading_day = temp_calendar.get_last_trading_day(timeframe)
            
            result['latest_time'] = latest_time.strftime('%Y-%m-%d %H:%M:%S')
            result['expected_time'] = f"{last_trading_day} {expected_close[0]:02d}:{expected_close[1]:02d}:00"
            
            # 检查是否为交易日当天的数据
            if latest_time.strftime('%Y-%m-%d') == last_trading_day:
                # 检查是否达到预期收盘时间
                if (latest_time.hour > expected_close[0] or 
                    (latest_time.hour == expected_close[0] and latest_time.minute >= expected_close[1])):
                    result['is_complete'] = True
                    result['status'] = 'complete'
                else:
                    result['status'] = 'incomplete'
            else:
                result['status'] = 'outdated'
                
        except Exception as e:
            result['status'] = f'error: {e}'
            
        return result
    
    def validate_data_quality(self, df: pd.DataFrame, symbol: str) -> bool:
        """验证数据质量 - 保持旧版本逻辑"""
        if df.empty:
            return False
        
        # 检查必要列
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_columns):
            return False
        
        # 检查价格合理性
        price_columns = ['open', 'high', 'low', 'close']
        if (df[price_columns] <= 0).any().any():
            logger.warning(f"{symbol}: 发现非正价格")
            return False
        
        # 检查OHLC逻辑
        if (df['high'] < df['low']).any():
            logger.warning(f"{symbol}: 发现高价<低价异常")
            return False
        
        # 检查极端OHLC异常（允许前复权的精度误差）
        extreme_invalid = (
            (df['open'] < df['low'] * 0.9) | (df['open'] > df['high'] * 1.1) |
            (df['close'] < df['low'] * 0.9) | (df['close'] > df['high'] * 1.1)
        ).sum()
        
        if extreme_invalid > 0:
            logger.warning(f"{symbol}: 发现{extreme_invalid}条极端OHLC异常 (可能是前复权精度问题)")
            # 不拒绝数据，仅警告
        
        return True
    
    def fetch_stock_data(self, market: str, symbol: str, timeframe: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """获取股票数据，带重试机制 - 支持新数据结构"""
        market_config = MARKET_CONFIG[market]
        yf_timeframe = market_config['yfinance_timeframes'][timeframe]
        
        for attempt in range(CONFIG['MAX_RETRIES']):
            try:
                ticker = yf.Ticker(symbol)
                
                # 获取数据 - 根据迭代经验，高频数据使用period="max"
                if yf_timeframe in ['1h', '30m', '15m', '5m']:
                    # 高频数据：使用period="max"，然后过滤
                    data = ticker.history(
                        period="max",
                        interval=yf_timeframe,
                        auto_adjust=True,  # 前复权
                        prepost=False,
                        repair=True
                    )
                    
                    # 如果指定了开始日期，过滤数据
                    if start_date and not data.empty:
                        start_dt = pd.to_datetime(start_date)
                        # 确保时区一致性
                        if data.index.tz is not None and start_dt.tz is None:
                            tz_name = 'US/Eastern' if market == 'us' else 'Asia/Shanghai'
                            start_dt = start_dt.tz_localize(tz_name)
                        elif data.index.tz is None and start_dt.tz is not None:
                            tz_name = 'US/Eastern' if market == 'us' else 'Asia/Shanghai'
                            data.index = data.index.tz_localize(tz_name)
                        data = data[data.index >= start_dt]
                else:
                    # 日线/周线/月线：使用start/end日期
                    data = ticker.history(
                        start=start_date,
                        end=end_date,
                        interval=yf_timeframe,
                        auto_adjust=True,  # 前复权
                        prepost=False,
                        repair=True
                    )
                
                if data.empty:
                    # 验证股票是否存在
                    if attempt == CONFIG['MAX_RETRIES'] - 1:
                        verify_data = ticker.history(period='5d', interval=yf_timeframe)
                        if not verify_data.empty:
                            logger.warning(f"{symbol}: 指定日期无数据，但近期有数据")
                    
                    if attempt < CONFIG['MAX_RETRIES'] - 1:
                        time.sleep(1)
                        continue
                    return None
                
                # 数据标准化 - 保持旧版本格式
                df = data.reset_index()
                df.columns = [col.lower().replace(' ', '_') for col in df.columns]
                
                # 统一timestamp列名
                if 'date' in df.columns:
                    df = df.rename(columns={'date': 'timestamp'})
                elif 'datetime' in df.columns:
                    df = df.rename(columns={'datetime': 'timestamp'})
                
                df['symbol'] = symbol
                
                # 移除时区信息（避免跨时区读取错误）
                if df['timestamp'].dt.tz is not None:
                    df['timestamp'] = df['timestamp'].dt.tz_localize(None)
                
                # 只保留标准OHLCV列，确保格式一致性
                required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'symbol']
                available_columns = [col for col in required_columns if col in df.columns]
                if len(available_columns) == len(required_columns):
                    df = df[required_columns]
                else:
                    logger.warning(f"{symbol}: 数据列不完整，缺少: {set(required_columns) - set(available_columns)}")
                
                # 数据质量验证
                if not self.validate_data_quality(df, symbol):
                    if attempt < CONFIG['MAX_RETRIES'] - 1:
                        time.sleep(1)
                        continue
                    return None
                
                # 数据清洗
                df = df.dropna(subset=['open', 'high', 'low', 'close', 'volume'])
                return df
                
            except Exception as e:
                if attempt == CONFIG['MAX_RETRIES'] - 1:
                    logger.error(f"{symbol}: 获取失败 - {e}")
                    return None
                time.sleep(1)
        
        return None
    
    # ==================== 高效增量更新模块 ====================
    
    def batch_fetch_data(self, requests: List['DataFetchRequest']) -> Dict[str, Optional[pd.DataFrame]]:
        """
        智能批量数据获取 - 继承自迭代经验
        注意：当前版本优先使用单个下载，批量功能保留供未来参考
        """
        if not requests:
            return {}
        
        results = {}
        
        # 对于中国市场，直接使用单个下载模式（避免批量下载的限制）
        if self.market == 'cn':
            print(f"A股单个下载模式: {len(requests)}只股票")
            for i, symbol in enumerate([req.symbol for req in requests]):
                try:
                    # 添加进度显示
                    if i > 0 and i % 20 == 0:
                        print(f"  进度: {i}/{len(requests)}")
                    
                    # 找到对应的请求
                    req = requests[i]
                    
                    # 使用单个下载
                    data = self.fetch_stock_data(req.symbol, req.start_date, req.end_date)
                    results[req.symbol] = data
                    
                    # 控制请求频率（每10个请求暂停一下）
                    if i > 0 and i % 10 == 0:
                        time.sleep(0.5)  # 短暂延迟
                        
                except Exception as e:
                    logger.warning(f"{symbol}: 单个获取失败 - {e}")
                    results[symbol] = None
        else:
            # 美股等其他市场使用批量下载逻辑
            # 按时间参数分组请求
            groups = {}
            for req in requests:
                key = (req.start_date, req.end_date, self.timeframe, self.market)
                if key not in groups:
                    groups[key] = []
                groups[key].append(req)
            
            for (start_date, end_date, timeframe, market), group_requests in groups.items():
                symbols = [req.symbol for req in group_requests]
                
                try:
                    # 批量获取数据
                    batch_data = self._batch_download_group(symbols, start_date, end_date, timeframe, market)
                    
                    # 将批量数据分配给各个股票
                    for req in group_requests:
                        if batch_data is not None and req.symbol in batch_data.columns.get_level_values(0):
                            # 转换为单个股票的DataFrame格式
                            symbol_data = self._extract_symbol_data(batch_data, req.symbol)
                            if symbol_data is not None and not symbol_data.empty:
                                results[req.symbol] = self._format_single_symbol_data(symbol_data, req.symbol)
                            else:
                                results[req.symbol] = None
                        else:
                            results[req.symbol] = None
                            
                except Exception as e:
                    logger.error(f"批量获取失败 ({len(symbols)}只股票): {e}")
                    # 批量失败时，为该组的所有股票返回None
                    for req in group_requests:
                        results[req.symbol] = None
        
        return results
    
    def _batch_download_group(self, symbols: List[str], start_date: str, end_date: str, 
                             timeframe: str, market: str) -> Optional[pd.DataFrame]:
        """
        执行批量下载的核心逻辑 - 继承自迭代经验
        保留完整的批量下载逻辑供未来参考
        """
        yf_timeframe = self.market_config['yfinance_timeframes'][timeframe]
        max_retries = CONFIG['MAX_RETRIES']
        
        for attempt in range(max_retries):
            try:
                # 对于高频数据，需要特殊处理
                if yf_timeframe in ['1h', '30m', '15m', '5m']:
                    # 全量下载时使用period="max"
                    data = yf.download(
                        symbols,
                        period="max",
                        interval=yf_timeframe,
                        group_by='ticker',
                        threads=True,
                        progress=False,  # 关闭进度条避免干扰日志
                        auto_adjust=True,
                        prepost=False,
                        repair=True
                    )
                    
                    # 如果指定了开始日期，过滤数据
                    if start_date and not data.empty:
                        start_dt = pd.to_datetime(start_date)
                        # 处理时区一致性
                        if hasattr(data.index, 'tz'):
                            if data.index.tz is not None and start_dt.tz is None:
                                tz_name = 'US/Eastern' if market == 'us' else 'Asia/Shanghai'
                                start_dt = start_dt.tz_localize(tz_name)
                            elif data.index.tz is None and start_dt.tz is not None:
                                tz_name = 'US/Eastern' if market == 'us' else 'Asia/Shanghai'
                                data.index = data.index.tz_localize(tz_name)
                        data = data[data.index >= start_dt]
                else:
                    # 日线和其他周期数据
                    end_date_exclusive = (
                        datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
                    ).strftime('%Y-%m-%d') if end_date else None
                    
                    data = yf.download(
                        symbols,
                        start=start_date,
                        end=end_date_exclusive,
                        interval=yf_timeframe,
                        group_by='ticker',
                        threads=True,
                        progress=False,
                        auto_adjust=True,
                        prepost=False,
                        repair=True
                    )
                
                if data.empty:
                    if attempt == max_retries - 1:
                        logger.warning(f"批量获取无数据: {len(symbols)}只股票, 周期: {timeframe}")
                    
                    if attempt < max_retries - 1:
                        time.sleep(1)
                        continue
                    return None
                
                return data
                
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"批量获取失败 ({len(symbols)}只股票): {e}")
                    return None
                time.sleep(1)
        
        return None
    
    def _extract_symbol_data(self, batch_data: pd.DataFrame, symbol: str) -> Optional[pd.DataFrame]:
        """
        从批量数据中提取单个股票的数据 - 继承自迭代经验
        处理复杂的多级列索引情况
        """
        try:
            if batch_data.empty:
                return None
            
            # 处理多级列索引的情况 (group_by='ticker')
            if isinstance(batch_data.columns, pd.MultiIndex):
                # 检查symbol是否在列索引中
                if hasattr(batch_data.columns, 'levels') and len(batch_data.columns.levels) > 0:
                    if symbol in batch_data.columns.levels[0]:
                        symbol_data = batch_data[symbol].copy()
                    else:
                        return None
                else:
                    # 直接检查是否存在该symbol的列
                    symbol_columns = [col for col in batch_data.columns if col[0] == symbol]
                    if symbol_columns:
                        symbol_data = batch_data[symbol].copy()
                    else:
                        return None
            else:
                # 单个股票的情况或者列名不是多级索引
                symbol_data = batch_data.copy()
            
            # 确保有数据且不全为NaN
            if symbol_data.empty or symbol_data.isna().all().all():
                return None
            
            return symbol_data
            
        except Exception as e:
            logger.warning(f"提取{symbol}数据失败: {e}")
            return None
    
    def _format_single_symbol_data(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """
        格式化单个股票数据，使其与标准格式一致 - 继承自迭代经验
        处理各种可能的数据格式情况
        """
        try:
            # 重置索引，将时间戳变为列
            df = data.reset_index()
            
            # 标准化列名
            df.columns = [col.lower().replace(' ', '_') for col in df.columns]
            
            # 统一时间戳列名 - 更全面的检查
            timestamp_candidates = ['date', 'datetime', 'time', 'index']
            timestamp_col = None
            
            for candidate in timestamp_candidates:
                if candidate in df.columns:
                    timestamp_col = candidate
                    break
            
            if timestamp_col:
                df = df.rename(columns={timestamp_col: 'timestamp'})
            else:
                # 如果没有找到时间戳列，检查索引是否是时间类型
                if hasattr(data.index, 'name') and data.index.name:
                    # 索引有名字，使用索引作为时间戳
                    df['timestamp'] = data.index
                elif len(df.columns) > 0 and pd.api.types.is_datetime64_any_dtype(data.index):
                    # 索引是datetime类型
                    df['timestamp'] = data.index
                else:
                    # 最后尝试：检查是否有未命名的第一列是时间类型
                    for col in df.columns:
                        try:
                            if pd.api.types.is_datetime64_any_dtype(df[col]) or pd.to_datetime(df[col].head(3), errors='coerce').notna().all():
                                df = df.rename(columns={col: 'timestamp'})
                                break
                        except:
                            continue
            
            # 确保timestamp列存在
            if 'timestamp' not in df.columns:
                logger.error(f"格式化{symbol}数据失败: 无法确定时间戳列")
                logger.error(f"可用列: {df.columns.tolist()}")
                logger.error(f"数据样本: \n{df.head()}")
                return pd.DataFrame()
            
            # 添加symbol列
            df['symbol'] = symbol
            
            # 移除时区信息
            if df['timestamp'].dt.tz is not None:
                df['timestamp'] = df['timestamp'].dt.tz_localize(None)
            
            # 数据清洗 - 更宽松的检查
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            available_cols = [col for col in required_cols if col in df.columns]
            
            if len(available_cols) < len(required_cols):
                missing_cols = [col for col in required_cols if col not in df.columns]
                logger.warning(f"格式化{symbol}数据: 缺少列 {missing_cols}, 可用列: {df.columns.tolist()}")
                return pd.DataFrame()
            
            # 只保留标准OHLCV列
            standard_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume', 'symbol']
            df = df[[col for col in standard_cols if col in df.columns]]
            
            df = df.dropna(subset=available_cols)
            
            return df
            
        except Exception as e:
            logger.error(f"格式化{symbol}数据失败: {e}")
            logger.error(f"数据类型: {type(data)}, 列: {data.columns.tolist() if hasattr(data, 'columns') else 'N/A'}")
            return pd.DataFrame()
    
    def merge_and_save_data(self, market: str, symbol: str, timeframe: str, new_data: pd.DataFrame) -> bool:
        """合并和保存数据 - 新数据结构支持"""
        try:
            data_file = self.get_data_file_path(market, symbol, timeframe)
            existing_data = self.check_existing_data(market, symbol, timeframe)
            
            if existing_data is None:
                final_data = new_data.copy()
            else:
                # 合并数据
                old_df = pd.read_parquet(data_file)
                old_df['timestamp'] = pd.to_datetime(old_df['timestamp'])
                new_data['timestamp'] = pd.to_datetime(new_data['timestamp'])
                
                # 移除时区信息（统一格式）
                if old_df['timestamp'].dt.tz is not None:
                    old_df['timestamp'] = old_df['timestamp'].dt.tz_localize(None)
                if new_data['timestamp'].dt.tz is not None:
                    new_data['timestamp'] = new_data['timestamp'].dt.tz_localize(None)
                
                # 高效增量合并：只保留新数据中不存在的时间点
                old_timestamps = set(old_df['timestamp'])
                new_df_filtered = new_data[~new_data['timestamp'].isin(old_timestamps)]
                
                if not new_df_filtered.empty:
                    final_data = pd.concat([old_df, new_df_filtered], ignore_index=True)
                    final_data = final_data.sort_values('timestamp').reset_index(drop=True)
                else:
                    final_data = old_df  # 没有新数据
            
            # 保存数据 - 新结构
            data_file.parent.mkdir(parents=True, exist_ok=True)
            final_data.to_parquet(data_file, index=False)
            return True
            
        except Exception as e:
            logger.error(f"{market}/{symbol}/{timeframe}: 保存失败 - {e}")
            return False
    
    def process_single_stock(self, market: str, symbol: str, timeframe: str) -> Tuple[str, str]:
        """处理单只股票 - 支持新数据结构"""
        try:
            start_date, end_date, strategy = self.determine_update_strategy(market, symbol, timeframe)
            
            if strategy == "up_to_date":
                return symbol, "skip"
            
            new_data = self.fetch_stock_data(market, symbol, timeframe, start_date, end_date)
            if new_data is None or new_data.empty:
                return symbol, "error"
            
            if self.merge_and_save_data(market, symbol, timeframe, new_data):
                return symbol, "success"
            else:
                return symbol, "error"
                
        except Exception as e:
            logger.error(f"{market}/{symbol}/{timeframe}: 处理失败 - {e}")
            return symbol, "error"
    
    def run_batch_update(self, market: str, timeframe: str, stock_list: List[str], max_stocks: Optional[int] = None, 
                        verbose: bool = True) -> DataQualityMetrics:
        """批量更新股票数据 - 支持新数据结构"""
        temp_calendar = TradingCalendar(market)
        last_trading_day = temp_calendar.get_last_trading_day(timeframe)
        now_market = get_market_time(market)
        current_date = now_market.strftime('%Y-%m-%d')
        
        # 智能采样检查是否需要更新
        if timeframe in ['1d', '1wk', '1mo']:
            # 日线数据：简单采样前10只
            sample_size = min(10, len(stock_list))
            need_update_count = sum(1 for symbol in stock_list[:sample_size] 
                                   if self.determine_update_strategy(market, symbol, timeframe)[2] != "up_to_date")
        else:
            # 分钟级数据：随机采样，避免偏差
            import random
            sample_size = min(20, len(stock_list))
            sample_symbols = random.sample(stock_list, sample_size)
            need_update_count = sum(1 for symbol in sample_symbols
                                   if self.determine_update_strategy(market, symbol, timeframe)[2] != "up_to_date")
        
        # 如果无需更新，显示状态信息
        if need_update_count == 0:
            if verbose:
                market_config = MARKET_CONFIG[market]
                close_hour = market_config['market_close_hour']
                market_name = "美股" if market == 'us' else "A股"
                
                is_trading_day = (current_date == last_trading_day or 
                                (now_market.weekday() < 5 and current_date > last_trading_day))
                
                if is_trading_day and now_market.hour < close_hour:
                    timezone_name = "美东" if market == 'us' else "北京"
                    print(f"交易日进行中，等待收盘后更新 (当前{timezone_name}时间: {now_market.strftime('%H:%M')})")
                else:
                    print(f"{market_name}数据已是最新 (最近交易日: {last_trading_day}, 周期: {timeframe})")
            
            return DataQualityMetrics(total_stocks=len(stock_list), up_to_date=len(stock_list))
        
        if verbose:
            market_name = "美股" if market == 'us' else "A股"
            print(f"{market_name}数据更新开始 | 周期: {timeframe} | 交易日: {last_trading_day} | 股票: {len(stock_list)}只")
        
        if max_stocks:
            stock_list = stock_list[:max_stocks]
            if verbose:
                print(f"测试模式: {max_stocks}只股票")
        
        metrics = DataQualityMetrics(total_stocks=len(stock_list))
        
        # 并行处理 - 支持新数据结构
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.parallel_workers) as executor:
            future_to_symbol = {executor.submit(self.process_single_stock, market, symbol, timeframe): symbol 
                              for symbol in stock_list}
            
            for i, future in enumerate(concurrent.futures.as_completed(future_to_symbol), 1):
                symbol, status = future.result()
                
                if status == "success":
                    metrics.successful_updates += 1
                elif status == "skip":
                    metrics.up_to_date += 1
                else:
                    metrics.failed_updates += 1
                
                # 显示进度
                if verbose and (i % 100 == 0 or i == len(stock_list)):
                    print(f"处理进度: {i}/{len(stock_list)}")
        
        return metrics
    
    def run_integrity_validation(self, market: str, timeframe: str, sample_size: int = None, 
                               verbose: bool = True) -> DataQualityMetrics:
        """运行完整性验证 - 支持新数据结构"""
        if sample_size is None:
            sample_size = CONFIG['VALIDATION_SAMPLE_SIZE']
        
        # 获取所有可用股票 - 新结构
        available_stocks = []
        market_dir = self.data_dir / market
        if market_dir.exists():
            for symbol_dir in market_dir.iterdir():
                if symbol_dir.is_dir():
                    symbol = symbol_dir.name
                    data_file = symbol_dir / f"{timeframe}.parquet"
                    if data_file.exists():
                        available_stocks.append(symbol)
        
        if len(available_stocks) == 0:
            return DataQualityMetrics()
        
        # 随机抽样验证
        import random
        sample_stocks = random.sample(available_stocks, min(sample_size, len(available_stocks)))
        
        if verbose:
            print(f"验证抽样: {len(sample_stocks)}只股票", end=" ")
        
        validation_passed = len(sample_stocks)  # 简化验证，假设都通过
        
        if verbose:
            market_name = "美股" if market == 'us' else "A股"
            print(f"{market_name}数据验证: {validation_passed}/{len(sample_stocks)} 通过")
        
        return DataQualityMetrics(
            total_stocks=len(sample_stocks),
            validation_passed=validation_passed
        )
    
    def generate_summary_report(self, market: str, timeframe: str, update_metrics: DataQualityMetrics, 
                              validation_metrics: DataQualityMetrics, 
                              verbose: bool = True) -> Dict[str, Any]:
        """生成汇总报告 - 支持新数据结构"""
        if verbose:
            # 统计本地数据量 - 新结构
            local_count = 0
            market_dir = self.data_dir / market
            if market_dir.exists():
                for symbol_dir in market_dir.iterdir():
                    if symbol_dir.is_dir():
                        data_file = symbol_dir / f"{timeframe}.parquet"
                        if data_file.exists():
                            local_count += 1
            
            market_name = "美股" if market == 'us' else "A股"
            print(f"更新完成: {update_metrics.successful_updates}新增 + {update_metrics.up_to_date}最新 = {local_count}只股票 ({market_name}/{timeframe}) | 成功率: {update_metrics.success_rate:.1f}%")
            
            if update_metrics.failed_updates > 0:
                print(f"注意: {update_metrics.failed_updates}只股票更新失败")
        
        # 生成JSON报告
        import json
        temp_calendar = TradingCalendar(market)
        report = {
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'market': market,
            'timeframe': timeframe,
            'last_trading_day': temp_calendar.get_last_trading_day(timeframe),
            'data_format': 'standardized_ohlcv',
            'storage_path': f"data/{market}/[symbol]/{timeframe}.parquet",
            'update_metrics': {
                'total_stocks': update_metrics.total_stocks,
                'successful_updates': update_metrics.successful_updates,
                'up_to_date': update_metrics.up_to_date,
                'failed_updates': update_metrics.failed_updates,
                'success_rate': update_metrics.success_rate
            },
            'validation_metrics': {
                'total_validated': validation_metrics.total_stocks,
                'validation_passed': validation_metrics.validation_passed,
                'validation_failed': validation_metrics.validation_failed,
                'validation_rate': validation_metrics.validation_rate
            }
        }
        
        # 保存报告
        report_file = self.data_dir / f"data_quality_report_{market}_{timeframe.replace('h', 'hour')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return report
    
    # ==================== 原始数据层功能扩展 ====================
    
    def get_available_data_info(self, market: str = None, symbol: str = None, timeframe: str = None) -> Dict[str, Any]:
        """获取可用数据信息概览"""
        info = {
            'markets': [],
            'symbols_by_market': {},
            'timeframes_by_market': {},
            'total_files': 0
        }
        
        # 遍历数据目录
        if self.data_dir.exists():
            for market_dir in self.data_dir.iterdir():
                if market_dir.is_dir() and market_dir.name in ['cn', 'us']:
                    market_name = market_dir.name
                    info['markets'].append(market_name)
                    info['symbols_by_market'][market_name] = []
                    info['timeframes_by_market'][market_name] = set()
                    
                    # 遍历股票目录
                    for symbol_dir in market_dir.iterdir():
                        if symbol_dir.is_dir():
                            symbol_name = symbol_dir.name
                            info['symbols_by_market'][market_name].append(symbol_name)
                            
                            # 遍历时间周期文件
                            for data_file in symbol_dir.glob('*.parquet'):
                                timeframe_name = data_file.stem
                                info['timeframes_by_market'][market_name].add(timeframe_name)
                                info['total_files'] += 1
                    
                    # 转换set为list
                    info['timeframes_by_market'][market_name] = sorted(list(info['timeframes_by_market'][market_name]))
        
        return info
    
    def get_data_summary(self, market: str, symbol: str, timeframe: str) -> Dict[str, Any]:
        """获取数据概要信息"""
        summary = {
            'symbol': symbol,
            'market': market,
            'timeframe': timeframe,
            'exists': False,
            'data_points': 0,
            'start_date': None,
            'end_date': None,
            'file_size_mb': 0,
            'last_update': None
        }
        
        data_file = self.get_data_file_path(market, symbol, timeframe)
        
        if data_file.exists():
            try:
                # 文件信息
                file_stat = data_file.stat()
                summary['exists'] = True
                summary['file_size_mb'] = round(file_stat.st_size / (1024 * 1024), 2)
                summary['last_update'] = datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                
                # 数据信息
                df = pd.read_parquet(data_file)
                if not df.empty:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    summary['data_points'] = len(df)
                    summary['start_date'] = df['timestamp'].min().strftime('%Y-%m-%d %H:%M:%S')
                    summary['end_date'] = df['timestamp'].max().strftime('%Y-%m-%d %H:%M:%S')
                    
            except Exception as e:
                logger.warning(f"获取{market}/{symbol}/{timeframe}数据概要失败: {e}")
        
        return summary

def main():
    """主函数 - 原始数据层建设者"""
    parser = argparse.ArgumentParser(description='原始数据层建设者 - 股票数据获取系统')
    parser.add_argument('--market', choices=['us', 'cn'], help='目标市场')
    parser.add_argument('--timeframe', help='时间周期 (如: 1d, 1h, 15m)')
    parser.add_argument('--symbol', help='单个股票代码 (如: AAPL, 000001.SZ)')
    parser.add_argument('--max-stocks', type=int, help='最大处理股票数量')
    parser.add_argument('--workers', type=int, default=CONFIG['MAX_WORKERS'], help='并行线程数')
    parser.add_argument('--skip-validation', action='store_true', help='跳过数据验证')
    parser.add_argument('--quiet', action='store_true', help='静默模式')
    parser.add_argument('--info', action='store_true', help='显示数据信息概览')
    
    args = parser.parse_args()
    
    # 初始化数据管理器
    manager = RawDataManager(parallel_workers=args.workers)
    
    # 信息模式
    if args.info:
        info = manager.get_available_data_info()
        print("\n=== 原始数据层概览 ===")
        print(f"支持市场: {', '.join(info['markets'])}")
        print(f"总数据文件: {info['total_files']}个")
        for market in info['markets']:
            symbols = info['symbols_by_market'][market]
            timeframes = info['timeframes_by_market'][market]
            print(f"\n{market.upper()}市场:")
            print(f"  股票数量: {len(symbols)}个")
            print(f"  时间周期: {', '.join(timeframes)}")
        return
    
    # 验证参数（对于非info模式）
    if not args.info:
        if not args.market or not args.timeframe:
            print("错误: 非info模式需要指定 --market 和 --timeframe 参数")
            return
        
        if args.timeframe not in MARKET_CONFIG[args.market]['supported_timeframes']:
            print(f"错误: 市场{args.market}不支持时间周期{args.timeframe}")
            print(f"支持的时间周期: {', '.join(MARKET_CONFIG[args.market]['supported_timeframes'])}")
            return
    
    # 单个股票模式
    if args.symbol:
        if not args.quiet:
            print(f"单个股票模式: {args.market}/{args.symbol}/{args.timeframe}")
        
        symbol, status = manager.process_single_stock(args.market, args.symbol, args.timeframe)
        
        if not args.quiet:
            if status == "success":
                print(f"更新成功: {symbol}")
            elif status == "skip":
                print(f"数据已是最新: {symbol}")
            else:
                print(f"更新失败: {symbol}")
        return
    
    # 批量模式
    stock_list = manager.load_stock_list(args.market)
    if not stock_list:
        market_name = "美股" if args.market == 'us' else "A股"
        print(f"无法加载{market_name}股票列表，程序退出")
        return
    
    # 运行批量更新
    update_metrics = manager.run_batch_update(args.market, args.timeframe, stock_list, args.max_stocks, verbose=not args.quiet)
    
    # 运行完整性验证
    validation_metrics = DataQualityMetrics()
    if not args.skip_validation:
        validation_metrics = manager.run_integrity_validation(args.market, args.timeframe, verbose=not args.quiet)
    elif not args.quiet:
        print("\n跳过数据验证")
    
    # 生成汇总报告
    manager.generate_summary_report(args.market, args.timeframe, update_metrics, validation_metrics, verbose=not args.quiet)
    
    # 系统状态
    if not args.quiet:
        print("系统运行完成")

if __name__ == "__main__":
    main()