#!/usr/bin/env python3
"""
专业交易信号生成系统
严格基于04模型训练结果的实时预测系统
遵循量化ML建模最佳实践：简洁、可重现、高效
"""

import pandas as pd
import numpy as np
import joblib
import json
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional
import warnings

warnings.filterwarnings('ignore')

@dataclass
class TradingSignal:
    """交易信号 - 简化版本"""
    symbol: str
    signal_type: str  # BUY/SELL/HOLD
    confidence: float
    price: float
    target_price: float
    stop_loss: float
    risk_level: str
    reasoning: str
    timestamp: str

class ProfessionalSignalGenerator:
    """专业交易信号生成器
    
    设计原则:
    1. 严格使用04训练的模型和参数
    2. 可重现的预测结果
    3. 科学的信号筛选逻辑
    4. 最小化参数调优，专注模型应用
    """
    
    def __init__(self):
        self.model = None
        self.optimal_features = []
        self.strategy_params = {}
        
        # 加载04模块的训练成果
        self._load_model_artifacts()
        
    def _load_model_artifacts(self):
        """加载04模块的所有训练成果"""
        
        # 1. 加载训练好的模型
        model_path = Path("model/saved_models/xgboost_model.pkl")
        if not model_path.exists():
            raise FileNotFoundError("未找到训练模型，请先运行04_model_training_evaluation.py")
        
        self.model = joblib.load(model_path)
        print(f"模型加载: {model_path}")
        
        # 2. 加载最优特征配置
        features_path = Path("model/optimal_features.json")
        if not features_path.exists():
            raise FileNotFoundError("未找到特征配置，请先运行02_feature_engineering.py")
        
        with open(features_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            self.optimal_features = config['selected_features']
        print(f"特征加载: {len(self.optimal_features)}个最优特征")
        
        # 3. 加载04的策略参数（如果存在）
        self._load_strategy_params()
        
    def _load_strategy_params(self):
        """加载策略参数 - diff优化版本"""
        # diff优化：提升信号质量和风险控制
        self.strategy_params = {
            'probability_threshold': 0.52,     # 提高买入阈值(0.45→0.55)，平衡质量与机会
            'min_confidence': 0.45,            # 适当提升最低置信度
            'max_positions': 8,                # 更集中持仓(10→6)，平衡分散与管理
            'take_profit': 0.07,               # 更现实止盈目标(0.06→0.08)，平衡收益
            'stop_loss': 0.09,                 # 强化风险控制(0.08→0.10)，平衡控制
            'commission_rate': 0.0007,         # 更现实成本估算(0.0005→0.001)，平衡现实性
            'min_signal_strength': 0.025,      # 略提升信号强度要求
            'auto_optimize': True              # 启用自动阈值优化
        }
        print(f"策略参数加载完成: 买入阈值{self.strategy_params['probability_threshold']}, 最大持仓{self.strategy_params['max_positions']}, 止盈{self.strategy_params['take_profit']:.1%}, 止损{self.strategy_params['stop_loss']:.1%}")
    
    def load_latest_data(self) -> pd.DataFrame:
        """加载最新数据用于预测"""
        
        test_data_path = Path("model/data/test_data.parquet")
        if not test_data_path.exists():
            raise FileNotFoundError("未找到测试数据，请先运行02_feature_engineering.py")
        
        # 读取所有测试数据
        all_data = pd.read_parquet(test_data_path)
        
        # 获取每只股票的最新记录
        latest_data = all_data.groupby('symbol').tail(1).copy()
        
        print(f"数据加载: {len(latest_data)}只股票的最新数据 (日期: {latest_data['date'].max().strftime('%Y-%m-%d')})")
        
        return latest_data
    
    def predict_probabilities(self, data: pd.DataFrame) -> pd.DataFrame:
        """使用04训练的模型进行预测"""
        
        # 检查特征完整性
        missing_features = [f for f in self.optimal_features if f not in data.columns]
        if missing_features:
            raise ValueError(f"缺少特征: {missing_features}")
        
        # 提取特征
        X = data[self.optimal_features].copy()
        
        # 数据清洗（与04训练时保持一致）
        X = X.replace([np.inf, -np.inf], np.nan)
        X = X.fillna(0)  # 简单填充，保持与训练时一致
        
        # 模型预测
        probabilities = self.model.predict_proba(X)[:, 1]  # 获取正类概率
        
        # 构建结果DataFrame
        result = data[['symbol', 'close', 'date']].copy()
        result['probability'] = probabilities
        
        print(f"模型预测: 概率范围 {probabilities.min():.4f} - {probabilities.max():.4f}")
        
        return result
    
    def generate_signals(self, predictions: pd.DataFrame) -> List[TradingSignal]:
        """基于预测概率生成交易信号"""
        
        signals = []
        buy_threshold = self.strategy_params['probability_threshold']
        min_confidence = self.strategy_params['min_confidence']
        
        # 筛选高概率股票作为买入候选
        buy_candidates = predictions[
            predictions['probability'] >= buy_threshold
        ].copy()
        
        # 按概率排序，选择最好的股票
        buy_candidates = buy_candidates.sort_values('probability', ascending=False)
        
        # 限制买入数量
        max_positions = self.strategy_params['max_positions']
        buy_candidates = buy_candidates.head(max_positions)
        
        print(f"信号筛选: {len(buy_candidates)}个买入候选 (阈值: {buy_threshold:.3f})")
        
        # 生成买入信号
        for _, row in buy_candidates.iterrows():
            signal = self._create_buy_signal(row)
            if signal:
                signals.append(signal)
        
        # 可选：生成明确的卖出信号（低概率股票）
        sell_threshold = 0.25  # 25%以下概率考虑卖出
        sell_candidates = predictions[
            predictions['probability'] <= sell_threshold
        ]
        
        # 只对持仓股票生成卖出信号（这里简化处理）
        # 实际应用中需要检查当前持仓
        
        print(f"信号生成: {len(signals)}个有效信号")
        
        return signals
    
    def _create_buy_signal(self, prediction_row: pd.Series) -> Optional[TradingSignal]:
        """创建买入信号"""
        
        symbol = prediction_row['symbol']
        probability = prediction_row['probability']
        current_price = prediction_row['close']
        
        # 计算目标价和止损价
        take_profit = self.strategy_params['take_profit']
        stop_loss = self.strategy_params['stop_loss']
        
        target_price = current_price * (1 + take_profit)
        stop_loss_price = current_price * (1 - stop_loss)
        
        # 风险评估（简化）
        risk_level = "LOW" if probability >= 0.6 else "MEDIUM"
        
        # 生成推理说明
        reasoning = f"模型预测上涨概率{probability:.3f}"
        
        # 添加技术指标确认（如果可用）
        if hasattr(prediction_row, 'rsi') and prediction_row.get('rsi', 50) < 70:
            reasoning += ", RSI未超买"
        if hasattr(prediction_row, 'macd') and prediction_row.get('macd', 0) > 0:
            reasoning += ", MACD正向"
        
        return TradingSignal(
            symbol=symbol,
            signal_type="BUY",
            confidence=probability,
            price=current_price,
            target_price=target_price,
            stop_loss=stop_loss_price,
            risk_level=risk_level,
            reasoning=reasoning,
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    
    def save_signals(self, signals: List[TradingSignal]) -> Tuple[str, str]:
        """保存信号到文件"""
        
        # 创建目录
        signals_dir = Path("logs/trading_signals")
        signals_dir.mkdir(exist_ok=True, parents=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细信号
        signals_file = signals_dir / f"signals_{timestamp}.json"
        signals_data = [
            {
                'symbol': s.symbol,
                'signal_type': s.signal_type,
                'confidence': s.confidence,
                'price': s.price,
                'target_price': s.target_price,
                'stop_loss': s.stop_loss,
                'risk_level': s.risk_level,
                'reasoning': s.reasoning,
                'timestamp': s.timestamp
            }
            for s in signals
        ]
        
        with open(signals_file, 'w', encoding='utf-8') as f:
            json.dump(signals_data, f, indent=2, ensure_ascii=False)
        
        # 生成简要报告
        report_file = signals_dir / f"daily_report_{timestamp}.json"
        report = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'total_signals': len(signals),
            'buy_signals': len([s for s in signals if s.signal_type == 'BUY']),
            'avg_confidence': np.mean([s.confidence for s in signals]),
            'top_picks': [
                {
                    'symbol': s.symbol,
                    'confidence': round(s.confidence, 3),
                    'target_return': round((s.target_price - s.price) / s.price * 100, 1)
                }
                for s in signals[:5]
            ]
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"信号保存: {signals_file}")
        print(f"报告保存: {report_file}")
        
        return str(signals_file), str(report_file)
    
    def run_signal_generation(self) -> Dict:
        """运行完整的信号生成流程"""
        
        print("=" * 60)
        print("专业交易信号生成系统")
        print("=" * 60)
        print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # 1. 加载最新数据
            latest_data = self.load_latest_data()
            
            # 2. 模型预测
            predictions = self.predict_probabilities(latest_data)
            
            # 3. 生成信号
            signals = self.generate_signals(predictions)
            
            # 4. 保存结果
            signals_file, report_file = self.save_signals(signals)
            
            # 5. 显示摘要
            self._display_summary(signals)
            
            return {
                'success': True,
                'signals_count': len(signals),
                'signals_file': signals_file,
                'report_file': report_file
            }
            
        except Exception as e:
            print(f"信号生成失败: {e}")
            import traceback
            traceback.print_exc()
            return {'success': False, 'error': str(e)}
    
    def _display_summary(self, signals: List[TradingSignal]):
        """显示信号摘要"""
        
        if not signals:
            print("\n今日无交易信号")
            # 仍需保存空信号文件以保持06模块兼容性
            empty_signals_file, empty_report_file = self.save_signals([])
            return
        
        print(f"\n今日交易信号摘要")
        print("=" * 40)
        print(f"总信号数: {len(signals)}")
        print(f"买入信号: {len([s for s in signals if s.signal_type == 'BUY'])}")
        print(f"平均置信度: {np.mean([s.confidence for s in signals]):.1%}")
        
        print(f"\nTOP 5 推荐:")
        for i, signal in enumerate(signals[:5], 1):
            expected_return = (signal.target_price - signal.price) / signal.price * 100
            print(f"  {i}. {signal.symbol}: {signal.confidence:.1%} (目标收益: +{expected_return:.1f}%)")
        
        print(f"\n基于04模型训练结果的科学预测 (优化: 提升信号质量, 强化风险控制)")


def main():
    """主函数"""
    
    try:
        # 创建信号生成器
        generator = ProfessionalSignalGenerator()
        
        # 运行信号生成
        result = generator.run_signal_generation()
        
        if result['success']:
            print(f"\n信号生成成功!")
            print(f"   生成 {result['signals_count']} 个信号")
        else:
            print(f"\n信号生成失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"系统错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()