#!/usr/bin/env python3
"""
金融量化特征工程系统
==================
功能: 技术指标计算、特征工程、智能特征选择
特点: 高效批处理、内存优化、生产就绪
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import argparse
import warnings
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import gc

# 机器学习库
import xgboost as xgb
from sklearn.feature_selection import f_classif, mutual_info_classif
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score

warnings.filterwarnings('ignore')

# 配置常量
CONFIG = {
    'RANDOM_SEED': 42,
    'SAMPLE_SIZE': 40000,
    'AUC_THRESHOLD': 0.65,
    'TRAIN_END': '2023-12-31',
    'TEST_START': '2024-01-01',
    'BATCH_SIZE': 50,
    'MEMORY_LIMIT_MB': 4000,
    'MIN_DATA_POINTS': 200,
    'MIN_VOLUME': 1000000,
    'MIN_PRICE': 5.0,
    'MISSING_RATIO_THRESHOLD': 0.10,
    'OUTLIER_STD_MULTIPLE': 10,
    'LABEL_BALANCE_MIN': 0.01,
    'LABEL_BALANCE_MAX': 0.99,
}

np.random.seed(CONFIG['RANDOM_SEED'])
data_dir = Path("data")
data_dir.mkdir(exist_ok=True)

@dataclass
class FeatureQualityMetrics:
    """特征质量指标"""
    total_features: int = 0
    high_quality_features: int = 0
    low_variance_features: int = 0
    redundant_features: int = 0
    selected_features: int = 0
    validation_auc: float = 0.0
    missing_values: int = 0
    infinite_values: int = 0
    outlier_values: int = 0
    data_quality_score: float = 1.0
    
    @property
    def quality_rate(self) -> float:
        return self.high_quality_features / self.total_features * 100 if self.total_features > 0 else 0.0
    
    @property
    def feature_efficiency(self) -> float:
        return self.selected_features / self.total_features * 100 if self.total_features > 0 else 0.0

@dataclass
class DataStats:
    """数据统计信息"""
    symbol: str
    start_date: str
    end_date: str
    total_records: int
    missing_ratio: float
    avg_volume: float
    avg_price: float
    outlier_ratio: float = 0.0
    
    def __post_init__(self):
        self.data_quality = "good" if self.missing_ratio < CONFIG['MISSING_RATIO_THRESHOLD'] else "poor"

@dataclass
class BatchProcessingStats:
    """批处理统计"""
    total_files: int = 0
    processed_files: int = 0
    failed_files: int = 0
    
    @property
    def success_rate(self) -> float:
        return self.processed_files / self.total_files * 100 if self.total_files > 0 else 0.0

class TechnicalIndicator:
    """技术指标计算引擎"""
    
    @staticmethod
    def sma(data: pd.Series, window: int = 20) -> pd.Series:
        """简单移动平均"""
        return data.rolling(window=window).mean()
    
    @staticmethod
    def ema(data: pd.Series, window: int) -> pd.Series:
        """指数移动平均"""
        return data.ewm(span=window).mean()
    
    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """MACD指标"""
        ema_fast = TechnicalIndicator.ema(data, fast)
        ema_slow = TechnicalIndicator.ema(data, slow)
        macd = ema_fast - ema_slow
        macd_signal = TechnicalIndicator.ema(macd, signal)
        macd_histogram = macd - macd_signal
        return macd, macd_signal, macd_histogram
    
    @staticmethod
    def rsi(data: pd.Series, window: int = 14) -> pd.Series:
        """相对强弱指数"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    @staticmethod
    def bollinger_bands(data: pd.Series, window: int = 20, num_std: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """布林带"""
        sma = TechnicalIndicator.sma(data, window)
        std = data.rolling(window=window).std()
        upper = sma + (std * num_std)
        lower = sma - (std * num_std)
        return upper, sma, lower
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """平均真实范围"""
        high_low = high - low
        high_close = np.abs(high - close.shift())
        low_close = np.abs(low - close.shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        return true_range.rolling(window=window).mean()
    
    @staticmethod
    def momentum(data: pd.Series, period: int = 10) -> pd.Series:
        """动量指标"""
        return data - data.shift(period)
    
    @staticmethod
    def volatility(data: pd.Series, period: int = 20) -> pd.Series:
        """波动率指标"""
        return data.pct_change().rolling(window=period).std()
    
    @staticmethod
    def price_position(close: pd.Series, high: pd.Series, low: pd.Series, period: int = 14) -> pd.Series:
        """价格位置指标"""
        highest = high.rolling(window=period).max()
        lowest = low.rolling(window=period).min()
        return (close - lowest) / (highest - lowest)

def calculate_vwap(df: pd.DataFrame) -> float:
    """计算成交量加权平均价格"""
    return (df['close'] * df['volume']).sum() / df['volume'].sum() if df['volume'].sum() > 0 else 0

def calculate_comprehensive_features(df: pd.DataFrame) -> pd.DataFrame:
    """计算全套技术指标特征"""
    # 数据类型转换
    numeric_cols = ['open', 'high', 'low', 'close', 'volume', 'dividends', 'stock_splits']
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 基础价格特征（避免数据泄漏）
    df['ohlc_avg'] = (df['open'] + df['high'] + df['low'] + df['close']) / 4  # 保留原特征
    df['hl_avg'] = (df['high'] + df['low']) / 2  # 保留原特征
    df['price_change'] = df['close'].pct_change()  # 保留原特征
    df['volume_change'] = df['volume'].pct_change()  # 保留原特征
    df['price_range'] = df['high'] - df['low']  # 保留原特征
    
    # 时间安全的滞后特征
    df['ohlc_avg_lag1'] = ((df['open'].shift(1) + df['high'].shift(1) + df['low'].shift(1) + df['close'].shift(1)) / 4)
    df['hl_avg_lag1'] = (df['high'].shift(1) + df['low'].shift(1)) / 2
    df['price_change_lag1'] = df['close'].shift(1).pct_change()
    df['volume_change_lag1'] = df['volume'].shift(1).pct_change()
    df['price_range_lag1'] = df['high'].shift(1) - df['low'].shift(1)
    
    # 趋势指标（使用前一日数据避免穿越）
    for window in [5, 10, 20, 55, 233]:
        df[f'sma_{window}'] = TechnicalIndicator.sma(df['close'].shift(1), window)
    
    for window in [12, 26]:
        df[f'ema_{window}'] = TechnicalIndicator.ema(df['close'].shift(1), window)
    
    # MACD指标（使用前一日数据）
    macd, macd_signal, macd_histogram = TechnicalIndicator.macd(df['close'].shift(1))
    df['macd'], df['macd_signal'], df['macd_histogram'] = macd, macd_signal, macd_histogram
    
    # 动量指标（使用前一日数据）
    df['rsi'] = TechnicalIndicator.rsi(df['close'].shift(1))
    df['price_momentum'] = TechnicalIndicator.momentum(df['close'].shift(1), 10)
    
    # 布林带（使用前一日数据）
    bb_upper, bb_middle, bb_lower = TechnicalIndicator.bollinger_bands(df['close'].shift(1))
    df['bb_upper'], df['bb_middle'], df['bb_lower'] = bb_upper, bb_middle, bb_lower
    df['bb_position'] = (df['close'].shift(1) - bb_lower) / (bb_upper - bb_lower)
    
    # 波动率指标（使用前一日数据）
    df['atr'] = TechnicalIndicator.atr(df['high'].shift(1), df['low'].shift(1), df['close'].shift(1))
    df['volatility'] = TechnicalIndicator.volatility(df['close'].shift(1), 20)
    
    # 成交量指标（使用前一日数据）
    df['volume_sma'] = TechnicalIndicator.sma(df['volume'].shift(1), 20)
    df['trading_intensity'] = df['volume'] / df['volume_sma']  # 保留原特征
    df['volume_price_trend'] = (df['close'] - df['close'].shift(1)) * df['volume']  # 保留原特征
    
    # 时间安全的滞后特征
    df['trading_intensity_lag1'] = df['volume'].shift(1) / df['volume_sma'].shift(1)
    df['volume_price_trend_lag1'] = (df['close'].shift(1) - df['close'].shift(2)) * df['volume'].shift(1)
    
    # 均线差异特征（基于前一日数据的均线）
    df['sma_diff_20_55'] = df['sma_20'] - df['sma_55']
    df['ema_diff_12_26'] = df['ema_12'] - df['ema_26']
    
    # 价格相对位置（使用前一日数据）
    df['price_position'] = TechnicalIndicator.price_position(df['close'].shift(1), df['high'].shift(1), df['low'].shift(1), 14)
    
    # 高级支撑阻力特征（使用前一日数据）
    df['resistance_strength'] = df['high'].shift(1).rolling(20).max() / df['close'].shift(1) - 1
    df['support_strength'] = 1 - df['low'].shift(1).rolling(20).min() / df['close'].shift(1)
    
    # 价格与关键均线的支撑/压力特征(专业金融特征，使用前一日数据)
    df['price_to_sma55_pct'] = (df['close'].shift(1) - df['sma_55']) / df['sma_55']
    df['proximity_to_sma55'] = np.abs(df['price_to_sma55_pct'])
    
    # 均线支撑/压力强度(结合成交量，使用前一日数据)
    proximity_threshold = 0.02  # 2%阈值判断是否接近均线
    df['sma55_position'] = np.where(df['proximity_to_sma55'] <= proximity_threshold, 0,
                                   np.where(df['price_to_sma55_pct'] > 0, 1, -1))
    df['sma55_support_strength'] = df['proximity_to_sma55'] * df['trading_intensity_lag1']
    
    # 缺口特征
    df['gap_up'] = (df['open'] > df['close'].shift(1)).astype(int)
    df['gap_down'] = (df['open'] < df['close'].shift(1)).astype(int)
    df['gap_size'] = np.abs(df['open'] - df['close'].shift(1)) / df['close'].shift(1)
    
    # 多时间框架特征（使用前一日数据）
    for period in [5, 10, 20]:
        df[f'high_{period}d'] = df['high'].shift(1).rolling(period).max()
        df[f'low_{period}d'] = df['low'].shift(1).rolling(period).min()
        df[f'return_{period}d'] = df['close'].shift(1).pct_change(period)
    
    # 高级技术分析特征（使用滞后数据）
    df['price_breakthrough_strength'] = np.abs(df['price_change']) * df['trading_intensity']  # 保留原特征
    df['price_breakthrough_strength_lag1'] = np.abs(df['price_change_lag1']) * df['trading_intensity_lag1']
    
    # 均线多头/空头排列信号（基于前一日数据的均线）
    df['ma_bullish_signal'] = np.where(
        (df['sma_20'] > df['sma_55']), 1,
        np.where((df['sma_20'] < df['sma_55']), -1, 0)
    )
    
    # 价格与多条均线的综合位置（使用前一日数据）
    df['price_above_key_ma'] = np.where(
        (df['close'].shift(1) > df['sma_20']) & (df['close'].shift(1) > df['sma_55']), 1,
        np.where((df['close'].shift(1) < df['sma_20']) & (df['close'].shift(1) < df['sma_55']), -1, 0)
    )
    
    # 添加满后特征以替代当日数据（避免数据泄漏）
    df['close_lag1'] = df['close'].shift(1)  # 前一日收盘价
    df['volume_lag1'] = df['volume'].shift(1)  # 前一日成交量
    df['high_lag1'] = df['high'].shift(1)  # 前一日最高价
    df['low_lag1'] = df['low'].shift(1)  # 前一日最低价
    df['range_lag1'] = df['price_range'].shift(1)  # 前一日价格区间
    
    # 短期价格动量特征（使用满后数据）
    df['price_momentum_3d'] = (df['close_lag1'] / df['close'].shift(3) - 1)  # 3日动量
    df['price_momentum_5d'] = (df['close_lag1'] / df['close'].shift(5) - 1)  # 5日动量
    
    # 创建预测标签（未来收益）- 按股票分组计算10日后收益
    # 按symbol分组确保不会跨股票计算
    df['future_return'] = df.groupby('symbol')['close'].transform(
        lambda x: (x.shift(-10) - x) / x  # 10日后涨幅
    )
    # 目标：未来10天涨幅超过5%
    df['target'] = (df['future_return'] > 0.05).astype(int)
    
    return df

class DataQualityMonitor:
    """数据质量监控"""
    
    @staticmethod
    def validate_temporal_consistency(df: pd.DataFrame, feature_cols: List[str]) -> Dict[str, List[str]]:
        """验证特征的时间一致性，检测潜在数据泄漏"""
        risk_assessment = {
            'high_risk': [],  # 高风险：明确包含当日数据
            'medium_risk': [],  # 中风险：可能包含当日数据
            'low_risk': [],   # 低风险：使用滞后数据
            'safe': []        # 安全：明确的滞后特征
        }
        
        for col in feature_cols:
            col_lower = col.lower()
            
            # 高风险特征：直接使用当日价格/成交量数据
            if any(keyword in col_lower for keyword in ['price_change', 'volume_change', 'trading_intensity', 
                                                        'volume_price_trend', 'price_breakthrough']):
                if 'lag' not in col_lower:
                    risk_assessment['high_risk'].append(col)
                else:
                    risk_assessment['safe'].append(col)
            
            # 中风险特征：可能间接包含当日数据
            elif any(keyword in col_lower for keyword in ['close', 'open', 'high', 'low', 'volume']):
                if 'lag' not in col_lower and col_lower not in ['close_lag1', 'volume_lag1', 'high_lag1', 'low_lag1']:
                    risk_assessment['medium_risk'].append(col)
                else:
                    risk_assessment['safe'].append(col)
            
            # 低风险特征：技术指标（通常基于历史数据）
            elif any(keyword in col_lower for keyword in ['sma', 'ema', 'rsi', 'macd', 'bb_', 'atr']):
                risk_assessment['low_risk'].append(col)
            
            # 安全特征：明确标记为滞后的特征
            elif 'lag' in col_lower or any(suffix in col_lower for suffix in ['_lag1', '_5d', '_10d', '_20d']):
                risk_assessment['safe'].append(col)
            
            else:
                risk_assessment['low_risk'].append(col)
        
        return risk_assessment
    
    @staticmethod
    def check_data_quality(df: pd.DataFrame) -> FeatureQualityMetrics:
        """检查数据质量"""
        metrics = FeatureQualityMetrics()
        
        metrics.missing_values = df.isnull().sum().sum()
        metrics.infinite_values = np.isinf(df.select_dtypes(include=[np.number])).sum().sum()
        
        # 统计异常值
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        outliers = 0
        for col in numeric_cols:
            if len(df[col].dropna()) > 0:
                mean_val = df[col].mean()
                std_val = df[col].std()
                if std_val > 0:
                    outliers += ((np.abs(df[col] - mean_val) > CONFIG['OUTLIER_STD_MULTIPLE'] * std_val)).sum()
        metrics.outlier_values = outliers
        
        # 质量分数
        total_values = df.size
        if total_values > 0:
            quality_issues = metrics.missing_values + metrics.infinite_values + metrics.outlier_values
            metrics.data_quality_score = max(0, 1 - (quality_issues / total_values))
        
        return metrics
    
    @staticmethod
    def clean_data(df: pd.DataFrame) -> pd.DataFrame:
        """数据清洗"""
        # 移除无限值
        df = df.replace([np.inf, -np.inf], np.nan)
        
        # 前向填充缺失值
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        df[numeric_cols] = df[numeric_cols].fillna(method='ffill')
        
        # 移除仍有缺失值的行
        df = df.dropna()
        
        return df

class SmartStockSelector:
    """智能股票筛选器"""
    
    @staticmethod
    def evaluate_stock_quality(df: pd.DataFrame, symbol: str) -> Optional[DataStats]:
        """评估股票数据质量"""
        if len(df) < CONFIG['MIN_DATA_POINTS']:
            return None
        
        missing_ratio = df.isnull().sum().sum() / df.size
        if missing_ratio > CONFIG['MISSING_RATIO_THRESHOLD']:
            return None
        
        avg_volume = df['volume'].mean()
        avg_price = df['close'].mean()
        
        if avg_volume < CONFIG['MIN_VOLUME'] or avg_price < CONFIG['MIN_PRICE']:
            return None
        
        return DataStats(
            symbol=symbol,
            start_date=df['timestamp'].min().strftime('%Y-%m-%d'),
            end_date=df['timestamp'].max().strftime('%Y-%m-%d'),
            total_records=len(df),
            missing_ratio=missing_ratio,
            avg_volume=avg_volume,
            avg_price=avg_price
        )
    
    @staticmethod
    def select_quality_stocks(symbols: List[str], max_count: int = 100) -> List[str]:
        """选择高质量股票"""
        quality_stocks = []
        
        for symbol in symbols:
            data_file = data_dir / f"timeframe=1d/symbol={symbol}/data.parquet"
            if not data_file.exists():
                continue
            
            try:
                df = pd.read_parquet(data_file)
                if 'timestamp' not in df.columns:
                    continue
                
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                stats = SmartStockSelector.evaluate_stock_quality(df, symbol)
                
                if stats:
                    quality_stocks.append(symbol)
                    if len(quality_stocks) >= max_count:
                        break
                        
            except Exception:
                continue
        
        return quality_stocks

class IntelligentFeatureOptimizer:
    """智能特征优化器"""
    
    @staticmethod
    def select_features_xgboost(X: pd.DataFrame, y: pd.Series, top_k: int = 20) -> List[str]:
        """使用XGBoost进行特征选择"""
        try:
            model = xgb.XGBClassifier(
                n_estimators=100,
                max_depth=6,
                random_state=CONFIG['RANDOM_SEED'],
                eval_metric='logloss',
                n_jobs=1  # 单线程保证一致性
            )
            model.fit(X, y)
            
            feature_importance = pd.DataFrame({
                'feature': X.columns,
                'importance': model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            return feature_importance.head(top_k)['feature'].tolist()
        except Exception:
            return X.columns[:top_k].tolist()
    
    @staticmethod
    def select_features_mutual_info(X: pd.DataFrame, y: pd.Series, top_k: int = 20) -> List[str]:
        """使用互信息进行特征选择"""
        try:
            mi_scores = mutual_info_classif(X, y, random_state=CONFIG['RANDOM_SEED'])
            mi_df = pd.DataFrame({
                'feature': X.columns,
                'mi_score': mi_scores
            }).sort_values('mi_score', ascending=False)
            
            return mi_df.head(top_k)['feature'].tolist()
        except Exception:
            return X.columns[:top_k].tolist()
    
    @staticmethod
    def select_features_combined_temporal(X: pd.DataFrame, y: pd.Series, timestamps: pd.Series, top_k: int = 20) -> Tuple[List[str], float]:
        """基于时序的组合特征选择方法"""
        # 确保随机种子一致性
        np.random.seed(CONFIG['RANDOM_SEED'])
        xgb_features = IntelligentFeatureOptimizer.select_features_xgboost(X, y, top_k)
        
        np.random.seed(CONFIG['RANDOM_SEED'])  # 为互信息重新设置
        mi_features = IntelligentFeatureOptimizer.select_features_mutual_info(X, y, top_k)
        
        # 取交集和并集的平衡（保证顺序稳定）
        intersection = sorted(list(set(xgb_features) & set(mi_features)))
        union = sorted(list(set(xgb_features) | set(mi_features)))
        
        # 优先使用交集，不足时从并集补充（按字母顺序）
        selected_features = intersection.copy()
        for feature in union:
            if feature not in selected_features and len(selected_features) < top_k:
                selected_features.append(feature)
        
        # 时序验证特征质量（严格时间分割）
        if len(selected_features) > 0:
            X_selected = X[selected_features[:top_k]]
            
            # 简化的时序分割：按行顺序前80%作为训练，后20%作为验证
            split_idx = int(len(X_selected) * 0.8)
            
            X_train = X_selected.iloc[:split_idx]
            X_test = X_selected.iloc[split_idx:]
            y_train = y.iloc[:split_idx]
            y_test = y.iloc[split_idx:]
            
            try:
                # 确保模型训练的一致性
                model = xgb.XGBClassifier(
                    random_state=CONFIG['RANDOM_SEED'], 
                    eval_metric='logloss',
                    n_jobs=1  # 单线程保证一致性
                )
                model.fit(X_train, y_train)
                y_pred_proba = model.predict_proba(X_test)[:, 1]
                auc_score = roc_auc_score(y_test, y_pred_proba)
            except Exception:
                auc_score = 0.5
        else:
            auc_score = 0.5
        
        return selected_features[:top_k], auc_score
    
    @staticmethod
    def select_features_combined(X: pd.DataFrame, y: pd.Series, top_k: int = 20) -> Tuple[List[str], float]:
        """兼容性保留：组合特征选择方法"""
        # 使用时序方法进行特征选择
        dummy_timestamps = pd.Series(range(len(X)))
        return IntelligentFeatureOptimizer.select_features_combined_temporal(X, y, dummy_timestamps, top_k)

class BatchDataProcessor:
    """批量数据处理器"""
    
    def __init__(self):
        self.stats = BatchProcessingStats()
    
    def process_batch(self, symbols: List[str]) -> pd.DataFrame:
        """批量处理股票数据"""
        combined_data = []
        
        for symbol in symbols:
            try:
                data_file = data_dir / f"timeframe=1d/symbol={symbol}/data.parquet"
                if not data_file.exists():
                    self.stats.failed_files += 1
                    continue
                
                df = pd.read_parquet(data_file)
                if len(df) < CONFIG['MIN_DATA_POINTS']:
                    self.stats.failed_files += 1
                    continue
                
                # 确保timestamp列存在并转换
                if 'timestamp' not in df.columns:
                    self.stats.failed_files += 1
                    continue
                
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.sort_values('timestamp')
                
                # 计算特征
                df_features = calculate_comprehensive_features(df.copy())
                
                # 数据清洗
                df_clean = DataQualityMonitor.clean_data(df_features)
                
                if len(df_clean) > 50:  # 确保有足够数据
                    df_clean['symbol'] = symbol
                    combined_data.append(df_clean)
                    self.stats.processed_files += 1
                else:
                    self.stats.failed_files += 1
                    
            except Exception as e:
                print(f"处理 {symbol} 失败: {e}")
                self.stats.failed_files += 1
        
        if combined_data:
            result = pd.concat(combined_data, ignore_index=True)
            # 移除target列中的NaN
            result = result.dropna(subset=['target'])
            return result
        else:
            return pd.DataFrame()

class FeatureManager:
    """特征管理器"""
    
    def __init__(self, mode: str = 'research'):
        self.mode = mode
        self.processor = BatchDataProcessor()
        self.selected_features = []
        self.feature_performance = {}
        
        # 创建model/data目录
        self.model_data_dir = Path("model/data")
        self.model_data_dir.mkdir(parents=True, exist_ok=True)
    
    def run_research_mode(self, symbols: List[str]) -> Dict:
        """研究模式：特征工程和选择"""
        print(f" 研究模式启动，处理 {len(symbols)} 只股票")
        
        # 批量处理数据
        combined_df = self.processor.process_batch(symbols)
        
        if combined_df.empty:
            print(" 数据加载失败")
            return {}
        
        print(f" 批处理完成\n  成功率: {self.processor.stats.success_rate:.1f}% ({self.processor.stats.processed_files}/{self.processor.stats.processed_files + self.processor.stats.failed_files})")
        print(f"  总记录: {len(combined_df):,} 行")
        
        # 准备特征和标签
        # 排除数据泄漏的列：严格排除所有当日数据和衍生特征
        exclude_cols = ['timestamp', 'symbol', 'target', 'future_return', 'date',
                       'open', 'high', 'low', 'close', 'volume',  # 当日原始数据
                       'ohlc_avg', 'hl_avg', 'price_range',  # 当日衍生特征
                       'price_change', 'volume_change', 'trading_intensity',  # 包含当日的特征
                       'volume_price_trend', 'price_breakthrough_strength',  # 当日计算特征
                       'ohlc_avg', 'hl_avg', 'price_range']  # 其他当日特征
        
        # 增加数据泄漏检测
        potential_leakage_features = []
        for col in combined_df.columns:
            if col not in exclude_cols and not col.startswith('Unnamed'):
                # 检测可能的泄漏特征（不包含lag标识的当日特征）
                if any(keyword in col.lower() for keyword in ['close', 'open', 'high', 'low', 'volume']) and 'lag' not in col:
                    if col not in ['close_lag1', 'volume_lag1', 'high_lag1', 'low_lag1']:
                        potential_leakage_features.append(col)
        
        if potential_leakage_features:
            print(f"  警告: 检测到潜在泄漏特征: {potential_leakage_features[:5]}{'...' if len(potential_leakage_features) > 5 else ''}")
        
        feature_cols = [col for col in combined_df.columns 
                       if col not in exclude_cols and not col.startswith('Unnamed')]
        
        X = combined_df[feature_cols].select_dtypes(include=[np.number])
        y = combined_df['target']
        
        # 数据质量检查
        quality_metrics = DataQualityMonitor.check_data_quality(X)
        print(f"  数据质量分数: {quality_metrics.data_quality_score:.3f}")
        
        # 时间一致性验证
        risk_assessment = DataQualityMonitor.validate_temporal_consistency(combined_df, feature_cols)
        if risk_assessment['high_risk']:
            print(f"  ⚠️ 高风险特征 ({len(risk_assessment['high_risk'])}): {risk_assessment['high_risk'][:3]}{'...' if len(risk_assessment['high_risk']) > 3 else ''}")
        if risk_assessment['medium_risk']:
            print(f"  ⚠️ 中风险特征 ({len(risk_assessment['medium_risk'])}): {risk_assessment['medium_risk'][:3]}{'...' if len(risk_assessment['medium_risk']) > 3 else ''}")
        print(f"  ✅ 安全特征: {len(risk_assessment['safe'])} 个, 低风险特征: {len(risk_assessment['low_risk'])} 个")
        
        # 采样以提高性能（使用固定种子保证可重现）
        if len(X) > CONFIG['SAMPLE_SIZE']:
            # 使用固定种子确保每次采样结果一致
            np.random.seed(CONFIG['RANDOM_SEED'])  # 重新设置种子
            sample_indices = np.random.choice(len(X), CONFIG['SAMPLE_SIZE'], replace=False)
            X = X.iloc[sample_indices]
            y = y.iloc[sample_indices]
            print(f"  采样数据: {len(X):,} 行")
        
        # 时序特征选择（使用采样后的时间戳）
        print(f"\n 智能特征优化（时序方法）")
        if len(X) > CONFIG['SAMPLE_SIZE']:
            # 使用采样后的时间戳
            timestamps_sampled = pd.Series(range(len(X)))
        else:
            timestamps_sampled = combined_df['timestamp'] if 'timestamp' in combined_df.columns else pd.Series(range(len(X)))
        selected_features, auc_score = IntelligentFeatureOptimizer.select_features_combined_temporal(X, y, timestamps_sampled, top_k=20)
        
        print(f"  原始特征: {len(feature_cols)} 个")
        print(f"  优选特征: {len(selected_features)} 个")
        print(f"  验证AUC: {auc_score:.4f}")
        
        # 保存结果
        result = {
            'selected_features': selected_features,
            'feature_performance': {'validation_auc': auc_score},
            'data_stats': {
                'total_records': len(combined_df),
                'total_features': len(feature_cols),
                'selected_features': len(selected_features),
                'data_quality_score': quality_metrics.data_quality_score
            }
        }
        
        # 保存特征配置
        self._save_feature_config(result)
        
        # 关键: 保存训练和测试数据供03模块使用
        self._save_train_test_data(combined_df)
        
        return result
    
    def run_production_mode(self, symbols: List[str]) -> Dict:
        """生产模式：使用预选特征"""
        print(f" 生产模式启动，处理 {len(symbols)} 只股票")
        
        # 加载特征配置
        feature_config = self._load_feature_config()
        if not feature_config:
            print(" 未找到特征配置，切换到研究模式")
            return self.run_research_mode(symbols)
        
        self.selected_features = feature_config.get('selected_features', [])
        
        # 批量处理数据
        combined_df = self.processor.process_batch(symbols)
        
        if combined_df.empty:
            print(" 数据加载失败")
            return {}
        
        print(f" 批处理完成")
        print(f"  成功率: {self.processor.stats.success_rate:.1f}% ({self.processor.stats.processed_files}/{self.processor.stats.processed_files + self.processor.stats.failed_files})")
        print(f"  总记录: {len(combined_df):,} 行")
        print(f"  使用特征: {len(self.selected_features)} 个")
        
        # 生成最终数据集
        output_file = data_dir / "processed_features.parquet"
        available_features = [f for f in self.selected_features if f in combined_df.columns]
        
        if available_features:
            # 确保包含时间和标签列
            base_cols = ['timestamp', 'symbol', 'target']
            feature_data = combined_df[base_cols + available_features]
            feature_data.to_parquet(output_file, index=False)
            print(f"  特征数据已保存: {output_file}")
        
        return {
            'output_file': str(output_file),
            'selected_features': available_features,
            'data_stats': {
                'total_records': len(combined_df),
                'selected_features': len(available_features)
            }
        }
    
    def _save_feature_config(self, config: Dict):
        """保存特征配置到model目录"""
        model_config_file = Path("model") / "optimal_features.json"
        model_config_file.parent.mkdir(exist_ok=True)
        with open(model_config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"  特征配置已保存: {model_config_file}")
    
    def _load_feature_config(self) -> Dict:
        """从 model 目录加载特征配置"""
        model_config_file = Path("model") / "optimal_features.json"
        if model_config_file.exists():
            with open(model_config_file, 'r') as f:
                return json.load(f)
        return {}
    
    def _save_train_test_data(self, df: pd.DataFrame):
        """保存训练和测试数据供03模块使用"""
        try:
            # 确保有timestamp列
            if 'timestamp' in df.columns:
                df['date'] = pd.to_datetime(df['timestamp'])
            else:
                print("警告: 缺少timestamp列，无法进行时间分割")
                return
            
            # 时间分割: 2015-2023为训练集，2024年至今为测试集
            train_end = '2023-12-31'
            test_start = '2024-01-01'
            
            train_data = df[df['date'] <= train_end].copy()
            test_data = df[df['date'] >= test_start].copy()
            
            # 保存到model/data目录
            train_path = self.model_data_dir / "train_data.parquet"
            test_path = self.model_data_dir / "test_data.parquet"
            
            train_data.to_parquet(train_path, index=False)
            test_data.to_parquet(test_path, index=False)
            
            print(f"  训练数据已保存: {train_path} ({len(train_data):,} 条)")
            print(f"  测试数据已保存: {test_path} ({len(test_data):,} 条)")
            
        except Exception as e:
            print(f"  警告: 数据保存失败 - {e}")

def get_all_available_symbols() -> List[str]:
    """获取所有可用股票代码"""
    symbols = []
    timeframe_dir = data_dir / "timeframe=1d"
    
    if timeframe_dir.exists():
        for symbol_dir in timeframe_dir.iterdir():
            if symbol_dir.is_dir() and symbol_dir.name.startswith('symbol='):
                symbol = symbol_dir.name.replace('symbol=', '')
                data_file = symbol_dir / "data.parquet"
                if data_file.exists():
                    symbols.append(symbol)
    
    return sorted(symbols)

def get_stock_symbols(mode: str = 'auto', count: int = 100) -> List[str]:
    """获取股票代码列表"""
    all_symbols = get_all_available_symbols()
    
    if mode == 'all':
        return all_symbols
    elif mode == 'sample':
        return all_symbols[:count] if len(all_symbols) >= count else all_symbols
    else:  # auto mode
        return SmartStockSelector.select_quality_stocks(all_symbols, count)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='金融量化特征工程系统')
    
    # 支持位置参数和关键字参数
    parser.add_argument('mode', nargs='?', choices=['research', 'production'], default='research',
                       help='运行模式')
    parser.add_argument('--mode', dest='mode_flag', choices=['research', 'production'],
                       help='运行模式(关键字参数)')
    
    # 股票选择参数(兼容多种参数格式)
    parser.add_argument('--stock-mode', choices=['all', 'sample', 'auto'], default='auto',
                       help='股票选择策略')
    parser.add_argument('--stocks', choices=['all', 'sample', 'auto'], 
                       help='股票选择策略(简化版)')
    parser.add_argument('--count', type=int, default=100,
                       help='处理股票数量')
    
    # 特征优化参数
    parser.add_argument('--optimize-features', action='store_true',
                       help='启用智能特征选择优化')
    
    args = parser.parse_args()
    
    # 参数处理和兼容性
    mode = args.mode_flag if args.mode_flag else args.mode
    stock_mode = args.stocks if args.stocks else args.stock_mode
    
    print(f"专业特征工程系统启动 [{mode.upper()}模式]")
    
    # 获取股票列表
    symbols = get_stock_symbols(stock_mode, args.count)
    if not symbols:
        print("未找到可用股票数据")
        return
    
    print(f"股票范围: {len(symbols)} 只")
    
    # 创建特征管理器并运行
    start_time = datetime.now()
    manager = FeatureManager(mode=mode)
    
    try:
        if mode == 'research':
            result = manager.run_research_mode(symbols)
            
            # 结果统计(精简输出)
            if result:
                stats = result.get('data_stats', {})
                if stats and args.optimize_features:
                    print(f"特征优化: {stats.get('total_features', 0)} -> {stats.get('selected_features', 0)} 个")
                    if 'feature_performance' in result:
                        auc = result['feature_performance'].get('validation_auc', 0)
                        print(f"AUC: {auc:.4f}")
        else:
            result = manager.run_production_mode(symbols)
            if result and 'output_file' in result:
                print(f"特征数据已生成: {result['output_file']}")
    
    except Exception as e:
        print(f"处理错误: {e}")
        return
    
    duration = (datetime.now() - start_time).total_seconds()
    # pipeline脚本会添加自己的SUCCESS消息，这里不重复输出
    # print(f"[SUCCESS] 02 特征工程 - {mode}模式 完成 (用时: {duration:.0f}s)")
    pass  # 静默结束，让pipeline脚本处理输出
    
    # 内存清理
    gc.collect()

if __name__ == "__main__":
    main()