#!/usr/bin/env python3
"""
专业投资组合管理系统
符合量化行业标准的风险管理、仓位优化、投资组合构建系统
基于现代投资组合理论和量化风险管理最佳实践
"""

import pandas as pd
import numpy as np
import json
import warnings
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
from scipy.optimize import minimize
from sklearn.covariance import LedoitWolf

warnings.filterwarnings('ignore')

@dataclass
class Position:
    """当前持仓"""
    symbol: str
    shares: int
    cost_basis: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    weight: float
    entry_date: datetime
    
@dataclass 
class TradeOrder:
    """交易订单"""
    symbol: str
    action: str  # BUY, SELL, REBALANCE
    quantity: int
    target_weight: float
    current_weight: float
    price: float
    order_value: float
    reasoning: str
    risk_score: float
    priority: int

@dataclass
class RiskMetrics:
    """风险指标"""
    portfolio_var_95: float  # 95% VaR
    portfolio_cvar_95: float  # 95% CVaR  
    portfolio_volatility: float
    max_drawdown: float
    sharpe_ratio: float
    beta: float
    concentration_risk: float
    
class PositionSizer:
    """仓位大小计算器"""
    
    def __init__(self, max_position_size: float = 0.15, max_sector_exposure: float = 0.3):
        self.max_position_size = max_position_size
        self.max_sector_exposure = max_sector_exposure
        
    def kelly_criterion(self, win_rate: float, avg_win: float, avg_loss: float) -> float:
        """Kelly公式计算最优仓位比例"""
        if avg_loss == 0:
            return 0
        
        expected_return = win_rate * avg_win + (1 - win_rate) * (-avg_loss)
        if expected_return <= 0:
            return 0
            
        kelly_fraction = expected_return / (avg_win * avg_loss)
        return min(kelly_fraction * 0.25, self.max_position_size)  # Kelly的1/4作为保守估计
    
    def confidence_based_sizing(self, confidence: float, volatility: float) -> float:
        """基于置信度和波动率的仓位计算"""
        base_size = self.max_position_size * confidence
        volatility_adjustment = max(0.3, 1 - volatility * 2)  # 高波动降低仓位
        return min(base_size * volatility_adjustment, self.max_position_size)
    
    def risk_parity_weight(self, asset_volatility: float, portfolio_volatility: float) -> float:
        """风险平价权重计算"""
        if portfolio_volatility == 0:
            return 1.0 / 10  # 默认等权重
        return (1.0 / asset_volatility) / (10.0 / portfolio_volatility)

class RiskManager:
    """风险管理器"""
    
    def __init__(self, var_confidence: float = 0.05, max_portfolio_var: float = 0.03):
        self.var_confidence = var_confidence
        self.max_portfolio_var = max_portfolio_var
        
    def calculate_var(self, returns: pd.Series, confidence: float = 0.05) -> float:
        """计算风险价值VaR"""
        if len(returns) < 30:
            return np.std(returns) * 2.33  # 99%置信度近似
        return -np.percentile(returns, confidence * 100)
    
    def calculate_cvar(self, returns: pd.Series, confidence: float = 0.05) -> float:
        """计算条件风险价值CVaR"""
        var = self.calculate_var(returns, confidence)
        tail_losses = returns[returns <= -var]
        return -tail_losses.mean() if len(tail_losses) > 0 else var
    
    def calculate_portfolio_risk(self, weights: np.ndarray, cov_matrix: np.ndarray) -> float:
        """计算投资组合风险"""
        return np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
    
    def diversification_ratio(self, weights: np.ndarray, asset_vols: np.ndarray, 
                            portfolio_vol: float) -> float:
        """计算分散化比率"""
        weighted_avg_vol = np.dot(weights, asset_vols)
        return weighted_avg_vol / portfolio_vol if portfolio_vol > 0 else 1.0

class PortfolioOptimizer:
    """投资组合优化器"""
    
    def __init__(self, risk_aversion: float = 3.0):
        self.risk_aversion = risk_aversion
        
    def mean_variance_optimization(self, expected_returns: np.ndarray, 
                                 cov_matrix: np.ndarray, 
                                 constraints: Dict = None) -> np.ndarray:
        """均值-方差优化"""
        n_assets = len(expected_returns)
        
        def objective(weights):
            portfolio_return = np.dot(weights, expected_returns)
            portfolio_risk = np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
            return -(portfolio_return - 0.5 * self.risk_aversion * portfolio_risk ** 2)
        
        # 约束条件
        constraints_list = [
            {'type': 'eq', 'fun': lambda w: np.sum(w) - 1.0},  # 权重和为1
        ]
        
        if constraints:
            for symbol, max_weight in constraints.items():
                idx = constraints.get(symbol, 0)
                constraints_list.append(
                    {'type': 'ineq', 'fun': lambda w, i=idx: max_weight - w[i]}
                )
        
        # 边界条件：每个资产权重在0-15%之间
        bounds = [(0, 0.15) for _ in range(n_assets)]
        
        # 初始权重
        x0 = np.array([1.0/n_assets] * n_assets)
        
        result = minimize(objective, x0, method='SLSQP', bounds=bounds, 
                        constraints=constraints_list)
        
        return result.x if result.success else x0
    
    def risk_parity_optimization(self, cov_matrix: np.ndarray) -> np.ndarray:
        """风险平价优化"""
        n_assets = cov_matrix.shape[0]
        
        def risk_budget_objective(weights):
            portfolio_vol = np.sqrt(np.dot(weights, np.dot(cov_matrix, weights)))
            marginal_contrib = np.dot(cov_matrix, weights) / portfolio_vol
            contrib = weights * marginal_contrib
            target_contrib = portfolio_vol / n_assets
            return np.sum((contrib - target_contrib) ** 2)
        
        constraints = [{'type': 'eq', 'fun': lambda w: np.sum(w) - 1.0}]
        bounds = [(0.01, 0.15) for _ in range(n_assets)]
        x0 = np.array([1.0/n_assets] * n_assets)
        
        result = minimize(risk_budget_objective, x0, method='SLSQP', 
                        bounds=bounds, constraints=constraints)
        
        return result.x if result.success else x0

class PortfolioManager:
    """专业投资组合管理系统"""
    
    def __init__(self, initial_capital: float = 100000, max_positions: int = 10):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.max_positions = max_positions
        
        # 核心组件
        self.position_sizer = PositionSizer()
        self.risk_manager = RiskManager()
        self.optimizer = PortfolioOptimizer()
        
        # 数据路径
        self.signals_dir = Path("logs/trading_signals")
        self.orders_dir = Path("logs/portfolio_orders")
        self.portfolio_dir = Path("logs/portfolio_state")
        
        # 创建目录
        self.orders_dir.mkdir(exist_ok=True, parents=True)
        self.portfolio_dir.mkdir(exist_ok=True, parents=True)
        
        # 当前持仓
        self.positions: Dict[str, Position] = {}
        
        # 风险控制参数
        self.max_sector_concentration = 0.3
        self.max_single_position = 0.15
        self.min_liquidity_threshold = 100000  # 最小日均成交额
        
    def load_latest_signals(self) -> pd.DataFrame:
        """加载最新交易信号"""
        print("=== 加载最新交易信号 ===")
        
        signal_files = list(self.signals_dir.glob("signals_*.json"))
        if not signal_files:
            print("未找到信号文件")
            return pd.DataFrame()
        
        # 查找今日生成的信号文件
        today_str = datetime.now().strftime("%Y%m%d")
        today_files = [f for f in signal_files if today_str in f.name]
        
        if not today_files:
            print(f"未找到今日({today_str})的信号文件")
            return pd.DataFrame()
        
        # 使用最新的今日信号文件
        latest_file = max(today_files, key=lambda p: p.stat().st_mtime)
        print(f"读取今日信号文件: {latest_file.name}")
        
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                signals_data = json.load(f)
            
            # 检查文件是否为空或无有效信号
            if not signals_data:
                print("信号文件为空，今日无交易信号")
                return pd.DataFrame()
                
            return pd.DataFrame(signals_data)
        except Exception as e:
            print(f"加载信号失败: {e}")
            return pd.DataFrame()
    
    def load_current_positions(self) -> Dict[str, Position]:
        """加载当前持仓状态"""
        position_files = list(self.portfolio_dir.glob("positions_*.json"))
        if not position_files:
            print("无历史持仓记录，初始化空投资组合")
            return {}
        
        latest_file = max(position_files, key=lambda p: p.stat().st_mtime)
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                positions_data = json.load(f)
            
            positions = {}
            for symbol, data in positions_data.items():
                positions[symbol] = Position(
                    symbol=data['symbol'],
                    shares=data['shares'],
                    cost_basis=data['cost_basis'],
                    current_price=data['current_price'],
                    market_value=data['market_value'],
                    unrealized_pnl=data['unrealized_pnl'],
                    weight=data['weight'],
                    entry_date=datetime.fromisoformat(data['entry_date'])
                )
            
            print(f"加载 {len(positions)} 个持仓")
            return positions
            
        except Exception as e:
            print(f"加载持仓失败: {e}")
            return {}
    
    def calculate_portfolio_metrics(self, signals: pd.DataFrame) -> RiskMetrics:
        """计算投资组合风险指标"""
        if self.positions:
            # 使用历史数据计算风险指标
            symbols = list(self.positions.keys())
            weights = np.array([pos.weight for pos in self.positions.values()])
            
            # 模拟风险计算（实际应用中需要历史收益率数据）
            portfolio_vol = 0.15  # 假设年化波动率15%
            var_95 = portfolio_vol * 1.65  # 95% VaR近似
            cvar_95 = var_95 * 1.3
            
            return RiskMetrics(
                portfolio_var_95=var_95,
                portfolio_cvar_95=cvar_95,
                portfolio_volatility=portfolio_vol,
                max_drawdown=0.10,  # 假设10%最大回撤
                sharpe_ratio=1.2,   # 假设夏普比率1.2
                beta=1.0,
                concentration_risk=max(weights) if len(weights) > 0 else 0
            )
        else:
            return RiskMetrics(0, 0, 0, 0, 0, 0, 0)
    
    def generate_trade_orders(self, signals: pd.DataFrame) -> List[TradeOrder]:
        """生成交易订单"""
        print("\n=== 生成专业交易订单 ===")
        
        if signals.empty:
            print("无信号数据")
            return []
        
        orders = []
        
        # 1. 处理买入信号
        buy_signals = signals[signals['signal_type'] == 'BUY'].copy()
        if not buy_signals.empty:
            buy_signals = buy_signals.sort_values('confidence', ascending=False)
            
            # 当前投资组合价值
            portfolio_value = self.current_capital
            if self.positions:
                portfolio_value = sum(pos.market_value for pos in self.positions.values())
            
            for _, signal in buy_signals.head(self.max_positions).iterrows():
                symbol = signal['symbol']
                confidence = signal['confidence']
                price = signal['price']
                
                # 跳过已持有的股票
                if symbol in self.positions:
                    continue
                
                # 计算仓位大小
                target_weight = self.position_sizer.confidence_based_sizing(
                    confidence, 0.02  # 假设2%日波动率
                )
                
                order_value = portfolio_value * target_weight
                quantity = int(order_value / price)
                
                if quantity > 0 and order_value >= 1000:  # 最小订单金额
                    order = TradeOrder(
                        symbol=symbol,
                        action='BUY',
                        quantity=quantity,
                        target_weight=target_weight,
                        current_weight=0.0,
                        price=price,
                        order_value=order_value,
                        reasoning=f"ML信号置信度{confidence:.3f}, 风险调整仓位{target_weight:.1%}",
                        risk_score=1 - confidence,
                        priority=1
                    )
                    orders.append(order)
        
        # 2. 处理卖出信号（现有持仓）
        sell_signals = signals[signals['signal_type'] == 'SELL'].copy()
        if not sell_signals.empty and self.positions:
            for _, signal in sell_signals.iterrows():
                symbol = signal['symbol']
                if symbol in self.positions:
                    position = self.positions[symbol]
                    order = TradeOrder(
                        symbol=symbol,
                        action='SELL',
                        quantity=position.shares,
                        target_weight=0.0,
                        current_weight=position.weight,
                        price=signal['price'],
                        order_value=position.market_value,
                        reasoning=f"ML卖出信号置信度{1-signal['confidence']:.3f}",
                        risk_score=signal['confidence'],
                        priority=0  # 卖出优先级高
                    )
                    orders.append(order)
        
        # 3. 风险管理检查
        orders = self._apply_risk_filters(orders)
        
        # 4. 按优先级和风险评分排序
        orders.sort(key=lambda x: (x.priority, x.risk_score))
        
        print(f"生成 {len(orders)} 个交易订单")
        return orders
    
    def _apply_risk_filters(self, orders: List[TradeOrder]) -> List[TradeOrder]:
        """应用风险过滤器"""
        filtered_orders = []
        
        # 当前权重分布
        current_weights = {}
        if self.positions:
            total_value = sum(pos.market_value for pos in self.positions.values())
            for symbol, pos in self.positions.items():
                current_weights[symbol] = pos.market_value / total_value
        
        # 检查每个订单
        for order in orders:
            # 1. 单一持仓限制
            if order.action == 'BUY' and order.target_weight > self.max_single_position:
                print(f"跳过 {order.symbol}: 超过单一持仓限制({order.target_weight:.1%} > {self.max_single_position:.1%})")
                continue
            
            # 2. 流动性检查（简化版）
            if order.order_value < 1000:
                continue
            
            # 3. 风险评分检查
            if order.risk_score > 0.6 and order.action == 'BUY':
                print(f"跳过 {order.symbol}: 风险评分过高({order.risk_score:.3f})")
                continue
                
            filtered_orders.append(order)
        
        return filtered_orders
    
    def execute_portfolio_rebalancing(self, orders: List[TradeOrder]) -> Dict:
        """执行投资组合再平衡"""
        print("\n=== 执行投资组合再平衡 ===")
        
        if not orders:
            print("无订单需要执行")
            return {
                'executed_orders': 0, 
                'buy_value': 0,
                'sell_value': 0,
                'remaining_cash': self.current_capital,
                'portfolio_value': self._calculate_portfolio_value()
            }
        
        executed_orders = []
        total_buy_value = 0
        total_sell_value = 0
        
        # 执行订单
        for order in orders:
            if order.action == 'SELL':
                # 卖出现有持仓
                if order.symbol in self.positions:
                    position = self.positions[order.symbol]
                    sell_value = order.quantity * order.price
                    total_sell_value += sell_value
                    
                    print(f"卖出 {order.symbol}: {order.quantity}股 @ ${order.price:.2f}")
                    print(f"  理由: {order.reasoning}")
                    
                    # 更新现金
                    self.current_capital += sell_value * (1 - 0.001)  # 扣除交易成本
                    
                    # 移除持仓
                    del self.positions[order.symbol]
                    executed_orders.append(order)
            
            elif order.action == 'BUY':
                # 买入新仓位
                buy_cost = order.quantity * order.price * (1 + 0.001)  # 包含交易成本
                
                if self.current_capital >= buy_cost:
                    print(f"买入 {order.symbol}: {order.quantity}股 @ ${order.price:.2f}")
                    print(f"  目标权重: {order.target_weight:.1%}")
                    print(f"  理由: {order.reasoning}")
                    
                    # 创建新持仓
                    position = Position(
                        symbol=order.symbol,
                        shares=order.quantity,
                        cost_basis=order.price,
                        current_price=order.price,
                        market_value=order.quantity * order.price,
                        unrealized_pnl=0,
                        weight=order.target_weight,
                        entry_date=datetime.now()
                    )
                    
                    self.positions[order.symbol] = position
                    self.current_capital -= buy_cost
                    total_buy_value += buy_cost
                    executed_orders.append(order)
                else:
                    print(f"跳过 {order.symbol}: 资金不足")
        
        # 重新计算权重
        self._update_position_weights()
        
        result = {
            'executed_orders': len(executed_orders),
            'buy_value': total_buy_value,
            'sell_value': total_sell_value,
            'remaining_cash': self.current_capital,
            'portfolio_value': self._calculate_portfolio_value()
        }
        
        print(f"\n执行摘要:")
        print(f"  执行订单: {result['executed_orders']} 个")
        print(f"  买入金额: ${result['buy_value']:,.2f}")
        print(f"  卖出金额: ${result['sell_value']:,.2f}")
        print(f"  剩余现金: ${result['remaining_cash']:,.2f}")
        print(f"  投资组合价值: ${result['portfolio_value']:,.2f}")
        
        return result
    
    def _update_position_weights(self):
        """更新持仓权重"""
        if not self.positions:
            return
            
        total_value = self._calculate_portfolio_value()
        for position in self.positions.values():
            position.weight = position.market_value / total_value
    
    def _calculate_portfolio_value(self) -> float:
        """计算投资组合总价值"""
        return self.current_capital + sum(pos.market_value for pos in self.positions.values())
    
    def save_portfolio_state(self):
        """保存投资组合状态"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存持仓状态
        if self.positions:
            positions_data = {}
            for symbol, position in self.positions.items():
                positions_data[symbol] = asdict(position)
                positions_data[symbol]['entry_date'] = position.entry_date.isoformat()
            
            positions_file = self.portfolio_dir / f"positions_{timestamp}.json"
            with open(positions_file, 'w', encoding='utf-8') as f:
                json.dump(positions_data, f, indent=2, ensure_ascii=False)
            
            print(f"投资组合状态已保存: {positions_file}")
    
    def save_trade_orders(self, orders: List[TradeOrder]):
        """保存交易订单"""
        if not orders:
            return
            
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        orders_file = self.orders_dir / f"orders_{timestamp}.json"
        
        orders_data = [asdict(order) for order in orders]
        with open(orders_file, 'w', encoding='utf-8') as f:
            json.dump(orders_data, f, indent=2, ensure_ascii=False)
        
        print(f"交易订单已保存: {orders_file}")
    
    def generate_portfolio_report(self) -> Dict:
        """生成投资组合报告"""
        if not self.positions:
            return {'error': '当前无持仓'}
        
        # 基本统计
        total_value = self._calculate_portfolio_value()
        positions_count = len(self.positions)
        
        # 持仓分析
        positions_summary = []
        for symbol, pos in self.positions.items():
            positions_summary.append({
                'symbol': symbol,
                'shares': pos.shares,
                'cost_basis': pos.cost_basis,
                'current_price': pos.current_price,
                'market_value': pos.market_value,
                'weight': pos.weight,
                'unrealized_pnl': pos.unrealized_pnl,
                'unrealized_return': pos.unrealized_pnl / (pos.shares * pos.cost_basis) if pos.shares > 0 else 0,
                'days_held': (datetime.now() - pos.entry_date).days
            })
        
        # 风险指标（简化）
        weights = [pos.weight for pos in self.positions.values()]
        concentration_risk = max(weights) if weights else 0
        
        report = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'portfolio_summary': {
                'total_value': total_value,
                'cash_balance': self.current_capital,
                'invested_amount': total_value - self.current_capital,
                'positions_count': positions_count,
                'concentration_risk': concentration_risk
            },
            'positions': positions_summary,
            'risk_metrics': {
                'max_single_position': max(weights) if weights else 0,
                'cash_ratio': self.current_capital / total_value,
                'diversification_score': 1 - concentration_risk
            }
        }
        
        return report
    
    def run_full_portfolio_management(self):
        """运行完整投资组合管理流程"""
        print("=== 专业投资组合管理系统 ===")
        print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # 1. 加载当前持仓
            self.positions = self.load_current_positions()
            
            # 2. 加载最新信号
            signals = self.load_latest_signals()
            if signals.empty:
                print("无交易信号，仅生成投资组合报告")
                report = self.generate_portfolio_report()
                self._save_portfolio_report(report)
                return report
            
            # 3. 计算风险指标
            risk_metrics = self.calculate_portfolio_metrics(signals)
            
            # 4. 生成交易订单
            orders = self.generate_trade_orders(signals)
            
            # 5. 执行交易
            execution_result = self.execute_portfolio_rebalancing(orders)
            
            # 6. 保存状态
            self.save_portfolio_state()
            self.save_trade_orders(orders)
            
            # 7. 生成报告
            report = self.generate_portfolio_report()
            report['execution_result'] = execution_result
            report['risk_metrics'] = asdict(risk_metrics)
            
            self._save_portfolio_report(report)
            
            # 8. 显示摘要
            self._display_portfolio_summary(report)
            
            return report
            
        except Exception as e:
            print(f"投资组合管理失败: {e}")
            import traceback
            traceback.print_exc()
            return {'error': str(e)}
    
    def _save_portfolio_report(self, report: Dict):
        """保存投资组合报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = self.portfolio_dir / f"portfolio_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"投资组合报告已保存: {report_file}")
    
    def _display_portfolio_summary(self, report: Dict):
        """显示投资组合摘要"""
        if 'error' in report:
            print(f"报告生成失败: {report['error']}")
            return
            
        print("\n" + "="*60)
        print("投资组合管理摘要")
        print("="*60)
        
        summary = report['portfolio_summary']
        print(f"投资组合总价值: ${summary['total_value']:,.2f}")
        print(f"现金余额: ${summary['cash_balance']:,.2f}")
        print(f"已投资金额: ${summary['invested_amount']:,.2f}")
        print(f"持仓数量: {summary['positions_count']} 个")
        print(f"集中度风险: {summary['concentration_risk']:.1%}")
        
        if 'execution_result' in report:
            exec_result = report['execution_result']
            print(f"\n交易执行:")
            print(f"  执行订单: {exec_result['executed_orders']} 个")
            print(f"  买入金额: ${exec_result['buy_value']:,.2f}")
            print(f"  卖出金额: ${exec_result['sell_value']:,.2f}")
        
        if report.get('positions'):
            print(f"\nTop 5 持仓:")
            positions = sorted(report['positions'], 
                             key=lambda x: x['weight'], reverse=True)
            for i, pos in enumerate(positions[:5], 1):
                print(f"  {i}. {pos['symbol']}: {pos['weight']:.1%} "
                      f"(${pos['market_value']:,.0f}, "
                      f"收益{pos['unrealized_return']:.1%})")
        
        print(f"\n基于专业风险管理的投资组合优化完成")

def main():
    """主函数"""
    import sys
    
    # 创建投资组合管理器
    portfolio_manager = PortfolioManager(
        initial_capital=100000,
        max_positions=10
    )
    
    # 运行完整流程
    result = portfolio_manager.run_full_portfolio_management()
    
    if 'error' not in result:
        print("\n投资组合管理完成！")
        print("可基于生成的订单进行实际交易")
    else:
        print(f"\n投资组合管理失败: {result['error']}")

if __name__ == "__main__":
    main()