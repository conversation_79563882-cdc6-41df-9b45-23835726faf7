#!/usr/bin/env python3
"""
Test script to validate Tushare token from .env file
"""

import os
from dotenv import load_dotenv
import tushare as ts


def test_tushare_token():
    """Test if the Tushare token in .env is valid"""
    # Load environment variables
    load_dotenv()
    
    # Get token from environment
    token = os.getenv('TUSHARE_TOKEN')
    
    if not token:
        print("❌ TUSHARE_TOKEN not found in .env file")
        return False
    
    print(f"🔑 Testing token: {token[:10]}...")
    print(f"📏 Token length: {len(token)} characters")
    
    # Check token format (should be 32 characters hexadecimal)
    if len(token) != 32:
        print(f"⚠️  Warning: Token length is {len(token)}, expected 32 characters")
    
    try:
        # Test 1: Set token and check if it's accepted
        ts.set_token(token)
        print("✅ Token format accepted by tushare")
        
        # Test 2: Initialize pro API
        pro = ts.pro_api()
        print("✅ Pro API initialized successfully")
        
        # Test 3: Try a very simple API call with timeout
        print("🔄 Testing API call...")
        df = pro.query('stock_basic', exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,list_date', limit=3)
        
        if df is not None and len(df) > 0:
            print("✅ Token is valid and API call successful!")
            print(f"📊 Successfully retrieved {len(df)} stock records")
            print("Sample data:")
            print(df.to_string(index=False))
            return True
        else:
            print("❌ API call returned empty data")
            return False
            
    except Exception as e:
        error_msg = str(e)
        print(f"❌ Token validation failed with error: {error_msg}")
        
        # Provide specific error diagnosis
        if "Connection" in error_msg or "timeout" in error_msg.lower():
            print("💡 This appears to be a network connectivity issue")
        elif "token" in error_msg.lower() or "auth" in error_msg.lower():
            print("💡 This appears to be a token authentication issue")
        elif "Remote end closed" in error_msg:
            print("💡 Server connection issue - may be temporary")
        
        return False


if __name__ == "__main__":
    success = test_tushare_token()
    exit(0 if success else 1)