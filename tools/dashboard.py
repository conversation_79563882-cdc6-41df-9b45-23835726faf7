#!/usr/bin/env python3
"""
量化交易系统Dashboard
统一查看所有输出信息的简洁入口
"""

import json
import pandas as pd
from pathlib import Path
from datetime import datetime
import subprocess
import sys
from typing import Dict, List, Optional
import warnings

warnings.filterwarnings('ignore')

class TradingDashboard:
    """量化交易系统Dashboard"""
    
    def __init__(self):
        self.logs_dir = Path("logs")
    
    def load_latest_results(self) -> Dict:
        """加载所有最新结果"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'model_results': self._load_model_results(),
            'feature_results': self._load_feature_results(),
            'trading_signals': self._load_trading_signals(),
            'automation_status': self._load_automation_status()
        }
        return results
    
    def _load_model_results(self) -> Optional[Dict]:
        """加载模型训练结果"""
        model_dir = self.logs_dir / "model_results"
        if not model_dir.exists():
            return None
        
        result_files = sorted(model_dir.glob("trading_results_*.json"))
        if not result_files:
            return None
        
        try:
            with open(result_files[-1], 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载模型结果失败: {e}")
            return None
    
    def _load_feature_results(self) -> Optional[Dict]:
        """加载特征分析结果"""
        feature_dir = self.logs_dir / "feature_results"
        if not feature_dir.exists():
            return None
        
        analysis_files = sorted(feature_dir.glob("analysis_results_*.json"))
        if not analysis_files:
            return None
        
        try:
            with open(analysis_files[-1], 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载特征分析结果失败: {e}")
            return None
    
    def _load_trading_signals(self) -> Optional[Dict]:
        """加载交易信号"""
        signals_dir = self.logs_dir / "trading_signals"
        if not signals_dir.exists():
            return None
        
        daily_reports = sorted(signals_dir.glob("daily_report_*.json"))
        if not daily_reports:
            return None
        
        try:
            with open(daily_reports[-1], 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载交易信号失败: {e}")
            return None
    
    def _load_automation_status(self) -> Optional[Dict]:
        """加载自动化状态"""
        automation_dir = self.logs_dir / "automation"
        if not automation_dir.exists():
            return None
        
        automation_files = sorted(automation_dir.glob("automation_report_*.json"))
        if not automation_files:
            return None
        
        try:
            with open(automation_files[-1], 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载自动化状态失败: {e}")
            return None
    
    def calculate_health_score(self, results: Dict) -> tuple[int, List[str]]:
        """计算系统健康度"""
        score = 100
        issues = []
        
        # 检查模型性能
        model_results = results.get('model_results')
        if model_results:
            training_results = model_results.get('training_results', {})
            auc = training_results.get('val_auc', 0)
            if auc < 0.6:
                score -= 30
                issues.append(f"模型AUC过低({auc:.3f})")
            elif auc < 0.7:
                score -= 15
                issues.append(f"模型AUC一般({auc:.3f})")
            
            # 检查策略表现
            strategy_results = model_results.get('strategy_results', {})
            total_return = strategy_results.get('total_return', 0)
            if total_return < -0.2:
                score -= 25
                issues.append(f"策略收益过低({total_return:.1%})")
            elif total_return < 0:
                score -= 10
                issues.append(f"策略收益为负({total_return:.1%})")
        else:
            score -= 40
            issues.append("缺少模型结果")
        
        # 检查交易信号
        signals = results.get('trading_signals')
        if signals:
            market_analysis = signals.get('market_analysis', {})
            avg_confidence = market_analysis.get('average_confidence', 0)
            if avg_confidence < 0.6:
                score -= 15
                issues.append(f"信号置信度偏低({avg_confidence:.1%})")
        else:
            score -= 20
            issues.append("缺少交易信号")
        
        return max(0, score), issues
    
    def generate_trading_recommendation(self, results: Dict) -> Dict:
        """生成交易建议"""
        signals = results.get('trading_signals')
        model_results = results.get('model_results')
        
        if not signals:
            return {
                'action': 'HOLD',
                'reason': '无可用交易信号',
                'confidence': 0.0,
                'opportunities': []
            }
        
        market_analysis = signals.get('market_analysis', {})
        buy_signals = market_analysis.get('buy_signals', 0)
        sell_signals = market_analysis.get('sell_signals', 0)
        avg_confidence = market_analysis.get('average_confidence', 0)
        
        # 基于信号生成建议
        if buy_signals > 0:
            action = 'BUY'
            reason = f'发现{buy_signals}个买入信号'
        elif sell_signals > 0:
            action = 'SELL/HOLD'
            reason = f'发现{sell_signals}个卖出信号，建议谨慎'
        else:
            action = 'HOLD'
            reason = '市场信号不明确'
        
        # 基于模型表现调整置信度
        confidence = avg_confidence
        if model_results:
            strategy_results = model_results.get('strategy_results', {})
            if strategy_results.get('total_return', 0) < -0.1:
                confidence *= 0.7  # 降低置信度
        
        return {
            'action': action,
            'reason': reason,
            'confidence': confidence,
            'opportunities': signals.get('top_opportunities', [])[:3]
        }
    
    def display_dashboard(self):
        """显示Dashboard"""
        print("="*80)
        print(f"📊 量化交易系统Dashboard - {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        print("="*80)
        
        # 加载所有结果
        results = self.load_latest_results()
        
        # 系统健康度
        health_score, issues = self.calculate_health_score(results)
        print(f"\n🏥 系统健康度: {health_score}分", end="")
        if health_score >= 85:
            print(" ✅ 优秀")
        elif health_score >= 70:
            print(" 🟡 良好")
        elif health_score >= 50:
            print(" 🟠 一般")
        else:
            print(" 🔴 需要改进")
        
        if issues:
            print("   问题清单:")
            for issue in issues:
                print(f"     • {issue}")
        
        # 模型性能
        model_results = results.get('model_results')
        if model_results:
            training_results = model_results.get('training_results', {})
            strategy_results = model_results.get('strategy_results', {})
            
            print(f"\n🤖 模型性能:")
            print(f"   📊 验证集AUC: {training_results.get('val_auc', 0):.3f}")
            print(f"   📈 准确率: {training_results.get('val_accuracy', 0):.3f}")
            
            print(f"\n💹 策略表现:")
            print(f"   💰 总收益率: {strategy_results.get('total_return', 0):.2%}")
            print(f"   📉 最大回撤: {strategy_results.get('max_drawdown', 0):.2%}")
            print(f"   ⚡ 夏普比率: {strategy_results.get('sharpe_ratio', 0):.3f}")
            
            # Top特征
            top_features = model_results.get('top_features', [])[:5]
            if top_features:
                print(f"\n🔍 Top 5 重要特征:")
                for i, feat in enumerate(top_features, 1):
                    print(f"   {i}. {feat['feature']}: {feat['importance']:.3f}")
        
        # 交易信号
        signals = results.get('trading_signals')
        if signals:
            market_analysis = signals.get('market_analysis', {})
            print(f"\n📊 当前市场信号:")
            print(f"   📈 买入信号: {market_analysis.get('buy_signals', 0)}")
            print(f"   📉 卖出信号: {market_analysis.get('sell_signals', 0)}")
            print(f"   🎯 平均置信度: {market_analysis.get('average_confidence', 0):.1%}")
            
            risk_dist = market_analysis.get('risk_distribution', {})
            print(f"   ⚠️  风险分布: 低{risk_dist.get('LOW', 0)} | 中{risk_dist.get('MEDIUM', 0)} | 高{risk_dist.get('HIGH', 0)}")
        
        # 交易建议
        recommendation = self.generate_trading_recommendation(results)
        print(f"\n🎯 交易建议:")
        print(f"   📋 行动: {recommendation['action']}")
        print(f"   💭 理由: {recommendation['reason']}")
        print(f"   🎲 置信度: {recommendation['confidence']:.1%}")
        
        # 顶级机会
        opportunities = recommendation['opportunities']
        if opportunities:
            print(f"\n🔥 顶级机会:")
            for i, opp in enumerate(opportunities, 1):
                print(f"   {i}. {opp['symbol']}: {opp['signal_type']} (置信度:{opp['confidence']})")
        
        # 系统状态
        automation = results.get('automation_status')
        if automation:
            auto_summary = automation.get('automation_summary', {})
            print(f"\n⚙️  自动化状态: {auto_summary.get('status', '未知')}")
            if auto_summary.get('date'):
                print(f"   📅 最后运行: {auto_summary['date']}")
        
        print("\n" + "="*80)
        print(f"📁 详细结果查看: logs/ 目录")
        print("="*80)
    
    def run_full_update(self):
        """运行完整更新"""
        print("🚀 运行完整系统更新...")
        
        try:
            result = subprocess.run(
                [sys.executable, "daily_trading_automation.py", "--now"],
                capture_output=True,
                text=True,
                timeout=1800
            )
            
            if result.returncode == 0:
                print("✅ 自动化更新完成")
                self.display_dashboard()
            else:
                print(f"⚠️ 更新过程有问题: {result.stderr}")
                self.display_dashboard()
        except subprocess.TimeoutExpired:
            print("⚠️ 更新超时，显示当前状态")
            self.display_dashboard()
        except Exception as e:
            print(f"⚠️ 更新异常: {e}")
            self.display_dashboard()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='量化交易系统Dashboard')
    parser.add_argument('--update', action='store_true', help='运行完整更新后显示Dashboard')
    
    args = parser.parse_args()
    
    dashboard = TradingDashboard()
    
    if args.update:
        dashboard.run_full_update()
    else:
        dashboard.display_dashboard()

if __name__ == "__main__":
    main()