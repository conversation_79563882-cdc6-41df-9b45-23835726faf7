#!/usr/bin/env python3
"""
交易日志分析工具 (Trade Analyzer)
读取回测引擎生成的交易日志，并计算详细的策略表现指标。
"""

import pandas as pd
import numpy as np
from pathlib import Path
import json
import sys

class TradeAnalyzer:
    def __init__(self, trades_log_path: Path):
        if not trades_log_path.exists():
            raise FileNotFoundError(f"交易日志文件不存在: {trades_log_path}")
        self.log_path = trades_log_path
        self.trades = self.load_trades()

    def load_trades(self) -> pd.DataFrame:
        """加载并预处理交易数据"""
        df = pd.read_json(self.log_path)
        df['date'] = pd.to_datetime(df['date'])
        return df

    def analyze(self):
        """执行完整的交易分析"""
        if self.trades.empty:
            print("交易日志为空，无法分析。")
            return

        sells = self.trades[self.trades['action'] == 'SELL'].copy()
        buys = self.trades[self.trades['action'] == 'BUY'].copy()

        total_trades = len(sells)
        if total_trades == 0:
            print("没有完整的买卖交易，无法分析。")
            return

        # --- 核心指标计算 ---
        win_rate = (sells['return'] > 0).mean()
        
        wins = sells[sells['return'] > 0]
        losses = sells[sells['return'] <= 0]
        
        avg_win_return = wins['return'].mean()
        avg_loss_return = losses['return'].mean()
        
        # 盈亏比 (Payoff Ratio)
        payoff_ratio = abs(avg_win_return / avg_loss_return) if avg_loss_return != 0 else np.inf

        # 利润因子 (Profit Factor)
        profit_factor = wins['return'].sum() / abs(losses['return'].sum()) if abs(losses['return'].sum()) > 0 else np.inf
        
        # 期望收益
        expectancy = (win_rate * avg_win_return) + ((1 - win_rate) * avg_loss_return)

        # --- 持仓周期分析 ---
        # 创建一个唯一的交易ID，以便正确匹配买入和卖出
        sells['trade_id'] = sells.groupby('symbol').cumcount()
        buys['trade_id'] = buys.groupby('symbol').cumcount()
        
        sells_with_buy_info = pd.merge(
            sells, 
            buys[['symbol', 'date', 'trade_id']], 
            on=['symbol', 'trade_id'], 
            suffixes=('_sell', '_buy')
        )
        
        sells_with_buy_info['holding_days'] = (sells_with_buy_info['date_sell'] - sells_with_buy_info['date_buy']).dt.days
        
        avg_holding_days = sells_with_buy_info['holding_days'].mean()
        avg_win_holding_days = sells_with_buy_info[sells_with_buy_info['return'] > 0]['holding_days'].mean()
        avg_loss_holding_days = sells_with_buy_info[sells_with_buy_info['return'] <= 0]['holding_days'].mean()

        # --- 打印报告 ---
        self.print_report({
            "总平仓交易数": total_trades,
            "胜率 (Win Rate)": f"{win_rate:.2%}",
            "平均盈利收益率": f"{avg_win_return:.2%}",
            "平均亏损收益率": f"{avg_loss_return:.2%}",
            "盈亏比 (Payoff Ratio)": f"{payoff_ratio:.2f} : 1",
            "利润因子 (Profit Factor)": f"{profit_factor:.2f}",
            "单笔交易期望收益 (Expectancy)": f"{expectancy:.3%}",
            "平均持仓天数": f"{avg_holding_days:.1f} 天",
            "盈利交易平均持仓天数": f"{avg_win_holding_days:.1f} 天",
            "亏损交易平均持仓天数": f"{avg_loss_holding_days:.1f} 天",
        })

    def print_report(self, metrics: dict):
        print("\n" + "="*50)
        print(" " * 15 + "交易行为深度分析")
        print("="*50)
        for key, value in metrics.items():
            print(f"{key:<30}: {value}")
        print("="*50)
        print("\n解读:")
        print("  - 胜率: 盈利交易占总交易的比例。")
        print("  - 盈亏比: 平均每笔盈利是平均每笔亏损的多少倍。 >1.5为佳。")
        print("  - 利润因子: 总盈利除以总亏损。 >2.0为佳。")
        print("  - 期望收益: 每进行一次交易，期望能获得的平均收益率。")
        print("="*50)


def main():
    log_dir = Path("logs/model_results")
    
    # 自动查找最新的交易日志文件
    if len(sys.argv) > 1:
        log_file = Path(sys.argv[1])
    else:
        trade_logs = list(log_dir.glob("backtest_trades_*.json"))
        if not trade_logs:
            print(f"错误: 在 {log_dir} 目录下未找到交易日志文件。")
            print("请先运行研究流程: ./run_pipeline.sh research")
            return
        log_file = max(trade_logs, key=lambda p: p.stat().st_mtime)

    print(f"正在分析交易日志: {log_file.name}")
    analyzer = TradeAnalyzer(log_file)
    analyzer.analyze()

if __name__ == "__main__":
    main()