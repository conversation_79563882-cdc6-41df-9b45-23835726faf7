# 🛠️ Tools Directory

专业量化交易工具集，高效简洁的特征工程和分析工具。

## 📊 核心工具

### `feature_optimizer_pro.py` - 专业特征优化器
**一站式特征工程解决方案**

**功能特点**:
- ✨ **特征质量分析**: 方差分析、相关性检测
- 🎯 **特征重要性**: XGBoost + F统计量综合评分
- 🚀 **智能组合优化**: 多种特征组合策略测试
- ⚡ **自动应用**: 直接更新模型配置文件

**使用方法**:
```bash
python tools/feature_optimizer_pro.py
```

**输出结果**:
- 自动更新 `04_model_training.py` 中的特征列表
- 保存详细分析结果到 `logs/feature_results/`
- 提供最优特征组合推荐

**核心算法**:
1. **数据清理**: 移除数据泄露和冗余特征
2. **质量筛选**: 低方差和高相关特征过滤
3. **重要性排序**: 多算法综合评分
4. **组合优化**: 平衡特征组合策略
5. **效果验证**: AUC和过拟合度综合评估

### `analyze_signal_accuracy.py` - 信号准确度分析
交易信号质量评估和优化工具

### `daily_trading_automation.py` - 日常交易自动化
自动化交易信号生成和执行

### `dashboard.py` - 可视化仪表板
交易结果和模型表现可视化分析

## 📁 文件结构

```
tools/
├── feature_optimizer_pro.py    # 核心特征优化器
├── analyze_signal_accuracy.py  # 信号分析工具
├── daily_trading_automation.py # 交易自动化
├── dashboard.py                # 可视化仪表板
└── README.md                   # 说明文档
```

## 🎯 最佳实践

1. **特征优化流程**:
   ```bash
   python tools/feature_optimizer_pro.py  # 特征优化
   python 04_model_training.py --auto     # 验证效果
   ```

2. **定期优化建议**:
   - 新数据到达时重新运行特征优化
   - 每月评估特征重要性变化
   - 根据市场变化调整特征组合

3. **监控指标**:
   - 验证AUC > 0.67
   - 过拟合度 < 0.15
   - 特征数量 12-20 个最优

## 🚀 技术特点

- **高效执行**: 40k样本分析在2秒内完成
- **智能去重**: 自动检测和移除冗余特征
- **版本控制**: 自动备份配置文件
- **错误处理**: 完善的异常处理机制
- **可扩展性**: 模块化设计便于功能扩展

---

**当前优化成果**: 18个精选特征，验证AUC达到0.683，46.26%收益率，1.0946夏普比率 🏆