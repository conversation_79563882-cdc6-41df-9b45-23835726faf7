import pandas as pd
import numpy as np
import os
import networkx as nx
import matplotlib.pyplot as plt
import community as community_louvain
from scipy.spatial import ConvexHull
import matplotlib.patches as patches

# 从旧notebook中提取的字典和颜色方案
NAME_DICT = {
    '600000': '浦发银行', '600016': '民生银行', '600028': '中国石化', '600030': '中信证券', '600036': '招商银行',
    '600048': '保利地产', '600050': '中国联通', '600104': '上汽集团', '600109': '国金证券', '600111': '北方稀土',
    '600340': '华夏幸福', '600489': '中金黄金', '600518': '康美药业', '600519': '贵州茅台', '600547': '山东黄金',
    '600585': '海螺水泥', '600606': '绿地控股', '600837': '海通证券', '600887': '伊利股份', '600958': '东方证券',
    '600999': '招商证券', '601006': '大秦铁路', '601088': '中国神华', '601166': '兴业银行', '601169': '北京银行',
    '601211': '国泰君安', '601288': '农业银行', '601318': '中国平安', '601328': '交通银行', '601336': '新华保险',
    '601390': '中国中铁', '601398': '工商银行', '601601': '中国太保', '601628': '中国人寿', '601668': '中国建筑',
    '601688': '华泰证券', '601766': '中国中车', '601788': '光大证券', '601800': '中国交建', '601818': '光大银行',
    '601857': '中国石油', '601881': '中国银河', '601901': '方正证券', '601988': '中国银行', '601989': '中国重工',
    '601998': '中信银行', '603993': '洛阳钼业'
}

# 尽量模仿png2.png的配色
COLOR_MAP = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#00ffff', '#ff00ff'] # Red, Green, Blue, Yellow, Cyan, Magenta

def replicate_correlation_graph(csv_path, output_image_path, corr_threshold=0.5):
    # 1. 数据加载和格式转换
    long_df = pd.read_csv(csv_path, encoding='gbk')
    df_pivoted = long_df.pivot(index='TradingDay', columns='SecuCode', values='涨跌幅')
    df_pivoted.columns = df_pivoted.columns.astype(str)
    
    tickers = [ticker for ticker in NAME_DICT.keys() if ticker in df_pivoted.columns]
    df = df_pivoted[tickers]

    # 2. 计算皮尔逊相关性
    corr_matrix = df.corr()

    # 3. 基于相关性阈值构建网络
    G = nx.Graph()
    for i in range(len(corr_matrix.columns)):
        for j in range(i + 1, len(corr_matrix.columns)):
            if abs(corr_matrix.iloc[i, j]) > corr_threshold:
                G.add_edge(corr_matrix.columns[i], corr_matrix.columns[j], weight=corr_matrix.iloc[i, j])

    # 使用中文标签
    G = nx.relabel_nodes(G, NAME_DICT)
    print(f"Graph created with {G.number_of_nodes()} nodes and {G.number_of_edges()} edges.")

    # 4. Louvain社区发现
    partition = community_louvain.best_partition(G, weight='weight')

    # 5. 可视化
    plt.rcParams['font.sans-serif'] = ['PingFang SC', 'Arial Unicode MS', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    fig, ax = plt.subplots(figsize=(16, 16))
    pos = nx.spring_layout(G, k=1.5, iterations=100, seed=42)

    for community_id in set(partition.values()):
        nodes_in_community = [node for node, cid in partition.items() if cid == community_id]
        if len(nodes_in_community) > 2:
            points = np.array([pos[node] for node in nodes_in_community])
            hull = ConvexHull(points)
            polygon = patches.Polygon(points[hull.vertices, :], closed=True, 
                                      facecolor=COLOR_MAP[community_id % len(COLOR_MAP)], 
                                      alpha=0.25)
            ax.add_patch(polygon)

    node_colors = [COLOR_MAP[partition[node] % len(COLOR_MAP)] for node in G.nodes()]
    edge_colors = ['red' if G.edges[u, v]['weight'] < 0 else 'black' for u, v in G.edges()]

    nx.draw_networkx_nodes(G, pos, node_color=node_colors, node_size=1000, ax=ax)
    nx.draw_networkx_edges(G, pos, edge_color=edge_colors, alpha=0.7, width=0.8, ax=ax)
    nx.draw_networkx_labels(G, pos, font_size=12, font_color='darkblue', ax=ax)

    ax.set_title("股票社交网络 (Pearson Correlation & Louvain)", fontsize=20)
    ax.set_aspect('equal', adjustable='box')
    plt.tight_layout()
    plt.savefig(output_image_path)
    print(f"\nReplicated network visualization saved to '{output_image_path}'")

if __name__ == '__main__':
    script_dir = os.path.dirname(os.path.abspath(__file__))
    csv_file_path = os.path.join(script_dir, '50stocks.csv')
    output_img_path = os.path.join(script_dir, 'correlation_replication.png')

    # 修正：传入 output_img_path 参数
    replicate_correlation_graph(csv_file_path, output_img_path)