# 股票社区发现算法探索项目

## 1. 项目概述

本目录包含了用于分析股票间相互关系、并自动发现其中社区（或簇）的系列算法实现。项目通过对历史脚本的重构和提炼，最终形成了三种不同层次、不同维度的分析方法，并分别应用于A股和美股市场。

所有脚本均被设计为独立、可直接运行的模块，旨在为量化投资研究提供关于市场结构的深刻洞察。

## 2. 文件结构与核心脚本

本目录的核心资产是5个算法脚本和它们对应的4个可视化结果图。

#### 核心脚本:
- `cluster_kmeans_us.py`: **(方法一)** 对美股进行PCA降维和K-Means聚类。
- `network_pearson_us.py`: **(方法二)** 对美股进行皮尔逊相关性网络分析与社区发现。
- `network_glasso_us.py`: **(方法三)** 对美股进行Graphical Lasso网络分析与社区发现。
- `network_pearson_cn.py`: **(方法二)** 对A股进行皮尔逊相关性网络分析与社区发现。
- `network_glasso_cn.py`: **(方法三)** 对A股进行Graphical Lasso网络分析与社区发现。

#### 数据文件:
- `50stocks.csv`: 用于A股分析的本地数据文件。美股数据由脚本通过`yfinance`实时获取。

#### 结果文件:
- `*.png`: 各网络分析脚本生成的可视化图表。

## 3. 算法详解

我们共探索并实现了三种分析方法，代表了从经典机器学习到前沿金融网络分析的演进。

### 方法一：几何聚类 (PCA + K-Means)

- **科学原理**:
  此方法将每只股票的每日收益率时间序列视为一个高维空间中的数据点。由于维度过高（一年约250个交易日，即250维）会产生“维度灾难”且包含大量噪声，我们首先采用**主成分分析（PCA）**进行降维，它能将原始特征浓缩成少数几个能解释大部分数据方差的核心主成分。最后，在降维后的空间中，应用经典的**K-Means算法**，通过计算欧氏距离，将空间位置上最相近的点归为一簇。

- **关键依赖包**:
  - `scikit-learn`: 提供`PCA`, `KMeans`, `StandardScaler`等核心机器学习工具。
  - `yfinance`: 用于实时获取美股数据。

- **评价**: 是一种非常快速、经典的无监督聚类方法，但它基于纯粹的几何距离，可能无法捕捉到金融市场中复杂的非线性关系。

### 方法二：相关性网络 (Pearson Correlation + Louvain)

- **科学原理**:
  此方法将股票市场看作一个网络。节点是股票，而节点间的“关系”由**皮尔逊相关系数**定义，它衡量了两只股票收益率序列的线性相关强度。在计算出相关系数矩阵后，设定一个阈值（如0.5），只保留高于此阈值的强关系作为网络中的“边”。网络构建完成后，采用**Louvain算法**进行社区发现。Louvain是一种高效的模块度优化算法，它通过迭代计算，寻找一种网络划分方式，使得社区内部的连接尽可能紧密，而社区之间的连接尽可能稀疏。

- **关键依赖包**:
  - `pandas`: 用于高效计算相关系数矩阵 (`.corr()`)。
  - `networkx`: 用于创建、操作和分析复杂的网络结构。
  - `python-louvain`: 提供核心的`Louvain`社区发现算法。
  - `scipy` & `matplotlib`: 用于最终的可视化，特别是通过`ConvexHull`计算并绘制社区背景“气泡”。

- **评价**: 非常直观，能清晰地识别出具有强联动效应的行业板块（如银行股、券商股的高度相关性）。但其缺点在于，简单相关性无法区分直接关系和间接关系（例如，两家公司都与市场本身高度相关，导致它们之间也呈现高相关性）。

### 方法三：部分相关性网络 (Graphical Lasso + Louvain)

- **科学原理**:
  这是第二种方法的进阶版，也是最为深刻和稳健的方法。它旨在过滤掉由市场整体等宏观因素带来的“伪关系”，寻找股票间更本质的直接联系。
  - **部分相关性**: 其核心是计算**部分相关性**，即在控制了系统中所有其他变量（股票）的影响之后，两个变量（股票）之间剩余的相关性。
  - **精度矩阵**: 在数学上，部分相关性与**精度矩阵（Precision Matrix）**，即协方差矩阵的逆矩阵，紧密相关。精度矩阵中的零元素，意味着对应的两个变量在给定其他所有变量的条件下是条件独立的。
  - **Graphical Lasso**: 直接计算精度矩阵非常困难且不稳定。**图套索（Graphical Lasso）**是一种前沿的统计学习方法，它通过对精度矩阵施加L1惩罚，来估计一个**稀疏的**精度矩阵。这个“稀疏”的特性使得模型会自动地将许多微弱的、不重要的关系置为零，从而只保留了最重要、最直接的连接，极大地简化了网络结构，使其更具可解释性。
  网络构建后，后续的社区发现和可视化流程与方法二相同。

- **关键依赖包**:
  - `scikit-learn`: 提供核心的`GraphicalLasso`算法实现。
  - 其他依赖包与方法二相同。

- **评价**: 此方法能有效过滤市场噪音，发现更本质的股票内在关联，其结果在金融风险管理、构建多元化投资组合等领域具有更高的应用价值。

## 4. 如何运行

确保已安装所有依赖包后，可直接在`recall/cluster/`目录下运行各个脚本：
```bash
# 示例：运行美股的Glasso网络分析
uv run python network_glasso_us.py
```
