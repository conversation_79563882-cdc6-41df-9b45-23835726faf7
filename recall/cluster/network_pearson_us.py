
import pandas as pd
import numpy as np
import yfinance as yf
import os
import networkx as nx
import matplotlib.pyplot as plt
import community as community_louvain
from scipy.spatial import ConvexHull
import matplotlib.patches as patches
from sklearn.impute import SimpleImputer

COLOR_MAP = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#00ffff', '#ff00ff', '#bada55'] # Red, Green, Blue, Yellow, Cyan, Magenta, etc.

def fetch_us_stock_data(tickers, start_date='2023-01-01', end_date='2024-01-01'):
    """Fetches historical stock data from yfinance."""
    print(f"Fetching data for {len(tickers)} US stocks...")
    data = yf.download(tickers, start=start_date, end=end_date, progress=False, auto_adjust=False)['Adj Close']
    return data

def get_us_tickers():
    """Reads US stock tickers from a CSV file using a robust path."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    csv_path = os.path.normpath(os.path.join(script_dir, '..', '..', 'data', 'tickers', 'tickers_us.csv'))
    df = pd.read_csv(csv_path)
    tickers = df['Ticker'].tolist()[:50]
    return tickers

def run_pearson_network_analysis():
    """Main pipeline for Pearson correlation network analysis of US stocks."""
    # 1. Get Tickers and Fetch Data
    tickers = get_us_tickers()
    price_data = fetch_us_stock_data(tickers)
    price_data.dropna(axis=1, thresh=len(price_data) * 0.9, inplace=True)
    print(f"Data fetched for {price_data.shape[1]} stocks after cleaning.")

    # 2. Feature Engineering & Imputation
    returns = price_data.pct_change().iloc[1:]
    imputer = SimpleImputer(strategy='mean')
    returns_imputed = imputer.fit_transform(returns)
    returns_imputed = pd.DataFrame(returns_imputed, index=returns.index, columns=returns.columns)

    # 3. Calculate Pearson Correlation
    corr_matrix = returns_imputed.corr()

    # 4. Build Graph from Correlation
    G = nx.Graph()
    corr_threshold = 0.5 # Set a threshold for building edges
    for i in range(len(corr_matrix.columns)):
        for j in range(i + 1, len(corr_matrix.columns)):
            if abs(corr_matrix.iloc[i, j]) > corr_threshold:
                G.add_edge(corr_matrix.columns[i], corr_matrix.columns[j], weight=corr_matrix.iloc[i, j])
    
    # Remove isolated nodes
    G.remove_nodes_from(list(nx.isolates(G)))
    print(f"Graph created with {G.number_of_nodes()} nodes and {G.number_of_edges()} edges.")

    # 5. Louvain Community Detection
    partition = community_louvain.best_partition(G, weight='weight')

    # 6. Visualization
    output_image_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'network_pearson_us.png')
    fig, ax = plt.subplots(figsize=(16, 16))
    pos = nx.spring_layout(G, k=1.5, iterations=100, seed=42)

    for community_id in set(partition.values()):
        nodes_in_community = [node for node, cid in partition.items() if cid == community_id]
        if len(nodes_in_community) > 2:
            points = np.array([pos[node] for node in nodes_in_community])
            hull = ConvexHull(points)
            polygon = patches.Polygon(points[hull.vertices, :], closed=True, 
                                      facecolor=COLOR_MAP[community_id % len(COLOR_MAP)], 
                                      alpha=0.25)
            ax.add_patch(polygon)

    node_colors = [COLOR_MAP[partition[node] % len(COLOR_MAP)] for node in G.nodes()]
    edge_colors = ['red' if G.edges[u, v]['weight'] < 0 else 'black' for u, v in G.edges()]

    nx.draw_networkx_nodes(G, pos, node_color=node_colors, node_size=1000, ax=ax)
    nx.draw_networkx_edges(G, pos, edge_color=edge_colors, alpha=0.7, width=0.8, ax=ax)
    nx.draw_networkx_labels(G, pos, font_size=9, font_color='black', ax=ax)

    ax.set_title("US Stock Network (Pearson Correlation & Louvain)", fontsize=20)
    ax.set_aspect('equal', adjustable='box')
    plt.tight_layout()
    plt.savefig(output_image_path)
    print(f"\nPearson network visualization saved to '{output_image_path}'")

    # 7. Print Community Results
    print("\n--- Pearson Network Community Results (US Stocks) ---")
    num_communities = max(partition.values()) + 1
    communities = [[] for _ in range(num_communities)]
    for node, community_id in partition.items():
        communities[community_id].append(node)
    for i, community in enumerate(communities):
        print(f"Community {i + 1}: {sorted(community)}")

if __name__ == '__main__':
    run_pearson_network_analysis()
