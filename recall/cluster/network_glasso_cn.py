import pandas as pd
import numpy as np
import os
from sklearn.preprocessing import StandardScaler
from sklearn.impute import SimpleImputer
from sklearn.covariance import GraphicalLasso
import networkx as nx
import matplotlib.pyplot as plt
import community as community_louvain
from scipy.spatial import ConvexHull
import matplotlib.patches as patches

NAME_DICT = {
    '600000': '浦发银行', '600016': '民生银行', '600028': '中国石化', '600030': '中信证券', '600036': '招商银行',
    '600048': '保利地产', '600050': '中国联通', '600104': '上汽集团', '600109': '国金证券', '600111': '北方稀土',
    '600340': '华夏幸福', '600489': '中金黄金', '600518': '康美药业', '600519': '贵州茅台', '600547': '山东黄金',
    '600585': '海螺水泥', '600606': '绿地控股', '600837': '海通证券', '600887': '伊利股份', '600958': '东方证券',
    '600999': '招商证券', '601006': '大秦铁路', '601088': '中国神华', '601166': '兴业银行', '601169': '北京银行',
    '601211': '国泰君安', '601288': '农业银行', '601318': '中国平安', '601328': '交通银行', '601336': '新华保险',
    '601390': '中国中铁', '601398': '工商银行', '601601': '中国太保', '601628': '中国人寿', '601668': '中国建筑',
    '601688': '华泰证券', '601766': '中国中车', '601788': '光大证券', '601800': '中国交建', '601818': '光大银行',
    '601857': '中国石油', '601881': '中国银河', '601901': '方正证券', '601988': '中国银行', '601989': '中国重工',
    '601998': '中信银行', '603993': '洛阳钼业'
}

COLOR_MAP = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#00ffff', '#ff00ff', '#bada55'] # Red, Green, Blue, Yellow, Cyan, Magenta, etc.

def generate_glasso_graph(csv_path, output_image_path, alpha=0.3, edge_threshold=0.055):
    # 1. 数据加载和格式转换 (与correlation_analysis.py一致)
    long_df = pd.read_csv(csv_path, encoding='gbk')
    df_pivoted = long_df.pivot(index='TradingDay', columns='SecuCode', values='涨跌幅')
    df_pivoted.columns = df_pivoted.columns.astype(str)
    
    tickers = [ticker for ticker in NAME_DICT.keys() if ticker in df_pivoted.columns]
    df = df_pivoted[tickers]

    # 2. 计算部分相关性 (此部分为Graphical Lasso的核心)
    X = df.values
    imputer = SimpleImputer(strategy='mean')
    X_imputed = imputer.fit_transform(X)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X_imputed)

    model = GraphicalLasso(alpha=alpha, mode='cd', tol=1e-4, max_iter=200)
    model.fit(X_scaled)
    
    precision_matrix = model.precision_
    n_features = precision_matrix.shape[0]
    partial_corr = np.zeros_like(precision_matrix)
    for i in range(n_features):
        for j in range(i, n_features):
            if i == j: continue
            p_ij = -precision_matrix[i, j] / np.sqrt(precision_matrix[i, i] * precision_matrix[j, j])
            partial_corr[i, j] = partial_corr[j, i] = p_ij

    # 3. 基于部分相关性阈值构建网络
    G = nx.Graph()
    G.add_nodes_from(df.columns)
    for i in range(len(df.columns)):
        for j in range(i + 1, len(df.columns)):
            if abs(partial_corr[i, j]) > edge_threshold:
                G.add_edge(df.columns[i], df.columns[j], weight=partial_corr[i, j])

    # 4. Louvain社区发现与可视化 (与correlation_analysis.py一致)
    G = nx.relabel_nodes(G, NAME_DICT)
    print(f"Graph created with {G.number_of_nodes()} nodes and {G.number_of_edges()} edges.")

    partition = community_louvain.best_partition(G, weight='weight')
    
    plt.rcParams['font.sans-serif'] = ['PingFang SC', 'Arial Unicode MS', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    fig, ax = plt.subplots(figsize=(16, 16))
    pos = nx.spring_layout(G, k=1.5, iterations=100, seed=42)

    for community_id in set(partition.values()):
        nodes_in_community = [node for node, cid in partition.items() if cid == community_id]
        if len(nodes_in_community) > 2:
            points = np.array([pos[node] for node in nodes_in_community])
            hull = ConvexHull(points)
            polygon = patches.Polygon(points[hull.vertices, :], closed=True, 
                                      facecolor=COLOR_MAP[community_id % len(COLOR_MAP)], 
                                      alpha=0.25)
            ax.add_patch(polygon)

    node_colors = [COLOR_MAP[partition[node] % len(COLOR_MAP)] for node in G.nodes()]
    edge_colors = ['red' if G.edges[u, v]['weight'] < 0 else 'black' for u, v in G.edges()]

    nx.draw_networkx_nodes(G, pos, node_color=node_colors, node_size=1000, ax=ax)
    nx.draw_networkx_edges(G, pos, edge_color=edge_colors, alpha=0.7, width=0.8, ax=ax)
    nx.draw_networkx_labels(G, pos, font_size=12, font_color='darkblue', ax=ax)

    ax.set_title("股票社交网络 (Graphical Lasso & Louvain)", fontsize=20)
    ax.set_aspect('equal', adjustable='box')
    plt.tight_layout()
    plt.savefig(output_image_path)
    print(f"\nGraphical Lasso community graph saved to '{output_image_path}'")

if __name__ == '__main__':
    script_dir = os.path.dirname(os.path.abspath(__file__))
    csv_file_path = os.path.join(script_dir, '50stocks.csv')
    output_img_path = os.path.join(script_dir, 'graph_lasso_community_graph.png')

    generate_glasso_graph(csv_file_path, output_img_path)