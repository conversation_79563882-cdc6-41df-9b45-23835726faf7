import pandas as pd
import numpy as np
import yfinance as yf
import os
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
from sklearn.decomposition import PCA
from sklearn.impute import SimpleImputer

def fetch_us_stock_data(tickers, start_date='2023-01-01', end_date='2024-01-01'):
    """Fetches historical stock data from yfinance."""
    print(f"Fetching data for {len(tickers)} US stocks...")
    # 修正：添加 auto_adjust=False 以兼容旧代码的列名选择方式
    data = yf.download(tickers, start=start_date, end=end_date, progress=False, auto_adjust=False)['Adj Close']
    return data

def get_us_tickers():
    """Reads US stock tickers from a CSV file using a robust path."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    csv_path = os.path.normpath(os.path.join(script_dir, '..', '..', 'data', 'tickers', 'tickers_us.csv'))
    df = pd.read_csv(csv_path)
    # Lets take a smaller sample for faster processing, e.g., first 50
    tickers = df['Ticker'].tolist()[:50]
    return tickers

def run_kmeans_clustering():
    """Main pipeline for the K-Means clustering of US stocks."""
    try:
        tickers = get_us_tickers()
    except FileNotFoundError:
        print(f"Error: tickers_us.csv not found. The path is likely incorrect.")
        print(f"Attempted path: {os.path.normpath(os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'data', 'tickers', 'tickers_us.csv'))}")
        return

    price_data = fetch_us_stock_data(tickers)
    price_data.dropna(axis=1, thresh=len(price_data) * 0.9, inplace=True)
    print(f"Data fetched for {price_data.shape[1]} stocks after cleaning.")

    returns = price_data.pct_change().iloc[1:]

    imputer = SimpleImputer(strategy='mean')
    returns_imputed = imputer.fit_transform(returns.T).T
    returns_imputed = pd.DataFrame(returns_imputed, index=returns.index, columns=returns.columns)

    scaler = StandardScaler()
    returns_scaled = scaler.fit_transform(returns_imputed)
    pca = PCA(n_components=0.95)
    returns_pca = pca.fit_transform(returns_scaled)
    print(f"PCA completed. Number of components: {pca.n_components_}")

    print("Finding optimal number of clusters (K) using Silhouette Score...")
    best_k = -1
    best_score = -1
    for k in range(3, 10):
        kmeans = KMeans(n_clusters=k, random_state=42, n_init='auto')
        kmeans.fit(returns_pca)
        score = silhouette_score(returns_pca, kmeans.labels_)
        print(f"  K={k}, Silhouette Score: {score:.4f}")
        if score > best_score:
            best_score = score
            best_k = k
    print(f"Best K found: {best_k}")

    kmeans = KMeans(n_clusters=best_k, random_state=42, n_init='auto')
    kmeans.fit(returns_pca)
    clusters = {i: [] for i in range(best_k)}
    for i, ticker in enumerate(returns_imputed.columns):
        clusters[kmeans.labels_[i]].append(ticker)

    print("\n--- K-Means Clustering Results (US Stocks) ---")
    for cluster_id, tickers_in_cluster in clusters.items():
        print(f"Cluster {cluster_id + 1}: {tickers_in_cluster}")

if __name__ == '__main__':
    run_kmeans_clustering()
