#!/usr/bin/env python3
"""
专业股票排序策略回测系统 - 基于vectorbt框架
整合技术分析排序策略，支持多资产组合回测

主要功能：
1. 本地股票数据加载和处理
2. 技术分析排序信号生成  
3. 多股票投资组合回测
4. 专业性能分析和可视化报告
5. 可复用的模块化架构

基于vectorbt最新架构设计
作者: AI Assistant
日期: 2025-01-23
"""

import vectorbt as vbt
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import quantstats as qs
import os
import warnings
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime, timedelta
import logging

# 配置日志和警告
warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Module 1: Enhanced Data Handler for Local Stock Data ---
class LocalStockDataHandler:
    """
    加载和处理本地股票数据的增强版数据处理器
    支持US和CN市场数据，包含数据质量检查和清洗功能
    """
    
    def __init__(self, market: str = 'us', timeframe: str = '1d', 
                 data_path: str = 'data', min_history_days: int = 252):
        self.market = market.lower()
        self.timeframe = timeframe
        self.data_path = Path(data_path) / self.market
        self.min_history_days = min_history_days
        self.stock_data = {}
        
    def load_stock_universe(self, max_stocks: int = 100, 
                          min_market_cap: float = 1e9,
                          min_volume: float = 1e6) -> Dict[str, pd.DataFrame]:
        """加载股票池数据，应用质量和流动性过滤"""
        logger.info(f"加载{self.market.upper()}市场数据...")
        
        if not self.data_path.exists():
            logger.error(f"数据路径不存在: {self.data_path}")
            return {}
        
        stock_dirs = [d for d in self.data_path.iterdir() if d.is_dir()]
        loaded_count = 0
        
        for stock_dir in stock_dirs:
            if loaded_count >= max_stocks:
                break
                
            symbol = stock_dir.name
            data_file = stock_dir / f'{self.timeframe}.parquet'
            
            if data_file.exists():
                try:
                    df = pd.read_parquet(data_file)
                    
                    # 数据质量检查
                    if self._quality_check(df, symbol):
                        # 流动性过滤
                        if self._liquidity_check(df, min_market_cap, min_volume):
                            self.stock_data[symbol] = self._prepare_data(df)
                            loaded_count += 1
                            
                except Exception as e:
                    logger.warning(f"加载{symbol}失败: {e}")
        
        logger.info(f"成功加载{loaded_count}只股票")
        return self.stock_data
    
    def _quality_check(self, df: pd.DataFrame, symbol: str) -> bool:
        """数据质量检查"""
        required_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        
        # 检查必要列
        if not all(col in df.columns for col in required_cols):
            return False
        
        # 检查数据长度
        if len(df) < self.min_history_days:
            return False
        
        # 检查价格合理性
        if (df['close'] <= 0).any() or (df['volume'] < 0).any():
            return False
        
        # 检查异常波动（单日涨跌超过50%可能是数据错误）
        daily_returns = df['close'].pct_change().abs()
        if (daily_returns > 0.5).sum() > len(df) * 0.01:
            logger.warning(f"{symbol}: 检测到异常波动")
            return False
            
        return True
    
    def _liquidity_check(self, df: pd.DataFrame, 
                        min_market_cap: float, min_volume: float) -> bool:
        """流动性检查"""
        # 简化市值估算
        df['market_cap'] = df['close'] * df['volume']
        avg_market_cap = df['market_cap'].tail(60).mean()
        avg_volume = df['volume'].tail(60).mean()
        
        return avg_market_cap > min_market_cap and avg_volume > min_volume
    
    def _prepare_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据预处理"""
        df = df.copy()
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp').reset_index(drop=True)
        df.set_index('timestamp', inplace=True)
        
        # 计算基础技术指标
        df['returns'] = df['close'].pct_change()
        df['sma_20'] = df['close'].rolling(20).mean()
        df['sma_50'] = df['close'].rolling(50).mean()
        df['volume_sma_20'] = df['volume'].rolling(20).mean()
        
        return df.fillna(method='ffill').dropna()

# --- Module 2: Technical Analysis Signal Generator ---
class TechnicalRankingSignalGenerator:
    """
    基于技术分析的股票排序信号生成器
    实现多因子股票评分和排序选择
    """
    
    def __init__(self, stock_data: Dict[str, pd.DataFrame], 
                 rebalance_frequency: int = 20, top_n_stocks: int = 10):
        self.stock_data = stock_data
        self.rebalance_frequency = rebalance_frequency
        self.top_n_stocks = top_n_stocks
        self.symbols = list(stock_data.keys())
        
        # 创建价格矩阵用于vectorbt
        self._create_price_matrix()
        
    def _create_price_matrix(self):
        """创建多股票价格矩阵"""
        # 获取所有共同的交易日期
        common_dates = None
        for symbol, df in self.stock_data.items():
            if common_dates is None:
                common_dates = df.index
            else:
                common_dates = common_dates.intersection(df.index)
        
        # 构建价格矩阵
        price_data = {}
        for symbol, df in self.stock_data.items():
            price_data[symbol] = df.reindex(common_dates)['close']
        
        self.price_matrix = pd.DataFrame(price_data)
        self.price_matrix = self.price_matrix.dropna()
        logger.info(f"价格矩阵形状: {self.price_matrix.shape}")
        
    def generate_ranking_signals(self) -> Dict[str, pd.DataFrame]:
        """生成基于排序的交易信号"""
        logger.info("生成技术分析排序信号...")
        
        # 获取重平衡日期
        rebalance_dates = self.price_matrix.index[::self.rebalance_frequency]
        
        # 初始化信号DataFrame
        entries = pd.DataFrame(False, index=self.price_matrix.index, 
                             columns=self.price_matrix.columns)
        exits = pd.DataFrame(False, index=self.price_matrix.index,
                           columns=self.price_matrix.columns)
        
        current_portfolio = set()
        
        for rebalance_date in rebalance_dates:
            try:
                # 无前视偏差选股：使用rebalance_date之前的数据
                lookback_end = rebalance_date
                selected_stocks = self._select_top_stocks(lookback_end)
                
                # 生成交易信号
                new_portfolio = set(selected_stocks)
                
                # 卖出信号：不在新组合中的股票
                stocks_to_sell = current_portfolio - new_portfolio
                for symbol in stocks_to_sell:
                    if symbol in exits.columns:
                        exits.loc[rebalance_date, symbol] = True
                
                # 买入信号：新加入组合的股票
                stocks_to_buy = new_portfolio - current_portfolio
                for symbol in stocks_to_buy:
                    if symbol in entries.columns:
                        entries.loc[rebalance_date, symbol] = True
                
                current_portfolio = new_portfolio
                
            except Exception as e:
                logger.warning(f"在{rebalance_date}生成信号时出错: {e}")
        
        logger.info("信号生成完成")
        return {
            'entries': entries,
            'exits': exits,
            'rebalance_dates': rebalance_dates,
            'price_matrix': self.price_matrix
        }
    
    def _select_top_stocks(self, as_of_date: pd.Timestamp, 
                          lookback_days: int = 20) -> List[str]:
        """基于技术分析指标选择顶级股票"""
        
        # 确保使用历史数据（无前视偏差）
        cutoff_date = as_of_date - pd.Timedelta(days=1)
        
        scores = []
        
        for symbol in self.symbols:
            try:
                df = self.stock_data[symbol]
                historical_data = df[df.index <= cutoff_date]
                
                if len(historical_data) < lookback_days + 50:
                    continue
                
                # 计算多因子评分
                score = self._calculate_technical_score(historical_data, lookback_days)
                
                if score > 0:  # 只考虑正评分的股票
                    scores.append((symbol, score))
                    
            except Exception as e:
                logger.debug(f"计算{symbol}评分时出错: {e}")
        
        # 排序并选择TOP N
        scores.sort(key=lambda x: x[1], reverse=True)
        selected = [symbol for symbol, _ in scores[:self.top_n_stocks]]
        
        logger.debug(f"选股日期: {as_of_date.date()}, 选中: {len(selected)}只")
        return selected
    
    def _calculate_technical_score(self, df: pd.DataFrame, lookback_days: int) -> float:
        """计算技术分析评分（保守版本）"""
        
        if len(df) < lookback_days + 20:
            return 0
        
        recent_data = df.tail(lookback_days + 20)
        score = 0
        
        # 1. 动量因子 (谨慎评分)
        try:
            return_5d = (recent_data['close'].iloc[-1] / recent_data['close'].iloc[-5] - 1) * 100
            return_20d = (recent_data['close'].iloc[-1] / recent_data['close'].iloc[-20] - 1) * 100
            
            # 温和正收益给分
            if 1 < return_5d < 10:  # 5日涨幅1-10%
                score += 1
            if 2 < return_20d < 20:  # 20日涨幅2-20%
                score += 2
                
        except (IndexError, KeyError):
            pass
        
        # 2. 趋势因子
        try:
            current_price = recent_data['close'].iloc[-1]
            sma_20 = recent_data['close'].rolling(20).mean().iloc[-1]
            sma_50 = recent_data['close'].rolling(50).mean().iloc[-1]
            
            if current_price > sma_20 > sma_50:  # 均线多头排列
                score += 2
            elif current_price > sma_20:  # 价格在短期均线上方
                score += 1
                
        except (IndexError, KeyError):
            pass
        
        # 3. 成交量因子
        try:
            recent_volume = recent_data['volume'].iloc[-5:].mean()  # 最近5日平均成交量
            volume_ma = recent_data['volume'].rolling(20).mean().iloc[-1]
            
            if recent_volume > volume_ma * 1.2:  # 成交量放大20%
                score += 1
                
        except (IndexError, KeyError):
            pass
        
        # 4. 波动性因子（避免过度波动）
        try:
            volatility = recent_data['close'].pct_change().tail(10).std()
            if volatility < 0.03:  # 日波动率小于3%
                score += 0.5
                
        except (IndexError, KeyError):
            pass
        
        return max(0, score)  # 确保非负评分

# --- Module 3: Portfolio Backtester with vectorbt ---
class VectorBTPortfolioBacktester:
    """
    基于vectorbt的投资组合回测器
    支持多资产组合，包含交易成本和现实约束
    """
    
    def __init__(self, fees: float = 0.002, slippage: float = 0.001, 
                 freq: str = 'D', init_cash: float = 1000000):
        self.fees = fees
        self.slippage = slippage  
        self.freq = freq
        self.init_cash = init_cash
        
    def run_backtest(self, signals: Dict) -> vbt.Portfolio:
        """运行vectorbt回测"""
        logger.info("开始运行vectorbt组合回测...")
        
        price_matrix = signals['price_matrix']
        entries = signals['entries']
        exits = signals['exits']
        
        # 确保所有DataFrame有相同的索引和列
        common_index = price_matrix.index.intersection(entries.index).intersection(exits.index)
        common_columns = price_matrix.columns.intersection(entries.columns).intersection(exits.columns)
        
        price_matrix = price_matrix.reindex(index=common_index, columns=common_columns)
        entries = entries.reindex(index=common_index, columns=common_columns)
        exits = exits.reindex(index=common_index, columns=common_columns)
        
        logger.info(f"回测数据维度: 价格{price_matrix.shape}, 入场{entries.shape}, 出场{exits.shape}")
        
        # 创建vectorbt投资组合
        portfolio = vbt.Portfolio.from_signals(
            close=price_matrix,
            entries=entries,
            exits=exits,
            size=np.inf,  # 使用所有可用资金
            size_type='percent',  # 按百分比分配
            fees=self.fees,
            slippage=self.slippage,
            init_cash=self.init_cash,
            freq=self.freq,
            group_by=True,  # 作为单一组合管理
            cash_sharing=True,  # 股票间共享资金
            call_seq='auto'  # 自动执行顺序
        )
        
        logger.info("vectorbt回测完成")
        return portfolio

# --- Module 4: Enhanced Results Analyzer ---
class EnhancedResultsAnalyzer:
    """
    增强版结果分析器
    生成专业的回测报告和可视化
    """
    
    def __init__(self, portfolio: vbt.Portfolio, market: str = 'us', 
                 report_dir: str = "backtest/reports/stock_ranking"):
        self.portfolio = portfolio
        self.market = market
        self.report_dir = Path(report_dir)
        self.report_dir.mkdir(parents=True, exist_ok=True)
        
    def print_comprehensive_stats(self):
        """打印全面的性能统计"""
        print(f"\n{'='*80}")
        print(f"股票排序策略回测结果 - {self.market.upper()}市场")
        print(f"{'='*80}")
        
        # 基础统计
        stats = self.portfolio.stats()
        print("\n📊 基础统计指标:")
        print(f"总收益率: {stats['Total Return [%]']:.2f}%")
        print(f"年化收益率: {stats.get('Annual Return [%]', 'N/A')}")
        print(f"最大回撤: {stats.get('Max Drawdown [%]', 'N/A')}")
        print(f"夏普比率: {stats.get('Sharpe Ratio', 'N/A')}")
        print(f"胜率: {stats.get('Win Rate [%]', 'N/A')}")
        
        # 交易统计
        if hasattr(self.portfolio, 'orders') and len(self.portfolio.orders.records) > 0:
            print(f"\n📈 交易统计:")
            print(f"总交易次数: {len(self.portfolio.orders.records)}")
            print(f"买入次数: {len(self.portfolio.orders.records[self.portfolio.orders.records['side'] == 0])}")
            print(f"卖出次数: {len(self.portfolio.orders.records[self.portfolio.orders.records['side'] == 1])}")
        
        print(f"\n完整统计报告:")
        print(stats)
        
    def generate_professional_reports(self) -> Dict[str, str]:
        """生成专业的HTML报告"""
        logger.info("生成专业回测报告...")
        
        reports = {}
        
        try:
            # 1. VectorBT综合报告
            vbt_report_path = self.report_dir / f"{self.market}_stock_ranking_vbt_report.html"
            
            # 获取统计表
            stats_df = self.portfolio.stats().to_frame(name="Value")
            stats_html = stats_df.style.set_table_attributes(
                'class="table table-striped table-hover table-sm"'
            ).to_html()
            
            # 创建组合可视化图表（简化版本）
            try:
                fig = self.portfolio.plot(title=f"{self.market.upper()}股票排序策略表现")
                plot_html = fig.to_html(full_html=False, include_plotlyjs='cdn')
            except Exception as e:
                logger.warning(f"创建可视化图表失败: {e}")
                plot_html = "<p>图表生成失败，请查看统计数据</p>"
            
            # 生成综合HTML报告
            html_content = f"""
            <html>
            <head>
                <title>{self.market.upper()}股票排序策略回测报告</title>
                <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .header {{ text-align: center; margin-bottom: 30px; }}
                    .container-fluid {{ display: flex; flex-wrap: nowrap; padding: 10px; }}
                    .plot-container {{ flex: 3; padding-right: 20px; }}
                    .stats-container {{ flex: 1; overflow-y: auto; font-size: 0.8rem; }}
                    .table-sm td, .table-sm th {{ padding: .3rem; }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>{self.market.upper()}股票排序策略回测分析</h1>
                    <p>基于技术分析的多因子股票选择策略</p>
                    <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                <div class="container-fluid">
                    <div class="plot-container">
                        {plot_html}
                    </div>
                    <div class="stats-container">
                        <h4>📊 性能指标</h4>
                        {stats_html}
                    </div>
                </div>
            </body>
            </html>
            """
            
            with open(vbt_report_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            reports['vectorbt_report'] = str(vbt_report_path)
            logger.info(f"VectorBT报告生成: {vbt_report_path}")
            
        except Exception as e:
            logger.error(f"生成VectorBT报告失败: {e}")
        
        try:
            # 2. QuantStats报告
            qs_report_path = self.report_dir / f"{self.market}_stock_ranking_qs_report.html"
            
            returns = self.portfolio.returns()
            if not returns.empty and returns.notna().any():
                qs.extend_pandas()
                qs.reports.html(
                    returns, 
                    output=qs_report_path,
                    title=f"{self.market.upper()}股票排序策略 - QuantStats分析"
                )
                reports['quantstats_report'] = str(qs_report_path)
                logger.info(f"QuantStats报告生成: {qs_report_path}")
            else:
                logger.warning("收益率数据为空，跳过QuantStats报告")
                
        except Exception as e:
            logger.error(f"生成QuantStats报告失败: {e}")
        
        return reports

# --- Main Execution Pipeline ---
class StockRankingBacktestPipeline:
    """
    股票排序策略回测流水线
    整合所有模块，提供完整的回测解决方案
    """
    
    def __init__(self, market: str = 'us', config: Optional[Dict] = None):
        self.market = market
        self.config = config or self._default_config()
        
    def _default_config(self) -> Dict:
        """默认配置"""
        return {
            'data': {
                'timeframe': '1d',
                'max_stocks': 50,
                'min_history_days': 252,
                'min_market_cap': 1e9,
                'min_volume': 1e6
            },
            'strategy': {
                'rebalance_frequency': 20,
                'top_n_stocks': 10
            },
            'backtest': {
                'fees': 0.002,
                'slippage': 0.001,
                'init_cash': 1000000,
                'freq': 'D'
            }
        }
    
    def run_full_pipeline(self) -> Tuple[vbt.Portfolio, Dict]:
        """运行完整的回测流水线"""
        
        print(f"🚀 启动{self.market.upper()}市场股票排序策略回测流水线")
        print(f"配置: {self.config}")
        
        # 1. 数据加载
        print(f"\n📊 第1步: 加载股票数据...")
        data_handler = LocalStockDataHandler(
            market=self.market,
            timeframe=self.config['data']['timeframe'],
            min_history_days=self.config['data']['min_history_days']
        )
        
        stock_data = data_handler.load_stock_universe(
            max_stocks=self.config['data']['max_stocks'],
            min_market_cap=self.config['data']['min_market_cap'],
            min_volume=self.config['data']['min_volume']
        )
        
        if len(stock_data) < 5:
            raise ValueError(f"可用股票数量太少: {len(stock_data)}")
        
        # 2. 信号生成
        print(f"\n📈 第2步: 生成排序信号...")
        signal_generator = TechnicalRankingSignalGenerator(
            stock_data=stock_data,
            rebalance_frequency=self.config['strategy']['rebalance_frequency'],
            top_n_stocks=self.config['strategy']['top_n_stocks']
        )
        
        signals = signal_generator.generate_ranking_signals()
        
        # 3. 回测执行
        print(f"\n⚡ 第3步: 执行vectorbt回测...")
        backtester = VectorBTPortfolioBacktester(
            fees=self.config['backtest']['fees'],
            slippage=self.config['backtest']['slippage'],
            freq=self.config['backtest']['freq'],
            init_cash=self.config['backtest']['init_cash']
        )
        
        portfolio = backtester.run_backtest(signals)
        
        # 4. 结果分析
        print(f"\n📋 第4步: 生成分析报告...")
        analyzer = EnhancedResultsAnalyzer(portfolio, self.market)
        analyzer.print_comprehensive_stats()
        reports = analyzer.generate_professional_reports()
        
        print(f"\n✅ 回测流水线完成!")
        print(f"生成的报告:")
        for report_type, path in reports.items():
            print(f"  - {report_type}: {path}")
        
        return portfolio, {
            'signals': signals,
            'reports': reports,
            'stock_data': stock_data,
            'config': self.config
        }

def main():
    """主函数 - 演示完整流水线"""
    
    # 运行美股回测
    print("🇺🇸 美股市场回测")
    us_pipeline = StockRankingBacktestPipeline(
        market='us',
        config={
            'data': {
                'timeframe': '1d',
                'max_stocks': 30,  # 限制数量以加快测试
                'min_history_days': 252,
                'min_market_cap': 5e8,  # 降低门槛
                'min_volume': 5e5
            },
            'strategy': {
                'rebalance_frequency': 15,  # 更频繁重平衡
                'top_n_stocks': 8
            },
            'backtest': {
                'fees': 0.002,
                'slippage': 0.001,
                'init_cash': 1000000,
                'freq': 'D'
            }
        }
    )
    
    try:
        us_portfolio, us_results = us_pipeline.run_full_pipeline()
        print(f"美股回测成功完成！")
    except Exception as e:
        print(f"美股回测失败: {e}")
        us_portfolio, us_results = None, None
    
    # 运行A股回测
    print(f"\n" + "="*60)
    print("🇨🇳 A股市场回测")
    cn_pipeline = StockRankingBacktestPipeline(
        market='cn',
        config={
            'data': {
                'timeframe': '1d', 
                'max_stocks': 30,
                'min_history_days': 252,
                'min_market_cap': 5e8,
                'min_volume': 5e5
            },
            'strategy': {
                'rebalance_frequency': 15,
                'top_n_stocks': 8
            },
            'backtest': {
                'fees': 0.003,  # A股手续费略高
                'slippage': 0.002,
                'init_cash': 1000000,
                'freq': 'D'
            }
        }
    )
    
    try:
        cn_portfolio, cn_results = cn_pipeline.run_full_pipeline()
        print(f"A股回测成功完成！")
    except Exception as e:
        print(f"A股回测失败: {e}")
        cn_portfolio, cn_results = None, None
    
    # 总结
    print(f"\n" + "="*60)
    print("🎯 股票排序策略回测总结")
    print(f"基于vectorbt的专业回测系统")
    print(f"整合技术分析多因子选股")
    print(f"支持美股和A股市场")
    print(f"生成专业HTML报告")
    
    return {
        'us': {'portfolio': us_portfolio, 'results': us_results},
        'cn': {'portfolio': cn_portfolio, 'results': cn_results}
    }

if __name__ == "__main__":
    results = main()
