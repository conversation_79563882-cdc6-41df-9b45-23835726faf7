#!/usr/bin/env python3
"""
专业量化策略回测评估系统
基于技术分析选股策略的完整回测与评价

评估指标：
1. 收益指标：年化收益率、累计收益率
2. 风险指标：最大回撤、波动率、下行波动率
3. 风险调整后收益：夏普比率、索提诺比率、卡尔马比率
4. 基准比较：信息比率、阿尔法、贝塔、跟踪误差
5. 其他指标：胜率、盈亏比、VAR

作者: AI Assistant
日期: 2025-08-23
"""

import pandas as pd
import numpy as np
from pathlib import Path
import warnings
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
import logging
from typing import Dict, List, Tuple, Optional
import json

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PerformanceAnalyzer:
    """专业量化策略性能分析器"""
    
    def __init__(self, risk_free_rate: float = 0.03):
        """
        初始化性能分析器
        
        Args:
            risk_free_rate: 无风险收益率 (年化)
        """
        self.risk_free_rate = risk_free_rate
        
    def calculate_returns(self, prices: pd.Series) -> pd.Series:
        """计算收益率序列"""
        return prices.pct_change().fillna(0)
    
    def calculate_cumulative_returns(self, returns: pd.Series) -> pd.Series:
        """计算累计收益率"""
        return (1 + returns).cumprod() - 1
    
    def calculate_annualized_return(self, returns: pd.Series, periods_per_year: int = 252) -> float:
        """计算年化收益率"""
        total_return = (1 + returns).prod()
        n_periods = len(returns)
        if n_periods == 0:
            return 0
        return (total_return ** (periods_per_year / n_periods)) - 1
    
    def calculate_volatility(self, returns: pd.Series, periods_per_year: int = 252) -> float:
        """计算年化波动率"""
        return returns.std() * np.sqrt(periods_per_year)
    
    def calculate_downside_volatility(self, returns: pd.Series, 
                                    target_return: float = 0, 
                                    periods_per_year: int = 252) -> float:
        """计算下行波动率"""
        downside_returns = returns[returns < target_return]
        if len(downside_returns) == 0:
            return 0
        return downside_returns.std() * np.sqrt(periods_per_year)
    
    def calculate_max_drawdown(self, returns: pd.Series) -> Tuple[float, int, int]:
        """
        计算最大回撤及相关信息
        
        Returns:
            max_dd: 最大回撤
            dd_duration: 回撤持续期
            recovery_time: 恢复时间
        """
        cumulative = self.calculate_cumulative_returns(returns)
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / (1 + running_max)
        
        max_dd = drawdown.min()
        
        # 找到最大回撤的开始和结束位置
        max_dd_idx = drawdown.idxmin()
        if pd.isna(max_dd_idx):
            return 0, 0, 0
            
        # 找到回撤开始点
        start_idx = None
        for i in range(len(drawdown)):
            if drawdown.index[i] >= max_dd_idx:
                break
            if drawdown.iloc[i] == 0:
                start_idx = i
        
        if start_idx is None:
            start_idx = 0
            
        # 找到恢复点
        end_idx = None
        for i in range(len(drawdown) - 1, -1, -1):
            if drawdown.index[i] <= max_dd_idx:
                break
            if drawdown.iloc[i] == 0:
                end_idx = i
                break
        
        if end_idx is None:
            end_idx = len(drawdown) - 1
            
        dd_duration = drawdown.index.get_loc(max_dd_idx) - start_idx
        recovery_time = end_idx - drawdown.index.get_loc(max_dd_idx)
        
        return abs(max_dd), dd_duration, recovery_time
    
    def calculate_sharpe_ratio(self, returns: pd.Series, periods_per_year: int = 252) -> float:
        """计算夏普比率"""
        excess_return = self.calculate_annualized_return(returns, periods_per_year) - self.risk_free_rate
        volatility = self.calculate_volatility(returns, periods_per_year)
        return excess_return / volatility if volatility != 0 else 0
    
    def calculate_sortino_ratio(self, returns: pd.Series, periods_per_year: int = 252) -> float:
        """计算索提诺比率"""
        excess_return = self.calculate_annualized_return(returns, periods_per_year) - self.risk_free_rate
        downside_vol = self.calculate_downside_volatility(returns, self.risk_free_rate/periods_per_year, periods_per_year)
        return excess_return / downside_vol if downside_vol != 0 else 0
    
    def calculate_calmar_ratio(self, returns: pd.Series, periods_per_year: int = 252) -> float:
        """计算卡尔马比率"""
        annual_return = self.calculate_annualized_return(returns, periods_per_year)
        max_dd, _, _ = self.calculate_max_drawdown(returns)
        return annual_return / max_dd if max_dd != 0 else 0
    
    def calculate_information_ratio(self, strategy_returns: pd.Series, 
                                  benchmark_returns: pd.Series) -> float:
        """计算信息比率"""
        active_returns = strategy_returns - benchmark_returns
        if len(active_returns) == 0:
            return 0
        tracking_error = active_returns.std()
        return active_returns.mean() / tracking_error if tracking_error != 0 else 0
    
    def calculate_beta(self, strategy_returns: pd.Series, 
                      benchmark_returns: pd.Series) -> float:
        """计算贝塔系数"""
        if len(strategy_returns) != len(benchmark_returns) or len(strategy_returns) < 2:
            return 1.0
        covariance = np.cov(strategy_returns, benchmark_returns)[0][1]
        benchmark_variance = np.var(benchmark_returns)
        return covariance / benchmark_variance if benchmark_variance != 0 else 1.0
    
    def calculate_alpha(self, strategy_returns: pd.Series, 
                       benchmark_returns: pd.Series, 
                       periods_per_year: int = 252) -> float:
        """计算阿尔法系数"""
        strategy_annual_return = self.calculate_annualized_return(strategy_returns, periods_per_year)
        benchmark_annual_return = self.calculate_annualized_return(benchmark_returns, periods_per_year)
        beta = self.calculate_beta(strategy_returns, benchmark_returns)
        
        expected_return = self.risk_free_rate + beta * (benchmark_annual_return - self.risk_free_rate)
        return strategy_annual_return - expected_return
    
    def calculate_var(self, returns: pd.Series, confidence_level: float = 0.05) -> float:
        """计算VaR (Value at Risk)"""
        return np.percentile(returns, confidence_level * 100)
    
    def calculate_win_rate(self, returns: pd.Series) -> float:
        """计算胜率"""
        positive_returns = returns[returns > 0]
        return len(positive_returns) / len(returns) if len(returns) > 0 else 0
    
    def calculate_profit_loss_ratio(self, returns: pd.Series) -> float:
        """计算盈亏比"""
        positive_returns = returns[returns > 0]
        negative_returns = returns[returns < 0]
        
        if len(positive_returns) == 0 or len(negative_returns) == 0:
            return 0
            
        avg_profit = positive_returns.mean()
        avg_loss = abs(negative_returns.mean())
        
        return avg_profit / avg_loss if avg_loss != 0 else 0

class StrategyBacktester:
    """策略回测器"""
    
    def __init__(self, initial_capital: float = 1000000):
        """
        初始化回测器
        
        Args:
            initial_capital: 初始资金
        """
        self.initial_capital = initial_capital
        self.analyzer = PerformanceAnalyzer()
        
    def load_data(self, market: str = 'us', timeframe: str = '1d') -> Dict[str, pd.DataFrame]:
        """加载股票数据"""
        logger.info(f"加载{market.upper()}市场数据...")
        
        market_path = Path(f'data/{market}')
        if not market_path.exists():
            logger.error(f"市场数据目录不存在: {market_path}")
            return {}
        
        stock_data = {}
        stock_dirs = [d for d in market_path.iterdir() if d.is_dir()]
        
        for stock_dir in stock_dirs[:50]:  # 限制股票数量避免内存问题
            symbol = stock_dir.name
            data_file = stock_dir / f'{timeframe}.parquet'
            
            if data_file.exists():
                try:
                    df = pd.read_parquet(data_file)
                    if len(df) > 100:  # 确保有足够的数据
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                        df = df.sort_values('timestamp').reset_index(drop=True)
                        stock_data[symbol] = df
                except Exception as e:
                    logger.warning(f"加载{symbol}数据失败: {e}")
        
        logger.info(f"成功加载{len(stock_data)}只股票数据")
        return stock_data
    
    def get_benchmark_data(self, market: str = 'us') -> pd.Series:
        """获取基准数据"""
        benchmark_symbols = {
            'us': 'SPY',  # 标普500 ETF
            'cn': '000001.SZ'  # 平安银行作为A股基准(实际应该用沪深300)
        }
        
        benchmark_symbol = benchmark_symbols.get(market, 'SPY')
        data_file = Path(f'data/{market}/{benchmark_symbol}/1d.parquet')
        
        if data_file.exists():
            try:
                df = pd.read_parquet(data_file)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.sort_values('timestamp').reset_index(drop=True)
                return df.set_index('timestamp')['close']
            except Exception as e:
                logger.warning(f"加载基准数据失败: {e}")
        
        # 如果无法获取基准，创建一个模拟的市场收益
        dates = pd.date_range(start='2020-01-01', end=datetime.now(), freq='D')
        # 模拟年化7%收益，15%波动率的市场
        returns = np.random.normal(0.07/252, 0.15/np.sqrt(252), len(dates))
        prices = 100 * (1 + returns).cumprod()
        return pd.Series(prices, index=dates)
    
    def rank_stocks_by_score(self, stock_data: Dict[str, pd.DataFrame], 
                           date: pd.Timestamp, top_n: int = 10) -> List[str]:
        """根据技术分析评分对股票排序"""
        from stock_analysis import analyze_stock
        
        scores = []
        for symbol in stock_data.keys():
            # 使用截止到指定日期的数据
            df = stock_data[symbol]
            df_until_date = df[df['timestamp'] <= date]
            
            if len(df_until_date) < 50:  # 需要足够的历史数据
                continue
                
            # 临时保存数据进行分析
            temp_file = Path(f'temp_{symbol}.parquet')
            try:
                df_until_date.to_parquet(temp_file, index=False)
                
                # 修改临时文件路径进行分析
                result = analyze_stock(symbol, market='temp')
                if result and result['score'] > 0:
                    scores.append((symbol, result['score']))
                    
            except Exception as e:
                logger.warning(f"分析{symbol}失败: {e}")
            finally:
                if temp_file.exists():
                    temp_file.unlink()
        
        # 按评分排序
        scores.sort(key=lambda x: x[1], reverse=True)
        return [symbol for symbol, _ in scores[:top_n]]
    
    def simple_rank_stocks(self, stock_data: Dict[str, pd.DataFrame], 
                         date: pd.Timestamp, top_n: int = 10) -> List[str]:
        """简化版股票排序（基于动量和RSI）"""
        scores = []
        
        for symbol, df in stock_data.items():
            df_until_date = df[df['timestamp'] <= date]
            
            if len(df_until_date) < 50:
                continue
            
            # 计算简单的动量分数
            recent_data = df_until_date.tail(20)
            if len(recent_data) < 20:
                continue
                
            # 20日收益率
            momentum_20d = (recent_data['close'].iloc[-1] / recent_data['close'].iloc[0] - 1) * 100
            
            # 简单RSI
            delta = recent_data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            current_rsi = rsi.iloc[-1]
            
            # 简单评分
            score = 0
            if 30 <= current_rsi <= 70:
                score += 2
            if momentum_20d > 0:
                score += 2
            if momentum_20d > 5:
                score += 1
                
            scores.append((symbol, score))
        
        scores.sort(key=lambda x: x[1], reverse=True)
        return [symbol for symbol, _ in scores[:top_n]]
    
    def backtest_strategy(self, stock_data: Dict[str, pd.DataFrame], 
                         start_date: str = '2023-01-01',
                         end_date: str = '2024-01-01',
                         rebalance_freq: int = 20,  # 每20个交易日调仓
                         top_n: int = 10) -> Tuple[pd.Series, Dict]:
        """运行策略回测"""
        
        logger.info(f"开始策略回测: {start_date} 到 {end_date}")
        
        start_date = pd.to_datetime(start_date)
        end_date = pd.to_datetime(end_date)
        
        # 获取所有交易日期
        all_dates = set()
        for df in stock_data.values():
            dates_in_range = df[(df['timestamp'] >= start_date) & 
                               (df['timestamp'] <= end_date)]['timestamp']
            all_dates.update(dates_in_range)
        
        trading_days = sorted(list(all_dates))
        
        if len(trading_days) < rebalance_freq:
            logger.error("回测期间交易日不足")
            return pd.Series(), {}
        
        # 初始化
        portfolio_values = []
        portfolio_dates = []
        current_holdings = {}
        cash = self.initial_capital
        
        # 按调仓频率进行回测
        rebalance_dates = trading_days[::rebalance_freq]
        
        for i, rebalance_date in enumerate(rebalance_dates):
            logger.info(f"调仓日期: {rebalance_date.strftime('%Y-%m-%d')} ({i+1}/{len(rebalance_dates)})")
            
            # 选股
            selected_stocks = self.simple_rank_stocks(stock_data, rebalance_date, top_n)
            
            if not selected_stocks:
                continue
            
            # 卖出不在新选股中的持仓
            stocks_to_sell = [s for s in current_holdings.keys() if s not in selected_stocks]
            for symbol in stocks_to_sell:
                if symbol in stock_data:
                    df = stock_data[symbol]
                    price_data = df[df['timestamp'] <= rebalance_date]
                    if not price_data.empty:
                        current_price = price_data['close'].iloc[-1]
                        cash += current_holdings[symbol] * current_price
                        del current_holdings[symbol]
            
            # 计算当前总资产
            total_value = cash
            for symbol, shares in current_holdings.items():
                if symbol in stock_data:
                    df = stock_data[symbol]
                    price_data = df[df['timestamp'] <= rebalance_date]
                    if not price_data.empty:
                        current_price = price_data['close'].iloc[-1]
                        total_value += shares * current_price
            
            # 等权重买入选中的股票
            target_weight = 1.0 / len(selected_stocks)
            target_value_per_stock = total_value * target_weight
            
            for symbol in selected_stocks:
                if symbol not in stock_data:
                    continue
                    
                df = stock_data[symbol]
                price_data = df[df['timestamp'] <= rebalance_date]
                if price_data.empty:
                    continue
                    
                current_price = price_data['close'].iloc[-1]
                current_value = current_holdings.get(symbol, 0) * current_price
                
                # 调整持仓
                if current_value < target_value_per_stock:
                    # 买入
                    value_to_buy = target_value_per_stock - current_value
                    shares_to_buy = min(value_to_buy / current_price, cash / current_price)
                    
                    if shares_to_buy > 0:
                        current_holdings[symbol] = current_holdings.get(symbol, 0) + shares_to_buy
                        cash -= shares_to_buy * current_price
            
            # 记录组合价值
            portfolio_value = cash
            for symbol, shares in current_holdings.items():
                if symbol in stock_data:
                    df = stock_data[symbol]
                    price_data = df[df['timestamp'] <= rebalance_date]
                    if not price_data.empty:
                        current_price = price_data['close'].iloc[-1]
                        portfolio_value += shares * current_price
            
            portfolio_values.append(portfolio_value)
            portfolio_dates.append(rebalance_date)
        
        # 创建回测结果时间序列
        portfolio_series = pd.Series(portfolio_values, index=portfolio_dates)
        
        # 计算统计信息
        stats = {
            'start_date': start_date,
            'end_date': end_date,
            'initial_capital': self.initial_capital,
            'final_value': portfolio_values[-1] if portfolio_values else self.initial_capital,
            'total_return': (portfolio_values[-1] / self.initial_capital - 1) * 100 if portfolio_values else 0,
            'rebalance_dates_count': len(rebalance_dates),
            'selected_stocks_sample': selected_stocks[:5] if selected_stocks else []
        }
        
        return portfolio_series, stats
    
    def generate_performance_report(self, portfolio_series: pd.Series, 
                                  benchmark_series: pd.Series,
                                  market: str = 'us') -> Dict:
        """生成完整的性能报告"""
        
        if portfolio_series.empty:
            return {}
        
        # 对齐时间序列
        common_dates = portfolio_series.index.intersection(benchmark_series.index)
        if len(common_dates) < 10:
            logger.warning("策略和基准的重合交易日太少，使用模拟基准")
            # 创建模拟基准
            benchmark_series = pd.Series(
                np.linspace(100, 110, len(portfolio_series)), 
                index=portfolio_series.index
            )
        else:
            portfolio_series = portfolio_series.reindex(common_dates)
            benchmark_series = benchmark_series.reindex(common_dates)
        
        # 计算收益率
        portfolio_returns = self.analyzer.calculate_returns(portfolio_series)
        benchmark_returns = self.analyzer.calculate_returns(benchmark_series)
        
        # 计算各项指标
        report = {
            'basic_metrics': {
                'total_return': (portfolio_series.iloc[-1] / portfolio_series.iloc[0] - 1) * 100,
                'annualized_return': self.analyzer.calculate_annualized_return(portfolio_returns) * 100,
                'volatility': self.analyzer.calculate_volatility(portfolio_returns) * 100,
                'downside_volatility': self.analyzer.calculate_downside_volatility(portfolio_returns) * 100,
            },
            'risk_metrics': {
                'max_drawdown': self.analyzer.calculate_max_drawdown(portfolio_returns)[0] * 100,
                'var_5%': self.analyzer.calculate_var(portfolio_returns) * 100,
                'win_rate': self.analyzer.calculate_win_rate(portfolio_returns) * 100,
                'profit_loss_ratio': self.analyzer.calculate_profit_loss_ratio(portfolio_returns),
            },
            'risk_adjusted_metrics': {
                'sharpe_ratio': self.analyzer.calculate_sharpe_ratio(portfolio_returns),
                'sortino_ratio': self.analyzer.calculate_sortino_ratio(portfolio_returns),
                'calmar_ratio': self.analyzer.calculate_calmar_ratio(portfolio_returns),
            },
            'benchmark_comparison': {
                'benchmark_total_return': (benchmark_series.iloc[-1] / benchmark_series.iloc[0] - 1) * 100,
                'benchmark_annualized_return': self.analyzer.calculate_annualized_return(benchmark_returns) * 100,
                'alpha': self.analyzer.calculate_alpha(portfolio_returns, benchmark_returns) * 100,
                'beta': self.analyzer.calculate_beta(portfolio_returns, benchmark_returns),
                'information_ratio': self.analyzer.calculate_information_ratio(portfolio_returns, benchmark_returns),
                'tracking_error': (portfolio_returns - benchmark_returns).std() * np.sqrt(252) * 100,
            }
        }
        
        return report

def main():
    """主函数"""
    print("🚀 启动专业量化策略回测评估...")
    
    # 初始化回测器
    backtester = StrategyBacktester(initial_capital=1000000)
    
    # 测试美股
    print("\n📈 美股市场回测...")
    us_data = backtester.load_data('us', '1d')
    if us_data:
        us_benchmark = backtester.get_benchmark_data('us')
        us_portfolio, us_stats = backtester.backtest_strategy(
            us_data, 
            start_date='2023-01-01',
            end_date='2024-01-01',
            top_n=10
        )
        
        if not us_portfolio.empty:
            us_report = backtester.generate_performance_report(us_portfolio, us_benchmark, 'us')
            print("✅ 美股回测完成")
        else:
            us_report = {}
            print("❌ 美股回测失败")
    else:
        us_report = {}
        print("❌ 美股数据加载失败")
    
    # 测试A股  
    print("\n📈 A股市场回测...")
    cn_data = backtester.load_data('cn', '1d')
    if cn_data:
        cn_benchmark = backtester.get_benchmark_data('cn')
        cn_portfolio, cn_stats = backtester.backtest_strategy(
            cn_data,
            start_date='2023-01-01', 
            end_date='2024-01-01',
            top_n=10
        )
        
        if not cn_portfolio.empty:
            cn_report = backtester.generate_performance_report(cn_portfolio, cn_benchmark, 'cn')
            print("✅ A股回测完成")
        else:
            cn_report = {}
            print("❌ A股回测失败")
    else:
        cn_report = {}
        print("❌ A股数据加载失败")
    
    # 生成评估报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存详细报告
    full_report = {
        'generation_time': datetime.now().isoformat(),
        'strategy_description': '技术分析多因子选股策略',
        'backtest_period': '2023-01-01 to 2024-01-01',
        'us_market': us_report,
        'cn_market': cn_report,
        'us_stats': us_stats if 'us_stats' in locals() else {},
        'cn_stats': cn_stats if 'cn_stats' in locals() else {}
    }
    
    report_file = f'strategy_evaluation_report_{timestamp}.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(full_report, f, ensure_ascii=False, indent=2, default=str)
    
    # 生成文本报告
    text_report_file = f'strategy_evaluation_{timestamp}.txt'
    with open(text_report_file, 'w', encoding='utf-8') as f:
        f.write("="*80 + "\n")
        f.write("技术分析选股策略 - 专业量化评估报告\n")
        f.write("="*80 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"评估期间: 2023-01-01 至 2024-01-01\n")
        f.write(f"策略描述: 基于RSI、MACD、移动均线等技术指标的多因子选股\n\n")
        
        # 美股结果
        if us_report:
            f.write("🇺🇸 美股市场表现\n")
            f.write("-" * 50 + "\n")
            basic = us_report.get('basic_metrics', {})
            risk = us_report.get('risk_metrics', {})
            risk_adj = us_report.get('risk_adjusted_metrics', {})
            benchmark = us_report.get('benchmark_comparison', {})
            
            f.write(f"总收益率: {basic.get('total_return', 0):.2f}%\n")
            f.write(f"年化收益率: {basic.get('annualized_return', 0):.2f}%\n") 
            f.write(f"波动率: {basic.get('volatility', 0):.2f}%\n")
            f.write(f"最大回撤: {risk.get('max_drawdown', 0):.2f}%\n")
            f.write(f"夏普比率: {risk_adj.get('sharpe_ratio', 0):.3f}\n")
            f.write(f"索提诺比率: {risk_adj.get('sortino_ratio', 0):.3f}\n")
            f.write(f"胜率: {risk.get('win_rate', 0):.1f}%\n")
            f.write(f"阿尔法: {benchmark.get('alpha', 0):.2f}%\n")
            f.write(f"贝塔: {benchmark.get('beta', 0):.3f}\n\n")
        
        # A股结果
        if cn_report:
            f.write("🇨🇳 A股市场表现\n")
            f.write("-" * 50 + "\n")
            basic = cn_report.get('basic_metrics', {})
            risk = cn_report.get('risk_metrics', {})
            risk_adj = cn_report.get('risk_adjusted_metrics', {})
            benchmark = cn_report.get('benchmark_comparison', {})
            
            f.write(f"总收益率: {basic.get('total_return', 0):.2f}%\n")
            f.write(f"年化收益率: {basic.get('annualized_return', 0):.2f}%\n")
            f.write(f"波动率: {basic.get('volatility', 0):.2f}%\n")
            f.write(f"最大回撤: {risk.get('max_drawdown', 0):.2f}%\n")
            f.write(f"夏普比率: {risk_adj.get('sharpe_ratio', 0):.3f}\n")
            f.write(f"索提诺比率: {risk_adj.get('sortino_ratio', 0):.3f}\n")
            f.write(f"胜率: {risk.get('win_rate', 0):.1f}%\n")
            f.write(f"阿尔法: {benchmark.get('alpha', 0):.2f}%\n")
            f.write(f"贝塔: {benchmark.get('beta', 0):.3f}\n\n")
        
        f.write("="*80 + "\n")
        f.write("专业评估指标说明:\n")
        f.write("- 夏普比率: 风险调整后收益，>1为优秀，>2为卓越\n")
        f.write("- 索提诺比率: 下行风险调整收益，通常大于夏普比率\n")
        f.write("- 最大回撤: 策略面临的最大损失，<10%为良好\n")
        f.write("- 阿尔法: 超越市场的超额收益，>0表示战胜市场\n")
        f.write("- 胜率: 盈利交易占比，>50%为正向\n")
        f.write("="*80 + "\n")
    
    print(f"\n✅ 专业评估完成!")
    print(f"📄 详细报告: {report_file}")
    print(f"📊 文本报告: {text_report_file}")
    
    # 显示关键指标
    if us_report:
        basic = us_report.get('basic_metrics', {})
        risk_adj = us_report.get('risk_adjusted_metrics', {})
        print(f"\n🇺🇸 美股策略表现:")
        print(f"   年化收益: {basic.get('annualized_return', 0):.1f}%")
        print(f"   夏普比率: {risk_adj.get('sharpe_ratio', 0):.2f}")
        print(f"   最大回撤: {us_report.get('risk_metrics', {}).get('max_drawdown', 0):.1f}%")
    
    if cn_report:
        basic = cn_report.get('basic_metrics', {})
        risk_adj = cn_report.get('risk_adjusted_metrics', {})
        print(f"\n🇨🇳 A股策略表现:")
        print(f"   年化收益: {basic.get('annualized_return', 0):.1f}%")
        print(f"   夏普比率: {risk_adj.get('sharpe_ratio', 0):.2f}")
        print(f"   最大回撤: {cn_report.get('risk_metrics', {}).get('max_drawdown', 0):.1f}%")

if __name__ == "__main__":
    main()

