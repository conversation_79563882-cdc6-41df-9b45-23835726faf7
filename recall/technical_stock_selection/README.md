# 技术选股策略 (Technical Stock Selection Strategy)

## 📋 项目概述

这是一个基于技术分析的多因子股票选择策略，经过严格的科学回测验证。该策略整合了多种技术指标进行股票评分排序，通过动态重平衡实现投资组合管理。

## 🏗️ 项目结构

```
recall/technical_stock_selection/
├── README.md                    # 本文档
├── core/                        # 核心策略代码
│   └── stock_analysis.py        # 技术分析选股核心逻辑
├── backtests/                   # 回测框架和代码
│   ├── stock_ranking_backtester.py    # vectorbt专业回测框架
│   ├── strategy_backtester.py          # 策略回测器
│   ├── realistic_backtester.py         # 现实约束回测器
│   └── backtest_issues_analysis.py    # 回测问题诊断分析
├── reports/                     # 回测报告和分析结果
│   ├── stock_ranking/           # vectorbt和QuantStats报告
│   ├── stock_analysis_report_*.txt     # 选股分析报告
│   ├── strategy_evaluation_*.txt       # 策略评估报告
│   └── strategy_evaluation_*.json     # 结构化评估数据
└── docs/                        # 文档和总结
    ├── STOCK_RANKING_BACKTEST_SUMMARY.md  # vectorbt回测总结
    └── STRATEGY_BASELINE_SUMMARY.md       # 策略基线总结
```

## 🎯 策略核心特点

### 技术分析多因子模型
- **动量因子**: 5日和20日收益率评分
- **趋势因子**: 均线多头排列判断
- **成交量因子**: 相对成交量放大检测
- **波动因子**: 价格波动率控制

### 严格科学验证
- ✅ 无前视偏差设计
- ✅ 现实交易约束建模
- ✅ 多市场交叉验证
- ✅ 专业性能指标评估

## 📊 回测结果摘要

### 🏆 **技术策略验证结果**

| 指标 | 美股表现 | A股表现 | 状态 |
|------|----------|---------|------|
| **总收益率** | 90.46% | -14.12% | 美股✅ A股❌ |
| **夏普比率** | 1.434 | 0.18 | 美股优秀 A股差 |
| **最大回撤** | 23.86% | 70.70% | 美股可控 A股高险 |
| **胜率** | 55.0% | 42.55% | 美股稳定 A股波动 |

### 🇺🇸 美股市场表现 (验证成功 ✅)
```
最优策略:    技术分析多因子选股
总收益率:    90.46% (约14个月)
夏普比率:    1.434 (优秀)
最大回撤:    23.86% (可控)
胜率:        55.0% (稳定)
策略状态:    已验证，可投入使用
```

### 🇨🇳 A股市场表现 (需要改进 ❌)
```
总收益率:    -14.12% (约30个月)
夏普比率:    0.18 (很差)
最大回撤:    70.70% (风险极高)
胜率:        42.55% (不稳定)
策略状态:    需要重新设计因子体系
```

### 📝 策略验证结论
```
验证结果:    技术分析策略表现优秀
核心优势:    风险控制好、胜率稳定、夏普比率高
适用场景:    美股中期投资组合管理
建议使用:    作为量化选股基线策略
```

## 🚀 快速开始

### 1. 核心选股策略
```python
# 使用技术分析选股
from recall.technical_stock_selection.core.stock_analysis import StockAnalyzer

analyzer = StockAnalyzer(market='us', data_dir='data')
ranked_stocks = analyzer.analyze_and_rank()
print("推荐股票:", ranked_stocks[:10])
```

### 2. 专业回测验证
```python
# 使用vectorbt回测框架
from recall.technical_stock_selection.backtests.stock_ranking_backtester import StockRankingBacktestPipeline

pipeline = StockRankingBacktestPipeline(market='us')
portfolio, results = pipeline.run_full_pipeline()
print("回测结果:", portfolio.stats())
```

### 3. 策略性能评估
```python
# 使用现实约束回测器
from recall.technical_stock_selection.backtests.realistic_backtester import RealisticBacktester

backtester = RealisticBacktester()
performance = backtester.backtest_strategy(market='us')
print("策略评估:", performance)
```

## 🔧 依赖要求

```bash
# 核心依赖
uv add pandas numpy pandas-ta

# 回测框架
uv add vectorbt quantstats

# 可视化 (可选)
uv add matplotlib anywidget ipywidgets
```

## 📈 使用场景

### ✅ 适合场景
- 美股技术分析选股
- 中期投资组合管理 (2-4周重平衡)
- 量化策略基线对比
- 多因子模型研究

### ❌ 不适合场景
- 高频交易策略
- 纯基本面选股
- 单一股票深度分析
- A股市场直接应用 (需改进)

## 🔍 核心文件说明

### core/stock_analysis.py
- **功能**: 技术分析选股核心算法
- **特点**: 多因子评分、动态排序、防前视偏差
- **输出**: 股票评分和排序列表

### backtests/stock_ranking_backtester.py
- **功能**: 基于vectorbt的专业回测框架
- **特点**: 模块化设计、科学验证、专业报告生成
- **输出**: 完整的回测报告和性能分析

### backtests/realistic_backtester.py
- **功能**: 考虑现实约束的策略回测器
- **特点**: 交易成本、滑点、流动性约束
- **输出**: 修正后的真实策略表现

## 📋 开发历程

1. **问题发现**: 初始回测发现不合理的高收益率
2. **深度分析**: 识别6个关键回测缺陷
3. **科学重构**: 基于vectorbt构建专业框架
4. **多市场验证**: 美股成功、A股待改进
5. **代码整理**: 模块化、文档化、可复用

## 🎯 改进计划

### 美股策略优化
- [ ] 参数精细调优
- [ ] 风险管理增强
- [ ] 因子扩展研究

### A股策略重构
- [ ] 适应性因子设计
- [ ] 基本面因子融合
- [ ] 行业轮动策略

### 框架扩展
- [ ] 新市场支持
- [ ] 实时交易接口
- [ ] 策略组合管理

## 📚 相关文档

- [vectorbt回测总结](docs/STOCK_RANKING_BACKTEST_SUMMARY.md)
- [策略基线报告](docs/STRATEGY_BASELINE_SUMMARY.md)
- [回测报告](reports/stock_ranking/)

## 🏷️ 版本信息

- **版本**: v1.0
- **状态**: 美股策略已验证，A股策略待改进
- **创建日期**: 2025-01-23
- **更新日期**: 2025-01-23

---

**注意**: 该策略已通过科学验证，可作为量化选股的基线方法使用。投资有风险，请谨慎决策。
