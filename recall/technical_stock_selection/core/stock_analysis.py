#!/usr/bin/env python3
"""
股票技术分析和排序工具
基于现有数据对美股和A股进行技术分析排序

功能：
1. 读取本地parquet数据
2. 计算技术指标（RSI、MACD、MA等）
3. 生成股票排序报告
4. 支持美股和A股分析

作者: AI Assistant
日期: 2025-08-23
"""

import pandas as pd
import numpy as np
from pathlib import Path
import warnings
from datetime import datetime, timedelta
import logging

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def calculate_rsi(prices, window=14):
    """计算RSI相对强弱指标"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """计算MACD指标"""
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd = ema_fast - ema_slow
    signal_line = macd.ewm(span=signal).mean()
    histogram = macd - signal_line
    return macd, signal_line, histogram

def calculate_bollinger_bands(prices, window=20, num_std=2):
    """计算布林带"""
    sma = prices.rolling(window=window).mean()
    std = prices.rolling(window=window).std()
    upper = sma + (std * num_std)
    lower = sma - (std * num_std)
    return upper, sma, lower

def analyze_stock(symbol, market='us'):
    """分析单只股票"""
    try:
        data_path = Path(f'data/{market}/{symbol}/1d.parquet')
        if not data_path.exists():
            return None
        
        df = pd.read_parquet(data_path)
        if df.empty or len(df) < 50:  # 需要足够的数据
            return None
        
        # 确保数据按时间排序
        df = df.sort_values('timestamp').reset_index(drop=True)
        
        # 计算技术指标
        df['rsi'] = calculate_rsi(df['close'])
        df['macd'], df['macd_signal'], df['macd_histogram'] = calculate_macd(df['close'])
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = calculate_bollinger_bands(df['close'])
        
        # 计算移动平均线
        df['ma5'] = df['close'].rolling(5).mean()
        df['ma20'] = df['close'].rolling(20).mean()
        df['ma50'] = df['close'].rolling(50).mean()
        
        # 计算价格动量
        df['price_change_5d'] = df['close'].pct_change(5) * 100
        df['price_change_20d'] = df['close'].pct_change(20) * 100
        df['volume_avg_20d'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_avg_20d']
        
        # 获取最新数据
        latest = df.iloc[-1]
        
        # 计算评分
        score = 0
        signals = []
        
        # RSI评分 (30-70为正常范围)
        if 30 <= latest['rsi'] <= 50:
            score += 2
            signals.append("RSI适中偏低")
        elif latest['rsi'] < 30:
            score += 3
            signals.append("RSI超卖")
        elif latest['rsi'] > 70:
            score -= 1
            signals.append("RSI超买")
            
        # MACD评分
        if latest['macd'] > latest['macd_signal']:
            score += 2
            signals.append("MACD金叉")
        else:
            score -= 1
            signals.append("MACD死叉")
            
        # 移动平均线评分
        if latest['close'] > latest['ma5'] > latest['ma20']:
            score += 2
            signals.append("均线多头排列")
        elif latest['close'] < latest['ma5'] < latest['ma20']:
            score -= 1
            signals.append("均线空头排列")
            
        # 价格动量评分
        if latest['price_change_20d'] > 5:
            score += 2
            signals.append("强势上涨")
        elif latest['price_change_20d'] < -10:
            score -= 2
            signals.append("深度调整")
            
        # 成交量评分
        if latest['volume_ratio'] > 1.5:
            score += 1
            signals.append("成交量放大")
        elif latest['volume_ratio'] < 0.5:
            score -= 1
            signals.append("成交量萎缩")
            
        return {
            'symbol': symbol,
            'market': market,
            'price': latest['close'],
            'rsi': latest['rsi'],
            'macd': latest['macd'],
            'price_change_5d': latest['price_change_5d'],
            'price_change_20d': latest['price_change_20d'],
            'volume_ratio': latest['volume_ratio'],
            'score': score,
            'signals': '; '.join(signals),
            'data_points': len(df)
        }
        
    except Exception as e:
        logger.error(f"分析{symbol}失败: {e}")
        return None

def analyze_market(market='us', top_n=20):
    """分析整个市场"""
    logger.info(f"开始分析{market.upper()}市场...")
    
    market_path = Path(f'data/{market}')
    if not market_path.exists():
        logger.error(f"市场数据目录不存在: {market_path}")
        return []
    
    # 获取所有股票代码
    stock_dirs = [d for d in market_path.iterdir() if d.is_dir()]
    symbols = [d.name for d in stock_dirs if (d / '1d.parquet').exists()]
    
    logger.info(f"发现{len(symbols)}只股票")
    
    results = []
    for i, symbol in enumerate(symbols, 1):
        if i % 50 == 0:
            logger.info(f"分析进度: {i}/{len(symbols)}")
            
        result = analyze_stock(symbol, market)
        if result:
            results.append(result)
    
    # 按评分排序
    results.sort(key=lambda x: x['score'], reverse=True)
    
    logger.info(f"成功分析{len(results)}只股票")
    return results[:top_n]

def generate_report(us_results, cn_results):
    """生成分析报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"stock_analysis_report_{timestamp}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("="*80 + "\n")
        f.write("股票技术分析排序报告\n")
        f.write("="*80 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"数据源: 本地Parquet文件\n\n")
        
        # 美股TOP排行
        if us_results:
            f.write("🇺🇸 美股TOP20技术分析排序\n")
            f.write("-"*50 + "\n")
            for i, stock in enumerate(us_results, 1):
                f.write(f"{i:2d}. {stock['symbol']:>6} | 评分:{stock['score']:3d} | "
                       f"价格:${stock['price']:7.2f} | RSI:{stock['rsi']:5.1f} | "
                       f"20日涨幅:{stock['price_change_20d']:6.1f}%\n")
                f.write(f"    信号: {stock['signals']}\n\n")
        
        # A股TOP排行
        if cn_results:
            f.write("🇨🇳 A股TOP20技术分析排序\n")
            f.write("-"*50 + "\n")
            for i, stock in enumerate(cn_results, 1):
                f.write(f"{i:2d}. {stock['symbol']:>10} | 评分:{stock['score']:3d} | "
                       f"价格:¥{stock['price']:7.2f} | RSI:{stock['rsi']:5.1f} | "
                       f"20日涨幅:{stock['price_change_20d']:6.1f}%\n")
                f.write(f"    信号: {stock['signals']}\n\n")
        
        f.write("="*80 + "\n")
        f.write("评分说明:\n")
        f.write("- RSI超卖(+3分), 适中偏低(+2分), 超买(-1分)\n")
        f.write("- MACD金叉(+2分), 死叉(-1分)\n")
        f.write("- 均线多头排列(+2分), 空头排列(-1分)\n")
        f.write("- 强势上涨(+2分), 深度调整(-2分)\n")
        f.write("- 成交量放大(+1分), 萎缩(-1分)\n")
        f.write("="*80 + "\n")
    
    logger.info(f"报告已保存: {report_file}")
    return report_file

def main():
    """主函数"""
    print("🚀 开始股票技术分析...")
    
    # 分析美股
    us_results = analyze_market('us', 20)
    
    # 分析A股
    cn_results = analyze_market('cn', 20)
    
    # 生成报告
    report_file = generate_report(us_results, cn_results)
    
    print(f"\n✅ 分析完成!")
    print(f"📊 美股分析: {len(us_results)}只")
    print(f"📊 A股分析: {len(cn_results)}只")
    print(f"📄 报告文件: {report_file}")
    
    # 显示TOP5
    if us_results:
        print(f"\n🇺🇸 美股TOP5:")
        for i, stock in enumerate(us_results[:5], 1):
            print(f"{i}. {stock['symbol']} (评分:{stock['score']}) - {stock['signals']}")
    
    if cn_results:
        print(f"\n🇨🇳 A股TOP5:")
        for i, stock in enumerate(cn_results[:5], 1):
            print(f"{i}. {stock['symbol']} (评分:{stock['score']}) - {stock['signals']}")

if __name__ == "__main__":
    main()

