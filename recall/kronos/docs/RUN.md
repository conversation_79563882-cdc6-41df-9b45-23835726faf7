# Kronos 金融时序预测模型 - 快速上手指南

## 项目简介

**Kronos** 是首个专为金融K线数据设计的开源基础模型，在来自45个全球交易所的数据上进行预训练。它采用创新的两阶段架构：
- **分层离散化tokenizer**: 将连续的多维K线数据(OHLCV)量化为分层离散tokens
- **大型自回归Transformer**: 在这些tokens上预训练，可统一处理多种量化任务

与通用时序模型不同，Kronos专门针对金融数据的高噪声特性进行了优化设计。

## 环境要求

- Python 3.10+
- CPU或GPU环境均可(GPU推荐)
- 网络连接(首次运行需从Hugging Face下载模型)

## 快速安装

### 1. 克隆项目
```bash
git clone https://github.com/shiyu-coder/Kronos.git
cd Kronos
```

### 2. 安装依赖
```bash
# 推荐使用uv(更快)
uv pip install -r requirements.txt

# 或使用传统pip
pip install -r requirements.txt

# 如果遇到safetensors错误，额外安装：
pip install safetensors
```

## 快速开始

### 基础预测示例

```python
from model import Kronos, KronosTokenizer, KronosPredictor
import pandas as pd

# 1. 加载预训练模型和tokenizer
tokenizer = KronosTokenizer.from_pretrained("NeoQuasar/Kronos-Tokenizer-base")
model = Kronos.from_pretrained("NeoQuasar/Kronos-small")

# 2. 初始化预测器
predictor = KronosPredictor(model, tokenizer, device="cpu", max_context=512)

# 3. 准备数据
df = pd.read_csv("./examples/data/XSHG_5min_600977.csv")
df['timestamps'] = pd.to_datetime(df['timestamps'])

# 定义历史数据长度和预测长度
lookback = 400  # 历史400个时间点
pred_len = 120  # 预测120个时间点

# 准备输入数据
x_df = df.loc[:lookback-1, ['open', 'high', 'low', 'close', 'volume', 'amount']]
x_timestamp = df.loc[:lookback-1, 'timestamps']
y_timestamp = df.loc[lookback:lookback+pred_len-1, 'timestamps']

# 4. 执行预测
pred_df = predictor.predict(
    df=x_df,
    x_timestamp=x_timestamp,
    y_timestamp=y_timestamp,
    pred_len=pred_len,
    T=1.0,          # 温度参数，控制随机性
    top_p=0.9,      # 核采样概率
    sample_count=1  # 预测路径数量
)

print("预测结果:")
print(pred_df.head())
```

### 运行示例程序

```bash
cd examples
python prediction_example.py
```

成功运行后会：
- 下载预训练模型(首次运行约100MB)
- 对中国A股数据进行预测
- 生成预测结果图表(prediction_result.png)
- 输出预测的OHLCV数据

## 核心技术架构

### 两阶段训练框架

**Kronos** 采用创新的两阶段架构，专门为金融K线数据的"语言"设计：

#### 1. 分层离散化Tokenizer (KronosTokenizer)
- **Binary Spherical Quantization (BSQ)**: 将连续的OHLCV数据量化为分层离散tokens
- **分层Token结构**: 
  - `s1_bits`: 预Token bits，捕捉主要价格模式
  - `s2_bits`: 后Token bits，捕捉精细价格变化
- **编码器-解码器架构**: Transformer编码器+BSQuantizer+Transformer解码器

#### 2. 自回归预测模型 (Kronos)
- **分层嵌入**: HierarchicalEmbedding融合s1和s2 tokens
- **时间嵌入**: TemporalEmbedding处理时间周期特征
- **依赖感知层**: DependencyAwareLayer建模s1和s2 tokens间的条件依赖
- **双重预测头**: DualHead分别预测s1和s2 tokens

### 关键创新点

1. **专门化设计**: 不同于通用时序模型，专门针对金融数据高噪声特性优化
2. **分层量化**: 将连续价格空间映射到离散token空间，更好捕捉价格模式
3. **条件生成**: s2 token生成条件依赖于s1 token，保证预测一致性
4. **多尺度表示**: 同时建模粗粒度趋势(s1)和细粒度波动(s2)

## 可用模型规格与性能对比

| 模型 | 参数量 | 文件大小 | 上下文长度 | 层数 | 推理速度 | 适用场景 |
|------|--------|----------|------------|------|----------|----------|
| Kronos-mini | 4.1M | ~16MB | 2048 | 较少 | 最快 | 快速原型验证 |
| Kronos-small | 24.7M | ~100MB | 512 | 中等 | 9.95it/s | **推荐日常使用** |
| Kronos-base | 102.3M | ~400MB | 512 | 更多 | 5.49it/s | 复杂场景预测 |
| Kronos-large | 499.2M | ~2GB | 512 | 最多 | 不开源 | 工业级应用 |

### ⚠️ 重要发现：模型大小 ≠ 性能更好

基于实际测试，我们发现了一个**反直觉的现象**：

```
实测结果 (A股5分钟数据):
├── Kronos-small: MAE=0.1217, RMSE=0.1507, MAPE=1.118%  ✅ 更优
└── Kronos-base:  MAE=0.1394, RMSE=0.1722, MAPE=1.280%  

趋势捕捉能力:
├── Kronos-small: 趋势相关性=0.1830  ✅ 更优  
└── Kronos-base:  趋势相关性=0.1527
```

### 为什么Small比Base表现更好？

#### 1. **过拟合风险**
- Base模型参数量是Small的4倍多，在有限数据上容易过拟合
- 金融数据噪声大，过于复杂的模型可能学到噪声而非信号

#### 2. **模型容量匹配**
- Small模型的24.7M参数对于K线预测任务可能是最优容量
- Base模型的复杂度可能超出了当前任务的需求

#### 3. **训练数据分布**
- 预训练数据分布可能与具体的A股5分钟数据存在域偏移
- Small模型泛化能力在某些数据域上可能更好

#### 4. **计算效率权衡**
- Small模型推理速度快近一倍(9.95 vs 5.49 it/s)
- 在实时交易场景中，速度优势显著

### 模型选择建议

```python
# 推荐配置
if 任务类型 == "日常预测" or 数据量 < 1000万条:
    model = "Kronos-small"  # 性能最佳，速度快
elif 任务类型 == "研究实验" or 需要最大上下文:
    model = "Kronos-mini"   # 上下文2048，适合长序列
elif 任务类型 == "复杂场景" and 数据量 >= 1000万条:
    model = "Kronos-base"   # 大容量，适合复杂模式
```

## 针对不同市场的应用

### A股市场应用
```python
# A股5分钟数据示例
df = pd.read_csv("a_share_5min_data.csv")  # 格式: timestamps,open,high,low,close,volume,amount
# 确保时间戳格式正确
df['timestamps'] = pd.to_datetime(df['timestamps'])

# 使用Kronos-small进行预测
predictor = KronosPredictor(model, tokenizer, device="cpu", max_context=512)
pred_df = predictor.predict(df=historical_data, ...)
```

### 美股市场应用
```python
# 美股分钟数据示例
df = pd.read_csv("us_stock_1min_data.csv")  # 格式: timestamps,open,high,low,close,volume,amount
df['timestamps'] = pd.to_datetime(df['timestamps'])

# 针对美股可能需要调整参数
pred_df = predictor.predict(
    df=historical_data,
    pred_len=60,     # 预测1小时(60分钟)
    T=0.8,          # 降低温度获得更稳定预测
    sample_count=5  # 多路径预测求平均
)
```

### 不同周期的预测
```python
# 1分钟级别短期预测
pred_len = 30  # 预测30分钟

# 5分钟级别中期预测  
pred_len = 120  # 预测10小时

# 日线级别长期预测
pred_len = 30   # 预测30天
```

## 数据格式要求

输入数据必须包含以下列：
- **必需**: `['open', 'high', 'low', 'close']` - OHLC价格数据
- **可选**: `['volume', 'amount']` - 成交量和金额(如无会自动填0)
- **时间戳**: pandas时间戳格式

```csv
timestamps,open,high,low,close,volume,amount
2024-06-18 11:15:00,11.27,11.28,11.26,11.27,379.0,427161.0
2024-06-18 11:20:00,11.27,11.28,11.27,11.27,277.0,312192.0
```

## 模型微调(针对特定市场)

### 1. 准备Qlib环境
```bash
pip install pyqlib
```

### 2. 配置微调参数
编辑 `finetune/config.py`:
```python
# 数据路径配置
qlib_data_path = "your_qlib_data_path"
dataset_path = "your_dataset_path" 
save_path = "your_model_save_path"

# 市场配置
instrument = "csi300"  # A股中证300
# instrument = "sp500"   # 美股标普500

# 训练参数
epochs = 50
batch_size = 16
```

### 3. 数据预处理
```bash
python finetune/qlib_data_preprocess.py
```

### 4. 微调tokenizer
```bash
torchrun --standalone --nproc_per_node=2 finetune/train_tokenizer.py
```

### 5. 微调预测器
```bash
torchrun --standalone --nproc_per_node=2 finetune/train_predictor.py
```

### 6. 回测评估
```bash
python finetune/qlib_test.py --device cpu
```

## 预测参数调优

### 温度参数(T)
- `T=0.5`: 保守预测，输出更稳定
- `T=1.0`: 平衡预测(默认)
- `T=1.5`: 激进预测，波动性更大

### 核采样(top_p)
- `top_p=0.7`: 保守，选择概率高的tokens
- `top_p=0.9`: 平衡(推荐)
- `top_p=0.99`: 激进，允许更多随机性

### 多路径预测(sample_count)
- `sample_count=1`: 单路径预测，速度快
- `sample_count=5`: 多路径平均，更稳定
- `sample_count=10`: 高精度预测，计算量大

## 性能优化建议

### GPU加速
```python
# 如有GPU，修改device参数
predictor = KronosPredictor(model, tokenizer, device="cuda:0", max_context=512)
```

### 批量预测
```python
# 对多个股票批量预测
for stock_code in stock_list:
    stock_data = load_stock_data(stock_code)
    pred_result = predictor.predict(...)
    save_prediction(stock_code, pred_result)
```

### 上下文长度优化
```python
# Kronos-small/base最大上下文512
# 输入数据长度建议不超过512以获得最佳性能
lookback = min(400, len(historical_data))
```

## 常见问题解决

### 1. CUDA相关错误
```python
# 解决方案：使用CPU
predictor = KronosPredictor(model, tokenizer, device="cpu")
```

### 2. 内存不足
```python
# 减少批量大小或预测长度
pred_len = 60  # 减少预测长度
sample_count = 1  # 减少采样路径
```

### 3. 网络连接问题
```bash
# 设置代理或离线下载模型文件
export HF_ENDPOINT=https://hf-mirror.com
```

## 实际应用建议

### 量化交易集成
1. **信号生成**: 使用预测价格变化生成买卖信号
2. **风险控制**: 结合VaR等风险指标
3. **组合优化**: 多资产预测结果组合优化

### 预测精度提升
1. **数据预处理**: 异常值处理、缺失值填充
2. **特征工程**: 技术指标、市场情绪等
3. **模型集成**: 多个预测结果加权平均

### 实时预测系统
1. **流式处理**: 实时获取市场数据
2. **增量预测**: 滑动窗口更新预测
3. **监控报警**: 预测异常检测和报警

## 深度技术分析与实验结论

### 实验方法论

基于A股数据(600977, 5分钟K线)的对比实验：
- **历史窗口**: 400个时间点 (~33小时交易数据)
- **预测长度**: 120个时间点 (~10小时)
- **评估指标**: MAE, RMSE, MAPE, 趋势相关性
- **实验参数**: T=1.0, top_p=0.9, sample_count=1

### 核心技术洞察

#### 1. 分层Token设计的优势
```python
# Kronos的分层token结构
s1_token: 主要价格模式 (粗粒度)
s2_token: 精细价格变化 (细粒度) | 条件依赖于s1_token

# 传统方法
continuous_values: 直接预测连续值 -> 容易产生不现实的价格跳跃
```

#### 2. 金融数据的"语言"特性
- **词汇表**: 价格模式被编码为有限的token词汇表
- **语法**: s1和s2 tokens的条件依赖关系类似语法规则
- **语义**: Token序列表达价格运动的"语义"含义

#### 3. Binary Spherical Quantization的作用
```python
连续OHLCV数据 -> BSQuantizer -> 分层离散tokens -> LLM建模
```
- 将连续价格空间映射到球面上的二进制码点
- 减少量化误差，保持价格关系的几何结构
- 使传统NLP技术可应用于金融时序

#### 4. 模型缩放规律在金融领域的特殊性

与NLP领域不同，金融预测存在**最优模型容量**现象：

```
NLP领域: 更大模型 → 更好性能 (缩放定律)
金融预测: 存在最优点，过大模型 → 过拟合噪声
```

**原因分析**:
1. **信噪比限制**: 金融数据信噪比低，复杂模型易学习噪声
2. **市场效率**: 可预测模式有限，过度复杂化无益
3. **数据稀缺**: 相比文本，金融训练数据相对稀缺

### 实际应用建议

#### 1. 数据预处理最佳实践
```python
# 异常值处理
data = np.clip(data, -5, 5)  # 限制在±5个标准差内

# 归一化策略
data = (data - mean) / (std + 1e-5)  # 加小常数避免除零

# 缺失值处理
data = data.fillna(method='ffill')  # 前向填充更符合金融逻辑
```

#### 2. 超参数调优指南
```python
# 保守策略 (稳定收益)
T = 0.5-0.8     # 降低随机性
top_p = 0.7-0.8 # 选择高概率tokens
sample_count = 5-10  # 多路径平均

# 激进策略 (高风险高收益)  
T = 1.2-1.5     # 增加探索性
top_p = 0.95-0.99   # 允许更多可能性
sample_count = 1-3  # 快速决策
```

#### 3. 实时交易集成架构
```python
# 流式预测管道
historical_data -> feature_engineering -> Kronos -> signal_generation -> risk_management -> execution
```

### 技术路线图与未来改进

#### 短期改进 (1-3个月)
1. **多模态融合**: 集成新闻情感、技术指标
2. **自适应温度**: 根据市场波动率动态调整T参数
3. **增量学习**: 支持在线学习新的市场模式

#### 中期发展 (3-12个月)
1. **跨市场迁移**: 不同交易所间的域适应
2. **多周期建模**: 同时建模分钟/小时/日线多个时间尺度
3. **不确定性量化**: 提供预测置信区间

#### 长期展望 (1年以上)
1. **因果推理**: 识别价格变动的因果机制
2. **对抗训练**: 提高模型对市场异常的鲁棒性
3. **联邦学习**: 多机构间协作训练而不泄露数据

## 免责声明

本项目仅供学习研究使用，不构成投资建议。量化交易具有风险，实际使用前请充分测试和风险评估。作者不对使用本项目产生的任何损失承担责任。

**特别提醒**: 
- 本文档中的实验结果基于特定数据集，不保证在其他市场条件下的表现
- 金融市场具有时变性，过去的预测性能不代表未来表现
- 建议在实盘前进行充分的回测和风险评估

## 技术支持

- **论文**: [Kronos: A Foundation Model for the Language of Financial Markets](https://arxiv.org/abs/2508.02739)
- **arXiv ID**: 2508.02739 (cs.AI, cs.LG, q-fin.ST)
- **在线演示**: https://shiyu-coder.github.io/Kronos-demo/
- **GitHub**: https://github.com/shiyu-coder/Kronos
- **模型下载**: https://huggingface.co/NeoQuasar
- **实验复现**: 运行 `python examples/compare_models.py` 查看详细对比

---

🎯 **快速验证**: 运行 `cd examples && python prediction_example.py` 即可体验完整预测流程！

🔬 **深度分析**: 运行 `cd examples && python compare_models.py` 生成详细的模型对比报告！

