#!/usr/bin/env python3
"""
Kronos快速预测脚本
便捷的命令行工具，支持单只或多只股票预测

用法:
  python predict.py SPY                    # 预测单只股票
  python predict.py SPY QQQ AAPL          # 预测多只股票
  python predict.py --batch                # 预测热门股票池
  python predict.py --list                 # 列出所有可用股票

作者：基于core模块整合
日期：2025-08-23
"""

import sys
import argparse
from pathlib import Path

# 添加core路径
sys.path.append("core")
from stock_predictor import StockPredictor
from batch_predictor import BatchStockPredictor

def predict_single_stock(symbol: str):
    """预测单只股票"""
    print(f"🎯 开始预测 {symbol}")
    print("-" * 40)
    
    predictor = StockPredictor()
    success = predictor.predict_stock(symbol)
    
    if success:
        print(f"✅ {symbol} 预测成功！")
        print(f"📊 图表已保存到 output/charts/")
        print(f"📁 报告已保存到 output/reports/")
    else:
        print(f"❌ {symbol} 预测失败")

def predict_multiple_stocks(symbols: list):
    """预测多只股票"""
    print(f"🚀 开始批量预测 {len(symbols)} 只股票")
    print(f"股票池: {', '.join(symbols)}")
    print("-" * 60)
    
    batch_predictor = BatchStockPredictor()
    success = batch_predictor.run_batch_prediction(symbols)
    
    if success:
        print(f"✅ 批量预测成功！")
        print(f"📊 所有图表已保存到 output/charts/")
        print(f"📁 排序报告已保存到 output/reports/")
    else:
        print(f"❌ 批量预测失败")

def list_available_stocks():
    """列出所有可用股票"""
    print("📋 扫描可用股票数据...")
    
    batch_predictor = BatchStockPredictor()
    stocks = batch_predictor.get_available_stocks()
    
    print(f"\n发现 {len(stocks)} 只可用股票:")
    print("-" * 40)
    
    # 按列显示
    cols = 8
    for i in range(0, len(stocks), cols):
        row = stocks[i:i+cols]
        print("  ".join(f"{stock:>6}" for stock in row))
    
    print(f"\n💡 使用方法: python predict.py [股票代码]")
    print(f"例如: python predict.py {stocks[0] if stocks else 'AAPL'}")

def predict_hot_stocks():
    """预测热门股票池"""
    print("🔥 预测热门股票池")
    print("-" * 40)
    
    # 热门股票池（ETF + 科技龙头）
    hot_stocks = [
        'SPY', 'QQQ', 'IWM',  # 主要ETF
        'AAPL', 'MSFT', 'GOOGL', 'AMZN',  # 科技巨头
        'NVDA', 'TSLA', 'META'  # AI/电动车
    ]
    
    # 检查数据可用性
    batch_predictor = BatchStockPredictor()
    available_stocks = batch_predictor.get_available_stocks()
    available_hot_stocks = [s for s in hot_stocks if s in available_stocks]
    
    print(f"热门股票池: {', '.join(available_hot_stocks)}")
    
    if available_hot_stocks:
        success = batch_predictor.run_batch_prediction(available_hot_stocks)
        if success:
            print(f"✅ 热门股票预测成功！")
        else:
            print(f"❌ 热门股票预测失败")
    else:
        print("❌ 没有找到热门股票数据")

def main():
    """主程序"""
    parser = argparse.ArgumentParser(description='Kronos股票预测工具')
    parser.add_argument('symbols', nargs='*', help='股票代码 (如: SPY QQQ AAPL)')
    parser.add_argument('--list', '-l', action='store_true', help='列出所有可用股票')
    parser.add_argument('--batch', '-b', action='store_true', help='预测热门股票池')
    
    args = parser.parse_args()
    
    print("📈 Kronos股票预测工具")
    print("=" * 50)
    
    try:
        if args.list:
            # 列出可用股票
            list_available_stocks()
            
        elif args.batch:
            # 预测热门股票池
            predict_hot_stocks()
            
        elif args.symbols:
            # 预测指定股票
            if len(args.symbols) == 1:
                predict_single_stock(args.symbols[0])
            else:
                predict_multiple_stocks(args.symbols)
                
        else:
            # 显示帮助
            print("用法示例:")
            print("  python predict.py SPY              # 预测SPY")
            print("  python predict.py SPY QQQ AAPL     # 预测多只股票")
            print("  python predict.py --batch          # 预测热门股票池")
            print("  python predict.py --list           # 列出所有可用股票")
            print("\n💡 所有结果保存在 output/ 目录下")
            
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()