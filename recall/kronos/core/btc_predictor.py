#!/usr/bin/env python3
"""
BTC预测复现系统
完全复现Kronos官方BTC预测效果

参考官方规格：
- 模型: Kronos-small (25M parameters)
- 数据: 最近360小时(15天) BTC/USD 1小时K线
- 预测: 未来24小时
- 采样: Monte Carlo N=30路径
- 指标: 上涨概率、波动性放大

作者：基于Kronos官方规格
日期：2025-08-23
"""

import os
import pandas as pd
import numpy as np
import sys
from datetime import datetime, timedelta
import logging
import warnings
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import ccxt
from typing import Optional, Dict, Tuple
import pytz

warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append("../")
from model import Kronos, KronosTokenizer, KronosPredictor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BTCForecastReplica:
    """BTC预测复现系统"""
    
    def __init__(self, device: str = "cpu"):
        """
        初始化BTC预测系统
        
        参数完全按照官方规格：
        - 360小时历史数据
        - 24小时预测窗口
        - 30路径Monte Carlo采样
        """
        self.lookback_hours = 360    # 官方规格：360小时(~15天)
        self.prediction_hours = 24   # 官方规格：24小时预测
        self.monte_carlo_paths = 30  # 官方规格：N=30路径
        self.device = device
        
        # 预测器组件（延迟加载）
        self.tokenizer = None
        self.model = None
        self.predictor = None
        
        logger.info("初始化BTC预测复现系统（官方规格）")
        logger.info(f"历史数据：{self.lookback_hours}小时，预测：{self.prediction_hours}小时，采样：{self.monte_carlo_paths}路径")

    def download_btc_data(self) -> Optional[pd.DataFrame]:
        """
        从Binance下载BTC/USDT数据（使用ccxt）
        """
        try:
            logger.info("正在从Binance下载BTC/USDT数据...")
            
            # 初始化Binance交易所（参考官方博客配置）
            exchange = ccxt.binance({
                'enableRateLimit': True,  # 必须开启！防止被交易所封IP
                'timeout': 15000,         # 超时设为15秒
                'sandbox': False,         # 使用实盘数据
            })
            
            # 计算需要的数据量（360小时 + 一些额外的缓冲）
            total_hours_needed = self.lookback_hours + 50  # 多获取50小时作为缓冲
            
            logger.info(f"准备获取{total_hours_needed}小时的BTC/USDT 1小时K线数据")
            
            # 分批获取数据（Binance API限制每次最多1000条）
            max_limit = 1000
            all_ohlcvs = []
            
            # 从当前时间开始往前获取
            end_time = int(datetime.now().timestamp() * 1000)  # 转换为毫秒时间戳
            
            while len(all_ohlcvs) < total_hours_needed:
                # 计算这次要获取的数量
                current_limit = min(max_limit, total_hours_needed - len(all_ohlcvs))
                
                try:
                    # 获取OHLCV数据
                    ohlcvs = exchange.fetch_ohlcv(
                        symbol='BTC/USDT',
                        timeframe='1h',
                        limit=current_limit,
                        params={'endTime': end_time}
                    )
                    
                    if not ohlcvs:
                        logger.warning("未获取到更多数据，停止下载")
                        break
                    
                    # 添加到总数据中（在前面插入，因为我们是从最新往前获取）
                    all_ohlcvs = ohlcvs + all_ohlcvs
                    
                    # 更新结束时间为这批数据的最早时间
                    end_time = ohlcvs[0][0] - 1  # 减1毫秒避免重复
                    
                    logger.info(f"已获取{len(all_ohlcvs)}小时数据，最新时间: {datetime.fromtimestamp(ohlcvs[-1][0]/1000)}")
                    
                except Exception as e:
                    logger.error(f"获取数据时出错: {str(e)}")
                    break
            
            if not all_ohlcvs:
                logger.error("未能获取到任何BTC数据")
                return None
            
            # 转换为DataFrame
            df_kronos = pd.DataFrame(
                all_ohlcvs, 
                columns=["timestamp_ms", "open", "high", "low", "close", "volume"]
            )
            
            # 转换时间戳格式（从毫秒转换为datetime）
            df_kronos['timestamp'] = pd.to_datetime(df_kronos['timestamp_ms'], unit='ms', utc=True)
            df_kronos['timestamp'] = df_kronos['timestamp'].dt.tz_localize(None)  # 移除时区信息
            
            # 计算amount列（与之前格式保持一致）
            df_kronos['amount'] = df_kronos['volume'] * df_kronos['close']
            
            # 删除临时的毫秒时间戳列
            df_kronos = df_kronos.drop('timestamp_ms', axis=1)
            
            # 确保数据类型正确
            for col in ['open', 'high', 'low', 'close', 'volume', 'amount']:
                df_kronos[col] = df_kronos[col].astype(float)
            
            # 按时间排序并重置索引
            df_kronos = df_kronos.sort_values('timestamp').reset_index(drop=True)
            
            # 移除重复数据
            df_kronos = df_kronos.drop_duplicates(subset=['timestamp']).reset_index(drop=True)
            
            logger.info(f"成功获取{len(df_kronos)}条BTC/USDT 1小时数据")
            logger.info(f"数据时间范围: {df_kronos['timestamp'].min()} 到 {df_kronos['timestamp'].max()}")
            logger.info(f"最新价格: ${df_kronos['close'].iloc[-1]:.2f}")
            
            return df_kronos
            
        except Exception as e:
            logger.error(f"下载BTC数据失败: {str(e)}")
            return None

    def _load_model(self):
        """加载Kronos-small模型（官方规格）"""
        if self.predictor is None:
            logger.info("加载Kronos-small模型（25M参数）...")
            self.tokenizer = KronosTokenizer.from_pretrained("NeoQuasar/Kronos-Tokenizer-base")
            self.model = Kronos.from_pretrained("NeoQuasar/Kronos-small")  # 官方使用small
            self.predictor = KronosPredictor(
                model=self.model,
                tokenizer=self.tokenizer,
                device=self.device,
                max_context=512
            )

    def monte_carlo_forecast(self, df: pd.DataFrame) -> Optional[Dict]:
        """
        使用Monte Carlo方法生成概率性预测
        完全按照官方方法论
        """
        try:
            self._load_model()
            
            # 检查数据量是否足够
            if len(df) < self.lookback_hours + 10:
                logger.error(f"数据不足，需要至少{self.lookback_hours + 10}小时，当前{len(df)}小时")
                return None
            
            # 提取最近360小时数据作为上下文
            end_idx = len(df) - 1
            start_idx = max(0, end_idx - self.lookback_hours + 1)
            context_data = df.iloc[start_idx:end_idx+1].copy()
            
            current_price = context_data['close'].iloc[-1]
            current_time = context_data['timestamp'].iloc[-1]
            
            logger.info(f"使用{len(context_data)}小时历史数据作为上下文")
            logger.info(f"当前BTC价格: ${current_price:.2f}")
            
            # 准备模型输入
            x_df = context_data[['open', 'high', 'low', 'close', 'volume', 'amount']]
            x_timestamp = context_data['timestamp']
            
            # 生成未来24小时时间戳
            future_timestamps = []
            for i in range(1, self.prediction_hours + 1):
                future_time = current_time + timedelta(hours=i)
                future_timestamps.append(future_time)
            y_timestamp = pd.Series(future_timestamps)
            
            logger.info(f"开始Monte Carlo预测（{self.monte_carlo_paths}路径）...")
            
            # 执行Monte Carlo采样（使用GitHub推荐参数：T=0.6, top_p=0.9, sample_count=30）
            pred_df = self.predictor.predict(
                df=x_df,
                x_timestamp=x_timestamp,
                y_timestamp=y_timestamp,
                pred_len=self.prediction_hours,
                T=0.6,          # GitHub推荐参数
                top_p=0.9,      # GitHub推荐参数
                sample_count=self.monte_carlo_paths,  # N=30路径
                verbose=True
            )
            
            # 生成多个独立预测路径以创建真实的概率分布
            logger.info("生成多路径Monte Carlo预测...")
            all_predictions = []
            
            # 生成20个独立预测（每次single sample以获得不同的趋势）
            for i in range(20):
                # 使用单个样本以获得更多样化的预测路径
                single_pred = self.predictor.predict(
                    df=x_df,
                    x_timestamp=x_timestamp,
                    y_timestamp=y_timestamp,
                    pred_len=self.prediction_hours,
                    T=0.6,          # GitHub推荐参数  
                    top_p=0.9,      # GitHub推荐参数
                    sample_count=1,  # 单个样本以获得多样性
                    verbose=False
                )
                all_predictions.append(single_pred['close'].values)
                
                if (i + 1) % 5 == 0:
                    logger.info(f"已生成 {i + 1}/20 预测路径")
            
            # 添加主要预测（高采样）作为基准
            all_predictions.append(pred_df['close'].values)
            all_predictions = np.array(all_predictions)  # shape: (21, 24)
            
            # 计算统计量
            mean_forecast = np.mean(all_predictions, axis=0)
            min_forecast = np.min(all_predictions, axis=0)
            max_forecast = np.max(all_predictions, axis=0)
            std_forecast = np.std(all_predictions, axis=0)
            
            # 计算官方指标
            # 1. 上涨概率（24小时后价格高于当前价格的概率）
            final_prices = all_predictions[:, -1]  # 24小时后的价格
            upside_probability = np.mean(final_prices > current_price)
            
            # 2. 波动性放大（预测波动性相对历史波动性）
            # 计算历史波动性（最近24小时）
            recent_prices = context_data['close'].tail(24).values
            historical_volatility = np.std(np.diff(recent_prices) / recent_prices[:-1])
            
            # 计算预测波动性
            predicted_returns = []
            for path in all_predictions:
                returns = np.diff(path) / path[:-1]
                predicted_returns.extend(returns)
            predicted_volatility = np.std(predicted_returns)
            
            volatility_amplification = max(0, (predicted_volatility - historical_volatility) / historical_volatility)
            
            result = {
                'success': True,
                'current_price': current_price,
                'current_time': current_time,
                'context_data': context_data,
                'future_timestamps': future_timestamps,
                
                # 预测结果
                'mean_forecast': mean_forecast,
                'min_forecast': min_forecast,
                'max_forecast': max_forecast,
                'std_forecast': std_forecast,
                'high_sample_pred': pred_df,  # 高采样预测结果
                'all_predictions': all_predictions,
                
                # 官方指标
                'upside_probability': upside_probability,
                'volatility_amplification': volatility_amplification,
                'historical_volatility': historical_volatility,
                'predicted_volatility': predicted_volatility,
                
                # 统计信息
                'prediction_range': [np.min(final_prices), np.max(final_prices)],
                'mean_final_price': np.mean(final_prices),
                'std_final_price': np.std(final_prices)
            }
            
            logger.info(f"Monte Carlo预测完成")
            logger.info(f"上涨概率: {upside_probability*100:.1f}%")
            logger.info(f"波动性放大: {volatility_amplification*100:.1f}%")
            logger.info(f"预测价格范围: ${np.min(final_prices):.0f} - ${np.max(final_prices):.0f}")
            
            return result
            
        except Exception as e:
            logger.error(f"Monte Carlo预测失败: {str(e)}")
            return None

    def create_official_style_chart(self, forecast_result: Dict) -> str:
        """
        创建完全复现官方风格的BTC预测图表
        """
        if not forecast_result.get('success', False):
            return None
        
        # 设置图表样式（匹配官方）
        plt.style.use('default')
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 10), height_ratios=[3, 1])
        
        # 准备数据
        context_data = forecast_result['context_data']
        future_timestamps = pd.to_datetime(forecast_result['future_timestamps'])
        current_time = forecast_result['current_time']
        current_price = forecast_result['current_price']
        
        mean_forecast = forecast_result['mean_forecast']
        min_forecast = forecast_result['min_forecast']
        max_forecast = forecast_result['max_forecast']
        
        # === 上部分：价格预测图 ===
        # 显示更多历史数据用于视觉效果
        hist_display_hours = min(200, len(context_data))  # 显示最近200小时
        hist_data = context_data.tail(hist_display_hours)
        
        # 历史价格（蓝色线）
        ax1.plot(hist_data['timestamp'], hist_data['close'], 
                'b-', linewidth=1.5, label='Historical Price', alpha=0.8)
        
        # 预测范围（浅橙色阴影区域）
        ax1.fill_between(future_timestamps, min_forecast, max_forecast,
                        color='orange', alpha=0.15, label='Forecast Range (Min-Max)')
        
        # 连接线（历史最后一点到预测第一点）
        connection_times = [current_time, future_timestamps[0]]
        connection_prices = [current_price, mean_forecast[0]]
        ax1.plot(connection_times, connection_prices, 'orange', linewidth=2, alpha=0.8)
        
        # 平均预测（橙色线）
        ax1.plot(future_timestamps, mean_forecast, 
                'orange', linewidth=3, label='Mean Forecast')
        
        # 当前时间分割线（红色虚线）
        ax1.axvline(x=current_time, color='red', linestyle='--', 
                   linewidth=2, alpha=0.8, label='Current Time')
        
        # 设置价格图属性
        ax1.set_title('BTCUSDT Probabilistic Price & Volume Forecast (Next 24 Hours)', 
                     fontsize=16, fontweight='bold')
        ax1.set_ylabel('Price (USDT)', fontsize=12)
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)
        
        # 设置Y轴范围（确保预测范围完全可见）
        y_min = min(hist_data['close'].min(), min_forecast.min()) * 0.98
        y_max = max(hist_data['close'].max(), max_forecast.max()) * 1.02
        ax1.set_ylim(y_min, y_max)
        
        # === 下部分：成交量图 ===
        # 历史成交量（细柱状图，匹配官方样式）
        ax2.bar(hist_data['timestamp'], hist_data['volume'],
               color='skyblue', label='Historical Volume', width=0.03)
        
        # 预测成交量（细柱状图，匹配官方样式）
        pred_volume = forecast_result['high_sample_pred']['volume'].values
        ax2.bar(future_timestamps, pred_volume,
               color='sandybrown', label='Mean Forecasted Volume', width=0.03)
        
        # 当前时间分割线
        ax2.axvline(x=current_time, color='red', linestyle='--', 
                   linewidth=2, alpha=0.8)
        
        # 设置成交量图属性
        ax2.set_ylabel('Volume', fontsize=12)
        ax2.set_xlabel('Time (UTC)', fontsize=12)
        ax2.legend(loc='upper left')
        ax2.grid(True, which='both', linestyle='--', linewidth=0.5)
        
        # X轴标签倾斜显示（匹配官方样式）
        for ax in [ax1, ax2]:
            ax.tick_params(axis='x', rotation=30)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'BTC_official_replica_forecast_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        logger.info(f"官方风格BTC预测图已生成: {filename}")
        return filename

    def generate_official_report(self, forecast_result: Dict) -> str:
        """
        生成官方风格的预测报告
        """
        if not forecast_result.get('success', False):
            return "预测失败，无法生成报告"
        
        # 获取当前UTC时间（匹配官方）
        current_utc = datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")
        
        # 格式化指标
        upside_prob = forecast_result['upside_probability'] * 100
        volatility_amp = forecast_result['volatility_amplification'] * 100
        current_price = forecast_result['current_price']
        mean_final_price = forecast_result['mean_final_price']
        
        report = []
        report.append("=" * 80)
        report.append("Kronos: Live Probabilistic Forecast")
        report.append("")
        report.append('A Replica of "Kronos: A Foundation Model for the Language of Financial Markets"')
        report.append("=" * 80)
        report.append("")
        report.append("Live BTC/USDT Forecast Dashboard")
        report.append(f"Last Updated (UTC): {current_utc}")
        report.append("Data Source: Binance | Interval: 1-Hour")
        report.append("")
        
        # 核心指标（匹配官方格式）
        report.append("Upside Probability (Next 24h)")
        report.append("")
        report.append(f"{upside_prob:.1f}%")
        report.append("The model's confidence that the price in 24 hours will be higher than the last known price.")
        report.append("")
        
        report.append("Volatility Amplification (Next 24h)")
        report.append("")
        report.append(f"{volatility_amp:.1f}%")
        report.append("The probability that predicted volatility over the next 24h will exceed recent historical volatility.")
        report.append("")
        
        # 24小时概率性预测部分
        report.append("24-Hour Probabilistic Forecast")
        report.append("")
        report.append("The chart shows the historical price (blue) and the probabilistic forecast (orange).")
        report.append("The orange line is the mean of multiple Monte Carlo simulations, and the shaded")
        report.append("area represents the full range of predicted outcomes, indicating forecast uncertainty.")
        report.append("")
        
        # 价格预测摘要
        price_range = forecast_result['prediction_range']
        report.append("Price Forecast Summary:")
        report.append(f"  Current Price: ${current_price:.2f}")
        report.append(f"  24h Mean Forecast: ${mean_final_price:.2f}")
        report.append(f"  24h Range: ${price_range[0]:.0f} - ${price_range[1]:.0f}")
        report.append(f"  Expected Change: {((mean_final_price - current_price) / current_price * 100):+.2f}%")
        report.append("")
        
        # 方法论（匹配官方）
        report.append("Methodology Overview")
        report.append("")
        report.append("This demo showcases the forecasting results of Kronos, a foundation model")
        report.append("pre-trained on the 'language' of financial markets. The predictions are")
        report.append("generated using the following process:")
        report.append("")
        report.append(f"  1. Model: The Kronos-small (25M parameters) model is used to autoregressively")
        report.append(f"     predict future K-line data.")
        report.append(f"  2. Data Context: The model uses the last {self.lookback_hours} hours (~15 days) of BTC/USDT")
        report.append(f"     1h K-line data from Binance as context for each new prediction.")
        report.append(f"  3. Probabilistic Forecasting: We employ Monte Carlo sampling (N={self.monte_carlo_paths} paths)")
        report.append(f"     to generate a distribution of possible future price trajectories.")
        report.append(f"  4. Derived Insights: The resulting distribution is analyzed to produce the")
        report.append(f"     mean forecast, uncertainty range, and key probability metrics.")
        report.append("")
        
        # 关于Kronos项目
        report.append("About The Kronos Project")
        report.append("")
        report.append("Kronos is the first open-source foundation model for financial candlesticks")
        report.append("(K-lines), trained on data from over 45 global exchanges. It is designed to")
        report.append("serve as a unified model for diverse quantitative finance tasks.")
        report.append("")
        report.append("© 2025 The Kronos Project. Licensed under the MIT License.")
        report.append("=" * 80)
        
        return "\n".join(report)

    def run_btc_forecast(self) -> bool:
        """
        执行完整的BTC预测流程
        """
        try:
            logger.info("=" * 60)
            logger.info("开始BTC预测复现流程")
            logger.info("=" * 60)
            
            # 1. 下载数据
            df = self.download_btc_data()
            if df is None:
                return False
            
            # 2. 执行Monte Carlo预测
            forecast_result = self.monte_carlo_forecast(df)
            if forecast_result is None:
                return False
            
            # 3. 生成图表
            chart_file = self.create_official_style_chart(forecast_result)
            
            # 4. 生成报告
            report = self.generate_official_report(forecast_result)
            print("\n" + report)
            
            # 5. 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"BTC_forecast_report_{timestamp}.txt"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"\n📁 详细报告已保存: {report_file}")
            if chart_file:
                print(f"📊 预测图表已保存: {chart_file}")
            
            logger.info("BTC预测复现完成！")
            return True
            
        except Exception as e:
            logger.error(f"BTC预测流程失败: {str(e)}")
            return False

def main():
    """主程序"""
    print("🚀 Kronos BTC预测复现系统")
    print("完全按照官方规格复现BTC/USD预测效果")
    print("=" * 60)
    
    try:
        # 初始化系统
        btc_system = BTCForecastReplica(device="cpu")
        
        # 执行预测
        success = btc_system.run_btc_forecast()
        
        if success:
            print("\n🎉 BTC预测复现成功!")
            print("📊 图表风格已匹配官方效果")
            print("📈 指标计算已按官方方法论")
        else:
            print("\n❌ BTC预测复现失败")
            
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        print(f"❌ 程序执行失败: {str(e)}")

if __name__ == "__main__":
    main()