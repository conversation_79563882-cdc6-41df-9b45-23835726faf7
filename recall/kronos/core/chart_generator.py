#!/usr/bin/env python3
"""
通用图表生成器
提取BTC预测系统中的优秀图表生成功能，用于所有资产的预测可视化

主要功能：
1. 官方风格的价格&成交量预测图表
2. 细柱状图Volume显示（width=0.03）
3. 概率性预测区间可视化
4. 支持股票、加密货币等多种资产

作者：基于btc_forecast_replica.py优化提取
日期：2025-08-23
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from typing import Dict, List, Optional
import logging

# 配置matplotlib中文字体支持
try:
    import matplotlib.font_manager as fm
    # 尝试使用系统中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
except:
    # 如果中文字体不可用，使用英文
    plt.rcParams['font.family'] = 'sans-serif'

logger = logging.getLogger(__name__)

class KronosChartGenerator:
    """Kronos风格的通用图表生成器"""
    
    def __init__(self):
        """初始化图表生成器"""
        self.output_dir = "./output/charts"
        os.makedirs(self.output_dir, exist_ok=True)
    
    def create_prediction_chart(self, 
                              forecast_result: Dict, 
                              symbol: str = "ASSET",
                              asset_type: str = "Stock") -> str:
        """
        创建官方风格的预测图表
        
        Args:
            forecast_result: 预测结果字典，必须包含：
                - success: bool
                - context_data: DataFrame (历史数据)
                - future_timestamps: List[datetime]
                - current_time: datetime
                - current_price: float
                - mean_forecast: array
                - min_forecast: array  
                - max_forecast: array
                - high_sample_pred: DataFrame (预测数据)
            symbol: 资产代码 (如 AAPL, BTC)
            asset_type: 资产类型 (如 Stock, Crypto)
        
        Returns:
            str: 生成的图表文件路径
        """
        if not forecast_result.get('success', False):
            logger.error("预测结果无效，无法生成图表")
            return None
        
        # 设置图表样式（匹配官方）
        plt.style.use('default')
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 10), height_ratios=[3, 1])
        
        # 准备数据
        context_data = forecast_result['context_data']
        future_timestamps = pd.to_datetime(forecast_result['future_timestamps'])
        current_time = forecast_result['current_time']
        current_price = forecast_result['current_price']
        
        mean_forecast = forecast_result['mean_forecast']
        min_forecast = forecast_result['min_forecast']
        max_forecast = forecast_result['max_forecast']
        
        # 确定单位
        price_unit = "USDT" if "BTC" in symbol.upper() else "USD"
        
        # 确定数据类型（基于历史数据时间间隔）
        if len(context_data) > 1:
            # 计算时间间隔（天为单位）
            time_diff = (context_data['timestamp'].iloc[1] - context_data['timestamp'].iloc[0]).total_seconds() / 86400
            # 根据时间间隔确定数据类型
            if time_diff >= 1:  # 日线数据
                data_type = "daily"
            elif time_diff >= 1/24:  # 小时数据  
                data_type = "hourly"
            else:  # 分钟数据
                data_type = "minute"
        else:
            data_type = "hourly"  # 默认为小时数据
        
        # === 上部分：价格预测图 ===
        # 显示更多历史数据用于视觉效果
        hist_display_hours = min(200, len(context_data))
        hist_data = context_data.tail(hist_display_hours)
        
        # 历史价格（蓝色线）
        ax1.plot(hist_data['timestamp'], hist_data['close'], 
                'b-', linewidth=1.5, label='Historical Price', alpha=0.8)
        
        # 预测范围（浅橙色阴影区域）
        ax1.fill_between(future_timestamps, min_forecast, max_forecast,
                        color='orange', alpha=0.15, label='Forecast Range (Min-Max)')
        
        # 连接线（历史最后一点到预测第一点）
        connection_times = [current_time, future_timestamps[0]]
        connection_prices = [current_price, mean_forecast[0]]
        ax1.plot(connection_times, connection_prices, 'orange', linewidth=2, alpha=0.8)
        
        # 平均预测（橙色线）
        ax1.plot(future_timestamps, mean_forecast, 
                'orange', linewidth=3, label='Mean Forecast')
        
        # 当前时间分割线（红色虚线）
        ax1.axvline(x=current_time, color='red', linestyle='--', 
                   linewidth=2, alpha=0.8, label='Current Time')
        
        # 动态生成预测时间范围文本
        forecast_steps = len(future_timestamps)
        if data_type == "daily":
            if forecast_steps == 1:
                time_range_text = "Next Day"
            else:
                time_range_text = f"Next {forecast_steps} Days"
        elif data_type == "hourly":
            if forecast_steps == 24:
                time_range_text = "Next 24 Hours"
            elif forecast_steps == 1:
                time_range_text = "Next Hour"
            else:
                time_range_text = f"Next {forecast_steps} Hours"
        else:  # minute data
            if forecast_steps == 60:
                time_range_text = "Next Hour"
            elif forecast_steps < 60:
                time_range_text = f"Next {forecast_steps} Minutes"
            else:
                hours = forecast_steps // 60
                minutes = forecast_steps % 60
                if minutes == 0:
                    time_range_text = f"Next {hours} Hours"
                else:
                    time_range_text = f"Next {hours}h{minutes}m"
        
        # 设置价格图属性
        ax1.set_title(f'{symbol} Probabilistic Price & Volume Forecast ({time_range_text})', 
                     fontsize=16, fontweight='bold')
        ax1.set_ylabel(f'Price ({price_unit})', fontsize=12)
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)
        
        # 设置Y轴范围（确保预测范围完全可见）
        y_min = min(hist_data['close'].min(), min_forecast.min()) * 0.98
        y_max = max(hist_data['close'].max(), max_forecast.max()) * 1.02
        ax1.set_ylim(y_min, y_max)
        
        # === 下部分：成交量图 ===
        # 根据数据类型设置柱子宽度
        if data_type == "daily":
            bar_width = 0.8  # 日线用较宽的柱子
        elif data_type == "hourly":
            bar_width = 0.03  # 小时线用细柱子
        else:  # minute data
            bar_width = 0.01  # 分钟线用最细的柱子
        
        # 历史成交量（动态宽度柱状图）
        ax2.bar(hist_data['timestamp'], hist_data['volume'],
               color='skyblue', label='Historical Volume', width=bar_width)
        
        # 预测成交量（动态宽度柱状图）
        pred_volume = forecast_result['high_sample_pred']['volume'].values
        ax2.bar(future_timestamps, pred_volume,
               color='sandybrown', label='Mean Forecasted Volume', width=bar_width)
        
        # 当前时间分割线
        ax2.axvline(x=current_time, color='red', linestyle='--', 
                   linewidth=2, alpha=0.8)
        
        # 设置成交量图属性
        ax2.set_ylabel('Volume', fontsize=12)
        ax2.set_xlabel('Time (UTC)', fontsize=12)
        ax2.legend(loc='upper left')
        ax2.grid(True, which='both', linestyle='--', linewidth=0.5)
        
        # X轴标签倾斜显示（匹配官方样式）
        for ax in [ax1, ax2]:
            ax.tick_params(axis='x', rotation=30)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'{symbol}_{asset_type}_forecast_{timestamp}.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        logger.info(f"预测图表已生成: {filepath}")
        return filepath
    
    def create_ranking_chart(self, 
                           ranking_data: pd.DataFrame, 
                           top_n: int = 10) -> str:
        """
        创建股票排序结果图表
        
        Args:
            ranking_data: 排序数据DataFrame
            top_n: 显示前N名
        
        Returns:
            str: 生成的图表文件路径
        """
        plt.style.use('default')
        fig, ax = plt.subplots(figsize=(14, 8))
        
        # 选择前N名
        top_data = ranking_data.head(top_n)
        
        # 创建水平柱状图
        y_pos = np.arange(len(top_data))
        
        # 使用预期收益作为主要指标（实际数据列名）
        if 'expected_return' in top_data.columns:
            scores = top_data['expected_return'] * 100  # 转换为百分比
            x_label = 'Expected Return (%)'
            score_format = '.2f'
        else:
            scores = top_data.iloc[:, -1]  # 使用最后一列作为默认评分
            x_label = 'Score'
            score_format = '.3f'
        
        bars = ax.barh(y_pos, scores, color='steelblue', alpha=0.7)
        
        # 设置标签（使用实际列名）
        ax.set_yticks(y_pos)
        if 'symbol' in top_data.columns:
            ax.set_yticklabels(top_data['symbol'])
        else:
            ax.set_yticklabels(top_data.index)
        
        ax.set_xlabel(x_label, fontsize=12)
        ax.set_title(f'Top {top_n} Stock Prediction Ranking', fontsize=16, fontweight='bold')
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            if 'expected_return' in top_data.columns:
                label = f'{width:.2f}%'  # 百分比格式
            else:
                label = f'{width:.3f}'   # 默认格式
            ax.text(width + 0.01, bar.get_y() + bar.get_height()/2, 
                   label, ha='left', va='center', fontsize=10)
        
        ax.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'stock_ranking_{timestamp}.png'
        filepath = os.path.join(self.output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        logger.info(f"排序图表已生成: {filepath}")
        return filepath

def create_asset_chart(forecast_result: Dict, 
                      symbol: str, 
                      asset_type: str = "Stock") -> str:
    """
    便捷函数：创建资产预测图表
    
    Args:
        forecast_result: 预测结果
        symbol: 资产代码  
        asset_type: 资产类型
    
    Returns:
        str: 图表文件路径
    """
    generator = KronosChartGenerator()
    return generator.create_prediction_chart(forecast_result, symbol, asset_type)

def create_stock_ranking_chart(ranking_data: pd.DataFrame, top_n: int = 10) -> str:
    """
    便捷函数：创建股票排序图表
    
    Args:
        ranking_data: 排序数据
        top_n: 显示前N名
    
    Returns:  
        str: 图表文件路径
    """
    generator = KronosChartGenerator()
    return generator.create_ranking_chart(ranking_data, top_n)

if __name__ == "__main__":
    print("📊 Kronos通用图表生成器")
    print("提供官方风格的预测图表生成功能")
    print("支持股票、加密货币等多种资产类型")