#!/usr/bin/env python3
"""
批量股票预测器
支持多只股票的批量预测和结果汇总

功能：
1. 批量预测多只股票
2. 生成汇总排序报告
3. 支持自定义股票池
4. 并行处理优化

作者：基于stock_predictor.py扩展
日期：2025-08-23
"""

import os
import pandas as pd
import numpy as np
import sys
from datetime import datetime
import logging
from typing import List, Dict, Optional
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor

# 添加项目路径
sys.path.append("../")
from stock_predictor import StockPredictor
from chart_generator import create_stock_ranking_chart

logger = logging.getLogger(__name__)

class BatchStockPredictor:
    """批量股票预测器"""
    
    def __init__(self, 
                 data_path: str = "../../data/us",  # 指向项目数据目录
                 lookback_days: int = 150,
                 prediction_days: int = 5,
                 max_workers: int = 3):  # 限制并行数避免内存过载
        """
        初始化批量预测器
        
        Args:
            data_path: 数据路径
            lookback_days: 历史数据天数
            prediction_days: 预测天数
            max_workers: 最大并行数
        """
        self.data_path = data_path
        self.lookback_days = lookback_days
        self.prediction_days = prediction_days
        self.max_workers = max_workers
        
        logger.info("初始化批量股票预测系统")
        logger.info(f"数据路径: {data_path}")
        logger.info(f"最大并行数: {max_workers}")

    def get_available_stocks(self) -> List[str]:
        """
        获取所有可用的股票列表
        
        Returns:
            List[str]: 股票代码列表
        """
        try:
            stocks = []
            if os.path.exists(self.data_path):
                for item in os.listdir(self.data_path):
                    stock_dir = os.path.join(self.data_path, item)
                    if os.path.isdir(stock_dir) and not item.startswith('.'):
                        # 检查是否有1d.parquet文件
                        parquet_file = os.path.join(stock_dir, "1d.parquet")
                        if os.path.exists(parquet_file):
                            stocks.append(item)
            
            stocks.sort()  # 按字母顺序排序
            logger.info(f"发现 {len(stocks)} 只可用股票")
            return stocks
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {str(e)}")
            return []

    def predict_single_stock(self, symbol: str) -> Optional[Dict]:
        """
        预测单只股票（用于并行处理）
        
        Args:
            symbol: 股票代码
        
        Returns:
            Dict: 预测结果或None
        """
        try:
            logger.info(f"开始预测 {symbol}")
            
            # 创建独立的预测器实例
            predictor = StockPredictor(
                data_path=self.data_path,
                lookback_days=self.lookback_days,
                prediction_days=self.prediction_days
            )
            
            # 加载数据
            df = predictor.load_stock_data(symbol, "1d")
            if df is None:
                logger.warning(f"{symbol}: 数据加载失败")
                return None
            
            # 执行预测
            forecast_result = predictor.monte_carlo_forecast(df, symbol)
            if forecast_result is None:
                logger.warning(f"{symbol}: 预测失败")
                return None
            
            # 生成图表
            try:
                from chart_generator import create_asset_chart
                chart_file = create_asset_chart(forecast_result, symbol, "Stock")
                forecast_result['chart_file'] = chart_file
            except Exception as e:
                logger.warning(f"{symbol}: 图表生成失败 - {str(e)}")
                forecast_result['chart_file'] = None
            
            # 生成报告
            try:
                report = predictor.generate_stock_report(forecast_result)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                report_file = f"./output/reports/{symbol}_forecast_report_{timestamp}.txt"
                
                os.makedirs("./output/reports", exist_ok=True)
                with open(report_file, 'w', encoding='utf-8') as f:
                    f.write(report)
                forecast_result['report_file'] = report_file
            except Exception as e:
                logger.warning(f"{symbol}: 报告生成失败 - {str(e)}")
                forecast_result['report_file'] = None
            
            logger.info(f"✅ {symbol} 预测完成")
            return forecast_result
            
        except Exception as e:
            logger.error(f"❌ {symbol} 预测失败: {str(e)}")
            return None

    def predict_multiple_stocks(self, symbols: List[str], use_parallel: bool = True) -> Dict:
        """
        批量预测多只股票
        
        Args:
            symbols: 股票代码列表
            use_parallel: 是否使用并行处理
        
        Returns:
            Dict: 预测结果汇总
        """
        logger.info(f"开始批量预测 {len(symbols)} 只股票")
        logger.info(f"并行处理: {'开启' if use_parallel else '关闭'}")
        
        results = {}
        successful_predictions = []
        
        if use_parallel and len(symbols) > 1:
            # 并行处理
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_symbol = {executor.submit(self.predict_single_stock, symbol): symbol 
                                  for symbol in symbols}
                
                for future in concurrent.futures.as_completed(future_to_symbol):
                    symbol = future_to_symbol[future]
                    try:
                        result = future.result()
                        results[symbol] = result
                        if result is not None:
                            successful_predictions.append(result)
                    except Exception as exc:
                        logger.error(f"{symbol} 预测异常: {exc}")
                        results[symbol] = None
        else:
            # 串行处理
            for symbol in symbols:
                result = self.predict_single_stock(symbol)
                results[symbol] = result
                if result is not None:
                    successful_predictions.append(result)
        
        # 汇总统计
        successful_count = len(successful_predictions)
        total_count = len(symbols)
        
        logger.info(f"批量预测完成: {successful_count}/{total_count} 成功")
        
        return {
            'results': results,
            'successful_predictions': successful_predictions,
            'successful_count': successful_count,
            'total_count': total_count
        }

    def generate_ranking_report(self, batch_results: Dict) -> str:
        """
        生成批量预测排序报告
        
        Args:
            batch_results: 批量预测结果
        
        Returns:
            str: 排序报告
        """
        successful_predictions = batch_results['successful_predictions']
        
        if not successful_predictions:
            return "没有成功的预测结果，无法生成排序报告。"
        
        # 构建排序数据
        ranking_data = []
        for pred in successful_predictions:
            ranking_data.append({
                'symbol': pred['symbol'],
                'current_price': pred['current_price'],
                'expected_return': pred['expected_return'],
                'upside_probability': pred['upside_probability'],
                'predicted_volatility': pred['predicted_volatility'],
                'mean_final_price': pred['mean_final_price'],
                'price_range_width': pred['prediction_range'][1] - pred['prediction_range'][0]
            })
        
        df = pd.DataFrame(ranking_data)
        
        # 按预期收益排序
        df = df.sort_values('expected_return', ascending=False).reset_index(drop=True)
        
        # 生成报告
        report = []
        report.append("=" * 80)
        report.append("Kronos Batch Stock Prediction Ranking Report")
        report.append("")
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}")
        report.append(f"Total Stocks Analyzed: {batch_results['total_count']}")
        report.append(f"Successful Predictions: {batch_results['successful_count']}")
        report.append("=" * 80)
        report.append("")
        
        # 排序表格
        report.append("🏆 Stock Ranking by Expected Return")
        report.append("-" * 80)
        report.append(f"{'排名':>4} {'股票':>6} {'当前价格':>10} {'预期收益':>10} {'上涨概率':>10} {'预测波动':>10}")
        report.append("-" * 80)
        
        for i, row in df.iterrows():
            report.append(f"{i+1:>4} {row['symbol']:>6} ${row['current_price']:>9.2f} "
                        f"{row['expected_return']*100:>8.2f}% {row['upside_probability']*100:>8.1f}% "
                        f"{row['predicted_volatility']*100:>8.2f}%")
        
        # 投资建议
        report.append("")
        report.append("📈 投资建议")
        report.append("-" * 40)
        
        # Top 3 推荐
        top3 = df.head(3)
        report.append("🥇 强烈推荐:")
        for _, row in top3.iterrows():
            report.append(f"   {row['symbol']}: 预期收益{row['expected_return']*100:+.2f}%, "
                        f"上涨概率{row['upside_probability']*100:.1f}%")
        
        # 风险警告
        high_volatility = df[df['predicted_volatility'] > 0.03]  # 3%以上波动率
        if len(high_volatility) > 0:
            report.append("")
            report.append("⚠️ 高波动性股票:")
            for _, row in high_volatility.iterrows():
                report.append(f"   {row['symbol']}: 预测波动性{row['predicted_volatility']*100:.2f}%, 谨慎操作")
        
        # 市场整体分析
        report.append("")
        report.append("🌍 市场整体分析")
        report.append("-" * 40)
        
        avg_return = df['expected_return'].mean()
        avg_upside_prob = df['upside_probability'].mean()
        avg_volatility = df['predicted_volatility'].mean()
        
        market_sentiment = "乐观" if avg_return > 0.01 else "谨慎" if avg_return > -0.01 else "悲观"
        
        report.append(f"市场情绪: {market_sentiment} (平均预期收益{avg_return*100:+.2f}%)")
        report.append(f"平均上涨概率: {avg_upside_prob*100:.1f}%")
        report.append(f"平均波动性: {avg_volatility*100:.2f}%")
        
        report.append("")
        report.append("=" * 80)
        report.append("💡 本报告基于Kronos模型的概率性预测，仅供参考")
        report.append("⚠️ 投资有风险，决策需谨慎")
        report.append("=" * 80)
        
        return "\n".join(report)

    def run_batch_prediction(self, symbols: List[str]) -> bool:
        """
        执行完整的批量预测流程
        
        Args:
            symbols: 股票代码列表
        
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("=" * 60)
            logger.info("开始批量股票预测流程")
            logger.info("=" * 60)
            
            # 1. 批量预测
            batch_results = self.predict_multiple_stocks(symbols, use_parallel=True)
            
            if batch_results['successful_count'] == 0:
                logger.error("没有任何股票预测成功")
                return False
            
            # 2. 生成排序报告
            ranking_report = self.generate_ranking_report(batch_results)
            print("\n" + ranking_report)
            
            # 3. 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存排序报告
            report_file = f"./output/reports/batch_ranking_report_{timestamp}.txt"
            os.makedirs("../output/reports", exist_ok=True)
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(ranking_report)
            
            # 保存数据CSV
            if batch_results['successful_predictions']:
                ranking_data = []
                for pred in batch_results['successful_predictions']:
                    ranking_data.append({
                        'symbol': pred['symbol'],
                        'current_price': pred['current_price'],
                        'expected_return': pred['expected_return'],
                        'upside_probability': pred['upside_probability'],
                        'predicted_volatility': pred['predicted_volatility'],
                        'mean_final_price': pred['mean_final_price']
                    })
                
                df = pd.DataFrame(ranking_data)
                df = df.sort_values('expected_return', ascending=False)
                
                csv_file = f"./output/data/batch_prediction_data_{timestamp}.csv"
                os.makedirs("./output/data", exist_ok=True)
                df.to_csv(csv_file, index=False)
                
                # 生成排序图表
                try:
                    chart_file = create_stock_ranking_chart(df, top_n=len(symbols))
                    print(f"\n📁 排序报告: {report_file}")
                    print(f"📁 预测数据: {csv_file}")
                    print(f"📊 排序图表: {chart_file}")
                except Exception as e:
                    logger.warning(f"排序图表生成失败: {str(e)}")
                    print(f"\n📁 排序报告: {report_file}")
                    print(f"📁 预测数据: {csv_file}")
            
            print(f"\n🎉 批量预测完成! {batch_results['successful_count']}/{batch_results['total_count']} 股票成功")
            return True
            
        except Exception as e:
            logger.error(f"批量预测流程失败: {str(e)}")
            return False

def main():
    """主程序"""
    print("🚀 Kronos批量股票预测系统")
    print("支持多只股票的批量预测和排序")
    print("=" * 60)
    
    try:
        # 初始化批量预测器
        batch_predictor = BatchStockPredictor()
        
        # 获取可用股票
        available_stocks = batch_predictor.get_available_stocks()
        print(f"发现 {len(available_stocks)} 只可用股票")
        
        # 选择要预测的股票池
        # 示例1: 预测热门股票
        hot_stocks = ['SPY', 'QQQ', 'AAPL', 'NVDA', 'TSLA', 'MSFT', 'GOOGL']
        available_hot_stocks = [s for s in hot_stocks if s in available_stocks]
        
        if available_hot_stocks:
            print(f"\n预测热门股票池: {', '.join(available_hot_stocks)}")
            success = batch_predictor.run_batch_prediction(available_hot_stocks)
            
            if success:
                print("\n✅ 热门股票批量预测成功!")
            else:
                print("\n❌ 热门股票批量预测失败")
        else:
            print("\n❌ 没有找到热门股票数据")
            
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        print(f"❌ 程序执行失败: {str(e)}")

if __name__ == "__main__":
    main()