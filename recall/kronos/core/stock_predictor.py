#!/usr/bin/env python3
"""
通用股票预测器
基于BTC预测系统，支持本地parquet数据的股票预测

功能：
1. 读取本地parquet格式的股票数据
2. 自动计算amount字段 (volume * close)
3. 生成官方风格的概率性预测图表
4. 支持任意时间频率的预测

作者：基于btc_predictor.py优化
日期：2025-08-23
"""

import os
import pandas as pd
import numpy as np
import sys
from datetime import datetime, timedelta
import logging
import warnings
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from typing import Optional, Dict, Tuple, List
import pytz

warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append("../")
from model import Kronos, KronosTokenizer, KronosPredictor
from chart_generator import create_asset_chart

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StockPredictor:
    """通用股票预测器"""
    
    def __init__(self, 
                 data_path: str = "../../data/us",  # 指向项目数据目录
                 lookback_days: int = 150,  # 历史数据天数
                 prediction_days: int = 5,  # 预测天数
                 monte_carlo_paths: int = 30,  # Monte Carlo路径数
                 device: str = "cpu"):
        """
        初始化股票预测系统
        
        Args:
            data_path: 数据路径 (parquet files)
            lookback_days: 历史数据天数
            prediction_days: 预测天数  
            monte_carlo_paths: Monte Carlo采样路径数
            device: 计算设备
        """
        self.data_path = data_path
        self.lookback_days = lookback_days
        self.prediction_days = prediction_days
        self.monte_carlo_paths = monte_carlo_paths
        self.device = device
        
        # 预测器组件（延迟加载）
        self.tokenizer = None
        self.model = None
        self.predictor = None
        
        logger.info("初始化通用股票预测系统")
        logger.info(f"数据路径: {data_path}")
        logger.info(f"历史数据: {lookback_days}天，预测: {prediction_days}天，采样: {monte_carlo_paths}路径")

    def load_stock_data(self, symbol: str, frequency: str = "1d") -> Optional[pd.DataFrame]:
        """
        从本地parquet文件加载股票数据
        
        Args:
            symbol: 股票代码 (如 SPY, AAPL)
            frequency: 数据频率 (1d, 1h, 15m)
        
        Returns:
            DataFrame: 包含timestamp, open, high, low, close, volume, amount的数据
        """
        try:
            # 构建文件路径
            file_path = os.path.join(self.data_path, symbol, f"{frequency}.parquet")
            
            if not os.path.exists(file_path):
                logger.error(f"数据文件不存在: {file_path}")
                return None
            
            logger.info(f"正在加载 {symbol} {frequency} 数据...")
            
            # 读取parquet文件
            df = pd.read_parquet(file_path)
            
            # 检查必需的列
            required_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_cols):
                logger.error(f"数据文件缺少必需列: {required_cols}")
                return None
            
            # 确保timestamp为datetime格式
            if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
                df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            # **关键：计算amount字段 (Kronos模型必需)**
            df['amount'] = df['volume'] * df['close']
            
            # 确保数据类型正确
            for col in ['open', 'high', 'low', 'close', 'volume', 'amount']:
                df[col] = df[col].astype(float)
            
            # 按时间排序并重置索引
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            # 移除重复数据
            df = df.drop_duplicates(subset=['timestamp']).reset_index(drop=True)
            
            # 只保留最近的数据（避免内存过载）
            max_records = self.lookback_days + 50  # 额外缓冲
            if len(df) > max_records:
                df = df.tail(max_records).reset_index(drop=True)
            
            logger.info(f"成功加载{len(df)}条{symbol}数据")
            logger.info(f"数据时间范围: {df['timestamp'].min()} 到 {df['timestamp'].max()}")
            logger.info(f"最新价格: ${df['close'].iloc[-1]:.2f}")
            
            return df
            
        except Exception as e:
            logger.error(f"加载{symbol}数据失败: {str(e)}")
            return None

    def _load_model(self):
        """加载Kronos模型"""
        if self.predictor is None:
            logger.info("加载Kronos-small模型...")
            self.tokenizer = KronosTokenizer.from_pretrained("NeoQuasar/Kronos-Tokenizer-base")
            self.model = Kronos.from_pretrained("NeoQuasar/Kronos-small")
            self.predictor = KronosPredictor(
                model=self.model,
                tokenizer=self.tokenizer,
                device=self.device,
                max_context=512
            )

    def monte_carlo_forecast(self, df: pd.DataFrame, symbol: str) -> Optional[Dict]:
        """
        使用Monte Carlo方法生成概率性预测
        
        Args:
            df: 股票历史数据
            symbol: 股票代码
        
        Returns:
            Dict: 预测结果
        """
        try:
            self._load_model()
            
            # 检查数据量是否足够
            if len(df) < self.lookback_days + 10:
                logger.error(f"数据不足，需要至少{self.lookback_days + 10}天，当前{len(df)}天")
                return None
            
            # 提取最近N天数据作为上下文
            context_data = df.tail(self.lookback_days).copy()
            
            current_price = context_data['close'].iloc[-1]
            current_time = context_data['timestamp'].iloc[-1]
            
            logger.info(f"使用{len(context_data)}天历史数据作为上下文")
            logger.info(f"当前{symbol}价格: ${current_price:.2f}")
            
            # 准备模型输入（6维：OHLCV + Amount）
            x_df = context_data[['open', 'high', 'low', 'close', 'volume', 'amount']]
            x_timestamp = context_data['timestamp']
            
            # 生成未来预测时间戳
            future_timestamps = []
            for i in range(1, self.prediction_days + 1):
                future_time = current_time + timedelta(days=i)
                future_timestamps.append(future_time)
            y_timestamp = pd.Series(future_timestamps)
            
            logger.info(f"开始Monte Carlo预测（{self.monte_carlo_paths}路径）...")
            
            # 执行主要预测（GitHub推荐参数）
            pred_df = self.predictor.predict(
                df=x_df,
                x_timestamp=x_timestamp,
                y_timestamp=y_timestamp,
                pred_len=self.prediction_days,
                T=0.6,          # GitHub推荐参数
                top_p=0.9,      # GitHub推荐参数
                sample_count=self.monte_carlo_paths,
                verbose=True
            )
            
            # 生成多个独立预测路径以创建真实的概率分布
            logger.info("生成多路径Monte Carlo预测...")
            all_predictions = []
            
            # 生成20个独立预测（每次单样本以获得多样性）
            for i in range(20):
                single_pred = self.predictor.predict(
                    df=x_df,
                    x_timestamp=x_timestamp,
                    y_timestamp=y_timestamp,
                    pred_len=self.prediction_days,
                    T=0.6,
                    top_p=0.9,
                    sample_count=1,  # 单个样本以获得多样性
                    verbose=False
                )
                all_predictions.append(single_pred['close'].values)
                
                if (i + 1) % 5 == 0:
                    logger.info(f"已生成 {i + 1}/20 预测路径")
            
            # 添加主要预测作为基准
            all_predictions.append(pred_df['close'].values)
            all_predictions = np.array(all_predictions)  # shape: (21, prediction_days)
            
            # 计算统计量
            mean_forecast = np.mean(all_predictions, axis=0)
            min_forecast = np.min(all_predictions, axis=0)
            max_forecast = np.max(all_predictions, axis=0)
            std_forecast = np.std(all_predictions, axis=0)
            
            # 计算关键指标
            # 1. 上涨概率（最后一天价格高于当前价格的概率）
            final_prices = all_predictions[:, -1]
            upside_probability = np.mean(final_prices > current_price)
            
            # 2. 预期收益
            expected_return = (np.mean(final_prices) - current_price) / current_price
            
            # 3. 波动性
            predicted_returns = []
            for path in all_predictions:
                returns = np.diff(path) / path[:-1]
                predicted_returns.extend(returns)
            predicted_volatility = np.std(predicted_returns)
            
            result = {
                'success': True,
                'symbol': symbol,
                'current_price': current_price,
                'current_time': current_time,
                'context_data': context_data,
                'future_timestamps': future_timestamps,
                
                # 预测结果
                'mean_forecast': mean_forecast,
                'min_forecast': min_forecast,
                'max_forecast': max_forecast,
                'std_forecast': std_forecast,
                'high_sample_pred': pred_df,  # 高采样预测结果
                'all_predictions': all_predictions,
                
                # 关键指标
                'upside_probability': upside_probability,
                'expected_return': expected_return,
                'predicted_volatility': predicted_volatility,
                
                # 统计信息
                'prediction_range': [np.min(final_prices), np.max(final_prices)],
                'mean_final_price': np.mean(final_prices),
                'std_final_price': np.std(final_prices)
            }
            
            logger.info(f"{symbol} Monte Carlo预测完成")
            logger.info(f"上涨概率: {upside_probability*100:.1f}%")
            logger.info(f"预期收益: {expected_return*100:+.2f}%")
            logger.info(f"预测价格范围: ${np.min(final_prices):.2f} - ${np.max(final_prices):.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"{symbol} Monte Carlo预测失败: {str(e)}")
            return None

    def generate_stock_report(self, forecast_result: Dict) -> str:
        """
        生成股票预测报告
        
        Args:
            forecast_result: 预测结果
        
        Returns:
            str: 格式化的报告
        """
        if not forecast_result.get('success', False):
            return "预测失败，无法生成报告"
        
        symbol = forecast_result['symbol']
        current_utc = datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")
        
        # 格式化指标
        upside_prob = forecast_result['upside_probability'] * 100
        expected_return = forecast_result['expected_return'] * 100
        current_price = forecast_result['current_price']
        mean_final_price = forecast_result['mean_final_price']
        
        report = []
        report.append("=" * 80)
        report.append(f"Kronos Stock Prediction: {symbol}")
        report.append("")
        report.append('Powered by "Kronos: A Foundation Model for the Language of Financial Markets"')
        report.append("=" * 80)
        report.append("")
        report.append(f"Live {symbol} Forecast Dashboard")
        report.append(f"Last Updated (UTC): {current_utc}")
        report.append(f"Data Source: Local Historical Data | Frequency: Daily")
        report.append("")
        
        # 核心指标
        report.append(f"Upside Probability (Next {self.prediction_days} Days)")
        report.append("")
        report.append(f"{upside_prob:.1f}%")
        report.append(f"The model's confidence that the price in {self.prediction_days} days will be higher than the current price.")
        report.append("")
        
        report.append(f"Expected Return (Next {self.prediction_days} Days)")
        report.append("")
        report.append(f"{expected_return:+.2f}%")
        report.append("The expected percentage change in stock price based on Monte Carlo simulation.")
        report.append("")
        
        # 预测摘要
        price_range = forecast_result['prediction_range']
        report.append(f"{self.prediction_days}-Day Probabilistic Forecast")
        report.append("")
        report.append("The chart shows the historical price (blue) and the probabilistic forecast (orange).")
        report.append("The orange line is the mean of multiple Monte Carlo simulations, and the shaded")
        report.append("area represents the full range of predicted outcomes, indicating forecast uncertainty.")
        report.append("")
        
        report.append("Price Forecast Summary:")
        report.append(f"  Current Price: ${current_price:.2f}")
        report.append(f"  {self.prediction_days}d Mean Forecast: ${mean_final_price:.2f}")
        report.append(f"  {self.prediction_days}d Range: ${price_range[0]:.2f} - ${price_range[1]:.2f}")
        report.append(f"  Expected Change: {expected_return:+.2f}%")
        report.append("")
        
        # 方法论
        report.append("Methodology Overview")
        report.append("")
        report.append("This prediction showcases the forecasting results of Kronos, a foundation model")
        report.append("pre-trained on the 'language' of financial markets. The predictions are")
        report.append("generated using the following process:")
        report.append("")
        report.append(f"  1. Model: The Kronos-small (25M parameters) model is used to autoregressively")
        report.append(f"     predict future K-line data.")
        report.append(f"  2. Data Context: The model uses the last {self.lookback_days} days of {symbol}")
        report.append(f"     daily OHLCV data as context for each new prediction.")
        report.append(f"  3. Probabilistic Forecasting: We employ Monte Carlo sampling (N={self.monte_carlo_paths} paths)")
        report.append(f"     to generate a distribution of possible future price trajectories.")
        report.append(f"  4. Amount Calculation: Trading amount is computed as volume × close price")
        report.append(f"     to provide the 6-dimensional input required by Kronos.")
        report.append("")
        
        report.append("About The Kronos Project")
        report.append("")
        report.append("Kronos is the first open-source foundation model for financial candlesticks")
        report.append("(K-lines), trained on data from over 45 global exchanges. It is designed to")
        report.append("serve as a unified model for diverse quantitative finance tasks.")
        report.append("")
        report.append("© 2025 The Kronos Project. Licensed under the MIT License.")
        report.append("=" * 80)
        
        return "\n".join(report)

    def predict_stock(self, symbol: str, frequency: str = "1d") -> bool:
        """
        执行完整的股票预测流程
        
        Args:
            symbol: 股票代码
            frequency: 数据频率
        
        Returns:
            bool: 是否成功
        """
        try:
            logger.info("=" * 60)
            logger.info(f"开始{symbol}股票预测流程")
            logger.info("=" * 60)
            
            # 1. 加载数据
            df = self.load_stock_data(symbol, frequency)
            if df is None:
                return False
            
            # 2. 执行Monte Carlo预测
            forecast_result = self.monte_carlo_forecast(df, symbol)
            if forecast_result is None:
                return False
            
            # 3. 生成图表（使用官方风格）
            try:
                chart_file = create_asset_chart(forecast_result, symbol, "Stock")
                if chart_file:
                    logger.info(f"预测图表已生成: {chart_file}")
            except Exception as e:
                logger.warning(f"图表生成失败: {str(e)}")
            
            # 4. 生成报告
            report = self.generate_stock_report(forecast_result)
            print("\n" + report)
            
            # 5. 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"./output/reports/{symbol}_forecast_report_{timestamp}.txt"
            
            os.makedirs("./output/reports", exist_ok=True)
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"\n📁 详细报告已保存: {report_file}")
            if 'chart_file' in locals():
                print(f"📊 预测图表已保存: {chart_file}")
            
            logger.info(f"{symbol}股票预测完成！")
            return True
            
        except Exception as e:
            logger.error(f"{symbol}股票预测失败: {str(e)}")
            return False

def predict_multiple_stocks(symbols: List[str], 
                          data_path: str = "/Users/<USER>/Documents/Git/quant-lab/data/us",
                          frequency: str = "1d") -> Dict:
    """
    批量预测多只股票
    
    Args:
        symbols: 股票代码列表
        data_path: 数据路径
        frequency: 数据频率
    
    Returns:
        Dict: 预测结果汇总
    """
    logger.info(f"开始批量预测 {len(symbols)} 只股票")
    
    predictor = StockPredictor(data_path=data_path)
    results = {}
    
    for symbol in symbols:
        logger.info(f"\n处理股票: {symbol}")
        success = predictor.predict_stock(symbol, frequency)
        results[symbol] = success
        
        if success:
            logger.info(f"✅ {symbol} 预测成功")
        else:
            logger.error(f"❌ {symbol} 预测失败")
    
    # 汇总结果
    successful = sum(results.values())
    total = len(results)
    
    logger.info(f"\n批量预测完成: {successful}/{total} 成功")
    
    return results

def main():
    """主程序"""
    print("📈 Kronos通用股票预测系统")
    print("支持本地parquet数据的股票预测")
    print("=" * 60)
    
    try:
        # 初始化预测器
        predictor = StockPredictor()
        
        # 预测SPY
        print("开始预测SPY...")
        success = predictor.predict_stock("SPY")
        
        if success:
            print("\n🎉 SPY预测成功!")
            print("📊 官方风格图表已生成")
            print("📈 完整预测报告已保存")
        else:
            print("\n❌ SPY预测失败")
            
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        print(f"❌ 程序执行失败: {str(e)}")

if __name__ == "__main__":
    main()