================================================================================
Kronos: Live Probabilistic Forecast

A Replica of "Kronos: A Foundation Model for the Language of Financial Markets"
================================================================================

Live BTC/USDT Forecast Dashboard
Last Updated (UTC): 2025-08-23 07:00:46
Data Source: Binance | Interval: 1-Hour

Upside Probability (Next 24h)

9.5%
The model's confidence that the price in 24 hours will be higher than the last known price.

Volatility Amplification (Next 24h)

0.0%
The probability that predicted volatility over the next 24h will exceed recent historical volatility.

24-Hour Probabilistic Forecast

The chart shows the historical price (blue) and the probabilistic forecast (orange).
The orange line is the mean of multiple Monte Carlo simulations, and the shaded
area represents the full range of predicted outcomes, indicating forecast uncertainty.

Price Forecast Summary:
  Current Price: $115780.00
  24h Mean Forecast: $115539.09
  24h Range: $114749 - $115903
  Expected Change: -0.21%

Methodology Overview

This demo showcases the forecasting results of Kronos, a foundation model
pre-trained on the 'language' of financial markets. The predictions are
generated using the following process:

  1. Model: The Kronos-small (25M parameters) model is used to autoregressively
     predict future K-line data.
  2. Data Context: The model uses the last 360 hours (~15 days) of BTC/USDT
     1h K-line data from Binance as context for each new prediction.
  3. Probabilistic Forecasting: We employ Monte Carlo sampling (N=30 paths)
     to generate a distribution of possible future price trajectories.
  4. Derived Insights: The resulting distribution is analyzed to produce the
     mean forecast, uncertainty range, and key probability metrics.

About The Kronos Project

Kronos is the first open-source foundation model for financial candlesticks
(K-lines), trained on data from over 45 global exchanges. It is designed to
serve as a unified model for diverse quantitative finance tasks.

© 2025 The Kronos Project. Licensed under the MIT License.
================================================================================