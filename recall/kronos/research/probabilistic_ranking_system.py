#!/usr/bin/env python3
"""
Kronos概率性股票排序系统
基于多次采样和置信区间的稳健预测排序

核心改进：
1. 使用多路径采样(sample_count=10)获得预测分布
2. 计算预测置信区间和不确定性度量
3. 基于概率分布进行风险调整排序
4. 生成类似官方BTC图的可视化结果

作者：基于Kronos项目优化
日期：2025-08-23
"""

import os
import pandas as pd
import numpy as np
import sys
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import logging
import warnings
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append("../")
from model import Kronos, KronosTokenizer, KronosPredictor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProbabilisticRankingSystem:
    """Kronos概率性股票排序系统"""
    
    def __init__(self, 
                 data_path: str = "../../data/us",
                 lookback_days: int = 150,
                 prediction_days: int = 5,
                 n_ensemble: int = 10,  # 集成预测次数
                 device: str = "cpu"):
        """
        初始化概率性排序系统
        
        Args:
            n_ensemble: 集成预测次数，用于获得置信区间
        """
        self.data_path = data_path
        self.lookback_days = lookback_days
        self.prediction_days = prediction_days
        self.n_ensemble = n_ensemble
        self.device = device
        
        # 预测器初始化（延迟加载）
        self.tokenizer = None
        self.model = None
        self.predictor = None
        
        # 结果存储
        self.probabilistic_predictions = {}
        self.ranking_results = {}
        
        logger.info(f"初始化概率性排序系统 - 集成预测:{n_ensemble}次")

    def _load_model(self):
        """延迟加载Kronos模型"""
        if self.predictor is None:
            logger.info("加载Kronos模型...")
            self.tokenizer = KronosTokenizer.from_pretrained("NeoQuasar/Kronos-Tokenizer-base")
            self.model = Kronos.from_pretrained("NeoQuasar/Kronos-small")
            self.predictor = KronosPredictor(
                model=self.model, 
                tokenizer=self.tokenizer, 
                device=self.device, 
                max_context=512
            )

    def load_stock_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """加载股票数据"""
        try:
            file_path = os.path.join(self.data_path, symbol, "1d.parquet")
            if not os.path.exists(file_path):
                return None
            
            df = pd.read_parquet(file_path)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df['amount'] = df['volume'] * df['close']
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            if len(df) < self.lookback_days + 20:
                return None
            
            return df
            
        except Exception as e:
            logger.error(f"{symbol}: 数据加载失败 - {str(e)}")
            return None

    def probabilistic_predict(self, symbol: str, df: pd.DataFrame) -> Optional[Dict]:
        """
        概率性预测：进行多次预测并分析分布
        """
        try:
            self._load_model()
            
            # 准备数据
            end_idx = len(df) - 1
            start_idx = max(0, end_idx - self.lookback_days + 1)
            historical_data = df.iloc[start_idx:end_idx+1]
            current_price = historical_data['close'].iloc[-1]
            
            x_df = historical_data[['open', 'high', 'low', 'close', 'volume', 'amount']]
            x_timestamp = historical_data['timestamp']
            
            # 生成未来时间戳
            last_time = x_timestamp.iloc[-1]
            future_timestamps = [last_time + timedelta(days=i) for i in range(1, self.prediction_days + 1)]
            y_timestamp = pd.Series(future_timestamps)
            
            logger.info(f"{symbol}: 开始概率性预测({self.n_ensemble}次集成)")
            
            # 方法1: 使用高sample_count的单次预测（类似官方BTC图）
            logger.info(f"{symbol}: 执行高采样预测...")
            pred_high_sample = self.predictor.predict(
                df=x_df,
                x_timestamp=x_timestamp,
                y_timestamp=y_timestamp,
                pred_len=self.prediction_days,
                T=0.8,
                top_p=0.9,
                sample_count=20,  # 高采样数，类似BTC图
                verbose=False
            )
            
            # 方法2: 多次独立预测（获得真正的分布）
            logger.info(f"{symbol}: 执行多次独立预测...")
            all_predictions = []
            
            for i in range(self.n_ensemble):
                pred_single = self.predictor.predict(
                    df=x_df,
                    x_timestamp=x_timestamp,
                    y_timestamp=y_timestamp,
                    pred_len=self.prediction_days,
                    T=0.8,
                    top_p=0.9,
                    sample_count=3,  # 中等采样
                    verbose=False
                )
                all_predictions.append(pred_single['close'].values)
            
            # 分析预测分布
            all_predictions = np.array(all_predictions)  # shape: (n_ensemble, pred_len)
            
            # 统计量计算
            mean_prediction = np.mean(all_predictions, axis=0)
            std_prediction = np.std(all_predictions, axis=0)
            min_prediction = np.min(all_predictions, axis=0)
            max_prediction = np.max(all_predictions, axis=0)
            
            # 置信区间（假设正态分布）
            confidence_level = 0.95
            z_score = 1.96  # 95%置信区间
            ci_lower = mean_prediction - z_score * std_prediction
            ci_upper = mean_prediction + z_score * std_prediction
            
            # 分位数
            q25 = np.percentile(all_predictions, 25, axis=0)
            q75 = np.percentile(all_predictions, 75, axis=0)
            
            # 收益率统计
            final_returns = (all_predictions[:, -1] - current_price) / current_price
            avg_returns = (np.mean(all_predictions, axis=1) - current_price) / current_price
            
            # 稳定性指标
            prediction_cv = std_prediction / (mean_prediction + 1e-6)  # 变异系数
            return_stability = np.std(avg_returns)  # 收益率稳定性
            direction_consistency = np.mean(avg_returns > 0)  # 方向一致性
            
            result = {
                'symbol': symbol,
                'current_price': current_price,
                'success': True,
                
                # 高采样预测结果
                'high_sample_pred': pred_high_sample,
                
                # 多次预测统计
                'all_predictions': all_predictions,
                'mean_prediction': mean_prediction,
                'std_prediction': std_prediction,
                'min_prediction': min_prediction,
                'max_prediction': max_prediction,
                'ci_lower': ci_lower,
                'ci_upper': ci_upper,
                'q25': q25,
                'q75': q75,
                
                # 收益率分析
                'mean_avg_return': np.mean(avg_returns),
                'std_avg_return': np.std(avg_returns),
                'mean_final_return': np.mean(final_returns),
                'std_final_return': np.std(final_returns),
                'return_range': [np.min(avg_returns), np.max(avg_returns)],
                
                # 稳定性指标
                'prediction_cv': np.mean(prediction_cv),
                'return_stability': return_stability,
                'direction_consistency': direction_consistency,
                
                # 时间戳
                'future_timestamps': future_timestamps
            }
            
            logger.info(f"{symbol}: 概率性预测完成 - "
                       f"平均收益:{np.mean(avg_returns)*100:.2f}%±{np.std(avg_returns)*100:.2f}%, "
                       f"方向一致性:{direction_consistency*100:.1f}%")
            
            return result
            
        except Exception as e:
            logger.error(f"{symbol}: 概率性预测失败 - {str(e)}")
            return {'symbol': symbol, 'success': False, 'error': str(e)}

    def calculate_probabilistic_scores(self, prediction_result: Dict) -> Dict:
        """
        基于概率分布计算稳健的排序评分
        """
        if not prediction_result.get('success', False):
            return {'symbol': prediction_result['symbol'], 'valid': False}
        
        symbol = prediction_result['symbol']
        
        # 基础收益指标
        mean_return = prediction_result['mean_avg_return']
        return_std = prediction_result['std_avg_return']
        direction_consistency = prediction_result['direction_consistency']
        
        # 风险调整收益（考虑预测不确定性）
        prediction_uncertainty = prediction_result['return_stability']
        risk_adjusted_return = mean_return / (return_std + prediction_uncertainty + 1e-6)
        
        # 置信度加权收益（方向一致性作为权重）
        confidence_weighted_return = mean_return * direction_consistency
        
        # 下行风险保护（只考虑负收益的概率）
        negative_return_prob = np.mean(np.array(prediction_result['return_range']) < 0)
        downside_protection = 1 - negative_return_prob
        
        # Kelly策略风格评分（考虑胜率和赔率）
        win_rate = direction_consistency
        avg_win = max(mean_return, 0.001)  # 防止除零
        avg_loss = abs(min(mean_return, -0.001))
        kelly_score = win_rate - ((1 - win_rate) / (avg_win / avg_loss)) if avg_win > 0 else -1
        
        # 综合评分（更加注重稳定性）
        composite_score = (
            0.25 * confidence_weighted_return +     # 置信度加权收益 25%
            0.25 * risk_adjusted_return +           # 风险调整收益 25%
            0.20 * direction_consistency +          # 方向一致性 20%
            0.15 * downside_protection +            # 下行保护 15%
            0.15 * kelly_score                      # Kelly评分 15%
        )
        
        scores = {
            'symbol': symbol,
            'valid': True,
            'current_price': prediction_result['current_price'],
            'mean_return': mean_return,
            'return_std': return_std,
            'direction_consistency': direction_consistency,
            'prediction_uncertainty': prediction_uncertainty,
            'risk_adjusted_return': risk_adjusted_return,
            'confidence_weighted_return': confidence_weighted_return,
            'downside_protection': downside_protection,
            'kelly_score': kelly_score,
            'composite_score': composite_score,
            
            # 用于可视化的数据
            'prediction_result': prediction_result
        }
        
        return scores

    def create_btc_style_visualization(self, symbol: str, prediction_result: Dict) -> str:
        """
        创建类似BTC官方图的概率性预测可视化
        """
        if not prediction_result.get('success', False):
            return None
        
        # 准备数据
        historical_data = prediction_result.get('historical_data', None)
        if historical_data is None:
            # 重新加载历史数据用于可视化
            df = self.load_stock_data(symbol)
            end_idx = len(df) - 1
            start_idx = max(0, end_idx - 50)  # 显示最近50天
            historical_data = df.iloc[start_idx:end_idx+1]
        
        future_timestamps = prediction_result['future_timestamps']
        mean_pred = prediction_result['mean_prediction']
        min_pred = prediction_result['min_prediction']
        max_pred = prediction_result['max_prediction']
        ci_lower = prediction_result['ci_lower']
        ci_upper = prediction_result['ci_upper']
        
        # 创建图表
        plt.style.use('default')
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10), height_ratios=[3, 1])
        
        # === 价格预测图 ===
        # 历史价格
        hist_times = historical_data['timestamp']
        hist_prices = historical_data['close']
        
        ax1.plot(hist_times, hist_prices, 'b-', linewidth=2, label='Historical Price', alpha=0.8)
        
        # 预测区域和线条
        future_times = pd.to_datetime(future_timestamps)
        
        # 连接点（历史最后一点到预测第一点）
        connection_times = [hist_times.iloc[-1], future_times[0]]
        connection_prices = [hist_prices.iloc[-1], mean_pred[0]]
        
        # 预测范围（阴影）
        ax1.fill_between(future_times, min_pred, max_pred, 
                        color='orange', alpha=0.2, label='Forecast Range (Min-Max)')
        
        # 95%置信区间（较深的阴影）
        ax1.fill_between(future_times, ci_lower, ci_upper, 
                        color='orange', alpha=0.4, label='95% Confidence Interval')
        
        # 平均预测线
        ax1.plot(connection_times, connection_prices, 'orange', linewidth=2, alpha=0.8)
        ax1.plot(future_times, mean_pred, 'orange', linewidth=3, label='Mean Forecast')
        
        # 当前价格线
        current_price = prediction_result['current_price']
        ax1.axhline(y=current_price, color='red', linestyle='--', alpha=0.7, 
                   label=f'Current Price: ${current_price:.2f}')
        
        # 设置价格图属性
        ax1.set_title(f'{symbol} Probabilistic Price Forecast (Next {self.prediction_days} Days)', 
                     fontsize=16, fontweight='bold')
        ax1.set_ylabel('Price (USD)', fontsize=12)
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)
        
        # === 成交量预测图 ===
        # 历史成交量
        hist_volume = historical_data['volume'] / 1e6  # 转换为百万
        ax2.bar(hist_times, hist_volume, color='lightblue', alpha=0.7, width=1, label='Historical Volume')
        
        # 预测成交量
        mean_volume_pred = prediction_result['high_sample_pred']['volume'].values / 1e6
        ax2.bar(future_times, mean_volume_pred, color='orange', alpha=0.8, width=1, label='Mean Forecasted Volume')
        
        # 设置成交量图属性
        ax2.set_ylabel('Volume (Millions)', fontsize=12)
        ax2.set_xlabel('Time (UTC)', fontsize=12)
        ax2.legend(loc='upper left')
        ax2.grid(True, alpha=0.3)
        
        # 整体布局调整
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f'{symbol}_probabilistic_forecast_{timestamp}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"{symbol}: 概率性预测图已保存 - {filename}")
        return filename

    def rank_stocks_probabilistically(self, stock_symbols: List[str]) -> pd.DataFrame:
        """
        基于概率性预测进行股票排序
        """
        logger.info(f"开始概率性排序分析 {len(stock_symbols)} 只股票")
        
        results = []
        
        for symbol in stock_symbols:
            logger.info(f"处理股票: {symbol}")
            
            # 1. 加载数据
            df = self.load_stock_data(symbol)
            if df is None:
                results.append({'symbol': symbol, 'valid': False, 'error': '数据加载失败'})
                continue
            
            # 2. 概率性预测
            prediction = self.probabilistic_predict(symbol, df)
            if not prediction.get('success', False):
                results.append({'symbol': symbol, 'valid': False, 'error': prediction.get('error', '预测失败')})
                continue
            
            # 保存完整预测结果
            self.probabilistic_predictions[symbol] = prediction
            
            # 3. 计算概率性评分
            scores = self.calculate_probabilistic_scores(prediction)
            if scores.get('valid', False):
                results.append(scores)
                
                # 4. 生成可视化（仅为前2名股票生成，节省时间）
                if len([r for r in results if r.get('valid', False)]) <= 2:
                    try:
                        self.create_btc_style_visualization(symbol, prediction)
                    except Exception as e:
                        logger.warning(f"{symbol}: 可视化生成失败 - {str(e)}")
            else:
                results.append({'symbol': symbol, 'valid': False, 'error': '评分计算失败'})
        
        # 转换为DataFrame并排序
        df_results = pd.DataFrame(results)
        valid_results = df_results[df_results['valid'] == True].copy()
        
        if len(valid_results) > 0:
            valid_results = valid_results.sort_values('composite_score', ascending=False)
            valid_results['rank'] = range(1, len(valid_results) + 1)
            valid_results = valid_results.reset_index(drop=True)
        
        return valid_results

    def generate_probabilistic_report(self, ranked_df: pd.DataFrame) -> str:
        """
        生成概率性排序报告
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report = []
        report.append("=" * 90)
        report.append(f"Kronos概率性股票排序系统报告")
        report.append(f"生成时间: {timestamp}")
        report.append(f"预测方法: {self.n_ensemble}次集成预测 + 高采样预测")
        report.append(f"置信水平: 95%")
        report.append("=" * 90)
        
        if len(ranked_df) == 0:
            report.append("❌ 没有有效的预测结果")
            return "\n".join(report)
        
        report.append(f"\n📊 概率性排序结果 (共{len(ranked_df)}只股票)")
        report.append("-" * 90)
        report.append(f"{'排名':>4} {'股票':>6} {'当前价格':>10} {'预期收益':>10} {'收益标准差':>10} {'方向一致性':>10} {'综合评分':>10}")
        report.append("-" * 90)
        
        for _, row in ranked_df.iterrows():
            report.append(f"{row['rank']:>4} {row['symbol']:>6} "
                         f"${row['current_price']:>9.2f} {row['mean_return']*100:>9.2f}% "
                         f"{row['return_std']*100:>9.2f}% {row['direction_consistency']*100:>9.1f}% "
                         f"{row['composite_score']:>9.3f}")
        
        # 稳定性分析
        report.append(f"\n🛡️  预测稳定性分析")
        report.append("-" * 50)
        
        for _, row in ranked_df.iterrows():
            symbol = row['symbol']
            stability_grade = "高" if row['return_std'] < 0.01 else "中" if row['return_std'] < 0.03 else "低"
            confidence_grade = "高" if row['direction_consistency'] > 0.8 else "中" if row['direction_consistency'] > 0.6 else "低"
            
            report.append(f"{symbol}: 稳定性-{stability_grade}, 置信度-{confidence_grade}")
        
        # 交易建议（基于稳定性）
        report.append(f"\n📈 稳健交易建议")
        report.append("-" * 50)
        
        # 高置信度的买入推荐
        high_confidence = ranked_df[
            (ranked_df['direction_consistency'] > 0.7) & 
            (ranked_df['mean_return'] > 0) &
            (ranked_df['return_std'] < 0.02)
        ]
        
        if len(high_confidence) > 0:
            report.append("🟢 高置信度买入推荐:")
            for _, row in high_confidence.iterrows():
                report.append(f"   {row['symbol']}: 预期收益{row['mean_return']*100:+.2f}%±{row['return_std']*100:.2f}% "
                             f"(置信度{row['direction_consistency']*100:.1f}%)")
        else:
            report.append("🟡 当前无高置信度买入机会")
        
        # 高风险警告
        high_risk = ranked_df[
            (ranked_df['direction_consistency'] < 0.6) | 
            (ranked_df['return_std'] > 0.03)
        ]
        
        if len(high_risk) > 0:
            report.append("\n🔴 高不确定性股票:")
            for _, row in high_risk.iterrows():
                report.append(f"   {row['symbol']}: 预测不稳定，建议谨慎或避免")
        
        # 总体市场评估
        report.append(f"\n🌍 总体市场评估")
        report.append("-" * 40)
        
        avg_return = ranked_df['mean_return'].mean()
        avg_confidence = ranked_df['direction_consistency'].mean()
        avg_stability = ranked_df['return_std'].mean()
        
        market_sentiment = "乐观" if avg_return > 0.005 else "谨慎" if avg_return > -0.005 else "悲观"
        overall_confidence = "高" if avg_confidence > 0.75 else "中" if avg_confidence > 0.6 else "低"
        overall_stability = "稳定" if avg_stability < 0.02 else "波动"
        
        report.append(f"市场情绪: {market_sentiment} (平均预期收益{avg_return*100:+.2f}%)")
        report.append(f"预测信心: {overall_confidence} (平均方向一致性{avg_confidence*100:.1f}%)")
        report.append(f"市场稳定性: {overall_stability} (平均波动性{avg_stability*100:.2f}%)")
        
        report.append("\n" + "=" * 90)
        report.append("💡 关键改进: 本报告基于概率性预测，提供了预测不确定性信息")
        report.append("⚠️  投资建议: 优先选择高置信度、低不确定性的投资机会")
        report.append("=" * 90)
        
        return "\n".join(report)

def main():
    """主程序"""
    print("🎲 Kronos概率性股票排序系统启动")
    print("=" * 60)
    
    # 优化后的配置
    config = {
        'data_path': '../../data/us',
        'lookback_days': 150,
        'prediction_days': 5,
        'n_ensemble': 8,  # 8次集成预测（平衡精度和速度）
        'device': 'cpu'
    }
    
    # 测试股票池
    stock_pool = ['AAPL', 'NVDA', 'TSLA', 'MSFT', 'SPY']
    
    print(f"📋 股票池: {', '.join(stock_pool)}")
    print(f"🎯 概率性预测: {config['n_ensemble']}次集成 + 高采样预测")
    print(f"📊 可视化: 类似BTC官方图的置信区间显示")
    print("-" * 60)
    
    try:
        # 初始化概率性排序系统
        prob_system = ProbabilisticRankingSystem(**config)
        
        # 执行概率性排序
        ranked_results = prob_system.rank_stocks_probabilistically(stock_pool)
        
        # 生成报告
        report = prob_system.generate_probabilistic_report(ranked_results)
        print(report)
        
        # 保存报告和数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        report_file = f"probabilistic_ranking_report_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        if len(ranked_results) > 0:
            csv_file = f"probabilistic_ranking_data_{timestamp}.csv"
            ranked_results.to_csv(csv_file, index=False)
            print(f"\n📁 详细报告: {report_file}")
            print(f"📁 排序数据: {csv_file}")
        
        print(f"🎉 概率性排序分析完成!")
        
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        print(f"❌ 程序执行失败: {str(e)}")

if __name__ == "__main__":
    main()