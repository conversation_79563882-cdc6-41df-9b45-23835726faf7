#!/usr/bin/env python3
"""
Kronos模型对比系统
比较Kronos-small和Kronos-base模型在相同股票池上的预测表现差异

主要功能：
1. 使用相同数据对两个模型进行预测
2. 计算预测结果的差异统计
3. 比较排序结果的一致性
4. 分析模型性能和特点差异

作者：基于Kronos项目
日期：2025-08-22
"""

import os
import pandas as pd
import numpy as np
import sys
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import logging
import warnings
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append("../")
from model import Kronos, KronosTokenizer, KronosPredictor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ModelComparison:
    """Kronos模型对比系统"""
    
    def __init__(self, 
                 data_path: str = "../../data/us",
                 lookback_days: int = 150,
                 prediction_days: int = 5,
                 device: str = "cpu"):
        """
        初始化模型对比系统
        """
        self.data_path = data_path
        self.lookback_days = lookback_days
        self.prediction_days = prediction_days
        self.device = device
        
        # 模型组件
        self.tokenizer = None
        self.model_small = None
        self.model_base = None
        self.predictor_small = None
        self.predictor_base = None
        
        # 结果存储
        self.predictions_small = {}
        self.predictions_base = {}
        self.comparison_results = {}
        
        logger.info(f"初始化模型对比系统 - 回望:{lookback_days}天, 预测:{prediction_days}天")

    def load_models(self):
        """加载两个Kronos模型"""
        logger.info("开始加载Kronos模型...")
        
        start_time = time.time()
        
        # 加载tokenizer（两个模型共用）
        logger.info("加载Tokenizer...")
        self.tokenizer = KronosTokenizer.from_pretrained("NeoQuasar/Kronos-Tokenizer-base")
        
        # 加载small模型
        logger.info("加载Kronos-small模型...")
        self.model_small = Kronos.from_pretrained("NeoQuasar/Kronos-small")
        self.predictor_small = KronosPredictor(
            model=self.model_small,
            tokenizer=self.tokenizer,
            device=self.device,
            max_context=512
        )
        
        # 加载base模型
        logger.info("加载Kronos-base模型...")
        self.model_base = Kronos.from_pretrained("NeoQuasar/Kronos-base")
        self.predictor_base = KronosPredictor(
            model=self.model_base,
            tokenizer=self.tokenizer,
            device=self.device,
            max_context=512
        )
        
        load_time = time.time() - start_time
        logger.info(f"模型加载完成，耗时: {load_time:.2f}秒")

    def load_stock_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """加载股票数据"""
        try:
            file_path = os.path.join(self.data_path, symbol, "1d.parquet")
            if not os.path.exists(file_path):
                logger.warning(f"{symbol}: 数据文件不存在")
                return None
            
            df = pd.read_parquet(file_path)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df['amount'] = df['volume'] * df['close']
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            if len(df) < self.lookback_days + 20:
                logger.warning(f"{symbol}: 数据量不足")
                return None
            
            return df
            
        except Exception as e:
            logger.error(f"{symbol}: 数据加载失败 - {str(e)}")
            return None

    def predict_with_model(self, symbol: str, df: pd.DataFrame, model_name: str) -> Optional[Dict]:
        """使用指定模型进行预测"""
        try:
            # 选择预测器
            if model_name == "small":
                predictor = self.predictor_small
            elif model_name == "base":
                predictor = self.predictor_base
            else:
                raise ValueError(f"未知模型: {model_name}")
            
            # 准备数据
            end_idx = len(df) - 1
            start_idx = max(0, end_idx - self.lookback_days + 1)
            historical_data = df.iloc[start_idx:end_idx+1]
            current_price = historical_data['close'].iloc[-1]
            
            x_df = historical_data[['open', 'high', 'low', 'close', 'volume', 'amount']]
            x_timestamp = historical_data['timestamp']
            
            # 生成未来时间戳
            last_time = x_timestamp.iloc[-1]
            future_timestamps = [last_time + timedelta(days=i) for i in range(1, self.prediction_days + 1)]
            y_timestamp = pd.Series(future_timestamps)
            
            # 预测
            start_time = time.time()
            pred_df = predictor.predict(
                df=x_df,
                x_timestamp=x_timestamp,
                y_timestamp=y_timestamp,
                pred_len=self.prediction_days,
                T=0.8,
                top_p=0.9,
                sample_count=2,  # 增加采样以提高稳定性
                verbose=False
            )
            prediction_time = time.time() - start_time
            
            # 计算统计指标
            first_pred_price = pred_df['close'].iloc[0]
            last_pred_price = pred_df['close'].iloc[-1]
            avg_pred_price = pred_df['close'].mean()
            max_pred_price = pred_df['close'].max()
            min_pred_price = pred_df['close'].min()
            pred_volatility = pred_df['close'].std()
            
            # 收益率计算
            total_return = (last_pred_price - current_price) / current_price
            avg_return = (avg_pred_price - current_price) / current_price
            daily_returns = pred_df['close'].pct_change().dropna()
            avg_daily_return = daily_returns.mean()
            
            result = {
                'symbol': symbol,
                'model': model_name,
                'current_price': current_price,
                'first_pred_price': first_pred_price,
                'last_pred_price': last_pred_price,
                'avg_pred_price': avg_pred_price,
                'max_pred_price': max_pred_price,
                'min_pred_price': min_pred_price,
                'pred_volatility': pred_volatility,
                'total_return': total_return,
                'avg_return': avg_return,
                'avg_daily_return': avg_daily_return,
                'prediction_time': prediction_time,
                'prediction_data': pred_df,
                'success': True
            }
            
            logger.info(f"{symbol} ({model_name}): 预测完成 - 预期收益 {avg_return*100:.2f}%, 耗时 {prediction_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"{symbol} ({model_name}): 预测失败 - {str(e)}")
            return {'symbol': symbol, 'model': model_name, 'success': False, 'error': str(e)}

    def compare_stock_predictions(self, symbol: str, df: pd.DataFrame) -> Dict:
        """对单只股票的两个模型预测结果进行对比"""
        logger.info(f"对比股票 {symbol} 的两个模型预测...")
        
        # 分别用两个模型预测
        pred_small = self.predict_with_model(symbol, df, "small")
        pred_base = self.predict_with_model(symbol, df, "base")
        
        if not (pred_small.get('success') and pred_base.get('success')):
            return {
                'symbol': symbol,
                'comparison_success': False,
                'small_result': pred_small,
                'base_result': pred_base
            }
        
        # 计算预测差异
        price_diff = abs(pred_small['avg_pred_price'] - pred_base['avg_pred_price'])
        price_diff_pct = price_diff / pred_small['current_price']
        
        return_diff = abs(pred_small['avg_return'] - pred_base['avg_return'])
        
        volatility_diff = abs(pred_small['pred_volatility'] - pred_base['pred_volatility'])
        
        # 价格序列相关性
        small_prices = pred_small['prediction_data']['close'].values
        base_prices = pred_base['prediction_data']['close'].values
        price_correlation = np.corrcoef(small_prices, base_prices)[0, 1]
        
        # 预测一致性（方向）
        small_direction = 1 if pred_small['avg_return'] > 0 else -1
        base_direction = 1 if pred_base['avg_return'] > 0 else -1
        direction_agree = small_direction == base_direction
        
        comparison = {
            'symbol': symbol,
            'comparison_success': True,
            'small_result': pred_small,
            'base_result': pred_base,
            'price_diff': price_diff,
            'price_diff_pct': price_diff_pct,
            'return_diff': return_diff,
            'volatility_diff': volatility_diff,
            'price_correlation': price_correlation,
            'direction_agree': direction_agree,
            'time_diff': pred_base['prediction_time'] - pred_small['prediction_time']
        }
        
        return comparison

    def calculate_ranking_scores(self, prediction_result: Dict) -> Dict:
        """计算排序评分（与原系统保持一致）"""
        if not prediction_result.get('success', False):
            return {'symbol': prediction_result['symbol'], 'model': prediction_result['model'], 'valid': False}
        
        symbol = prediction_result['symbol']
        model = prediction_result['model']
        current_price = prediction_result['current_price']
        pred_data = prediction_result['prediction_data']
        
        # 各种收益率指标
        final_return = prediction_result['total_return']
        avg_return = prediction_result['avg_return']
        h_day_avg_return = prediction_result['avg_daily_return']
        cumulative_return = (pred_data['close'].iloc[-1] / pred_data['close'].iloc[0]) - 1
        
        # 风险指标
        return_volatility = pred_data['close'].pct_change().std()
        risk_adjusted_return = avg_return / (return_volatility + 1e-6)
        
        # 趋势强度
        price_trend = np.corrcoef(range(len(pred_data)), pred_data['close'])[0, 1]
        
        # 综合评分
        composite_score = (
            0.3 * avg_return +
            0.2 * final_return +
            0.2 * risk_adjusted_return +
            0.2 * h_day_avg_return * 10 +
            0.1 * price_trend
        )
        
        return {
            'symbol': symbol,
            'model': model,
            'valid': True,
            'current_price': current_price,
            'avg_pred_price': prediction_result['avg_pred_price'],
            'final_return': final_return,
            'avg_return': avg_return,
            'h_day_avg_return': h_day_avg_return,
            'cumulative_return': cumulative_return,
            'return_volatility': return_volatility,
            'risk_adjusted_return': risk_adjusted_return,
            'price_trend': price_trend,
            'composite_score': composite_score
        }

    def compare_models(self, stock_symbols: List[str]) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """对模型进行完整对比分析"""
        logger.info(f"开始对比分析 {len(stock_symbols)} 只股票的模型表现...")
        
        # 加载模型
        self.load_models()
        
        comparison_results = []
        small_scores = []
        base_scores = []
        
        for symbol in stock_symbols:
            logger.info(f"处理股票: {symbol}")
            
            # 加载数据
            df = self.load_stock_data(symbol)
            if df is None:
                continue
            
            # 对比预测
            comparison = self.compare_stock_predictions(symbol, df)
            if comparison.get('comparison_success'):
                comparison_results.append(comparison)
                
                # 计算评分
                small_score = self.calculate_ranking_scores(comparison['small_result'])
                base_score = self.calculate_ranking_scores(comparison['base_result'])
                
                if small_score.get('valid') and base_score.get('valid'):
                    small_scores.append(small_score)
                    base_scores.append(base_score)
        
        # 转换为DataFrame并排序
        df_small = pd.DataFrame(small_scores)
        df_base = pd.DataFrame(base_scores)
        
        if len(df_small) > 0:
            df_small = df_small.sort_values('composite_score', ascending=False).reset_index(drop=True)
            df_small['rank'] = range(1, len(df_small) + 1)
        
        if len(df_base) > 0:
            df_base = df_base.sort_values('composite_score', ascending=False).reset_index(drop=True)
            df_base['rank'] = range(1, len(df_base) + 1)
        
        # 存储对比结果
        self.comparison_results = comparison_results
        
        logger.info(f"模型对比完成，成功对比 {len(comparison_results)} 只股票")
        
        return df_small, df_base

    def generate_comparison_report(self, df_small: pd.DataFrame, df_base: pd.DataFrame) -> str:
        """生成详细的模型对比报告"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report = []
        report.append("=" * 100)
        report.append("Kronos模型对比分析报告")
        report.append(f"生成时间: {timestamp}")
        report.append(f"对比模型: Kronos-small vs Kronos-base")
        report.append(f"预测周期: {self.prediction_days}天")
        report.append("=" * 100)
        
        if len(df_small) == 0 or len(df_base) == 0:
            report.append("❌ 模型对比失败，无有效预测结果")
            return "\n".join(report)
        
        # 1. 排序结果对比
        report.append(f"\n📊 排序结果对比")
        report.append("-" * 100)
        report.append(f"{'股票':>6} {'Small排名':>8} {'Small评分':>10} {'Small收益':>10} {'Base排名':>8} {'Base评分':>10} {'Base收益':>10} {'排名变化':>8}")
        report.append("-" * 100)
        
        rank_changes = []
        for _, row_small in df_small.iterrows():
            symbol = row_small['symbol']
            row_base = df_base[df_base['symbol'] == symbol].iloc[0]
            
            rank_change = row_base['rank'] - row_small['rank']
            rank_changes.append(rank_change)
            
            report.append(f"{symbol:>6} {row_small['rank']:>8} {row_small['composite_score']:>10.3f} {row_small['avg_return']*100:>9.2f}% "
                         f"{row_base['rank']:>8} {row_base['composite_score']:>10.3f} {row_base['avg_return']*100:>9.2f}% "
                         f"{rank_change:+8}")
        
        # 2. 统计分析
        report.append(f"\n📈 统计分析")
        report.append("-" * 50)
        
        # 平均预期收益率对比
        small_avg_return = df_small['avg_return'].mean()
        base_avg_return = df_base['avg_return'].mean()
        
        report.append(f"平均预期收益率:")
        report.append(f"  Kronos-small: {small_avg_return*100:+.2f}%")
        report.append(f"  Kronos-base:  {base_avg_return*100:+.2f}%")
        report.append(f"  差异:         {(base_avg_return - small_avg_return)*100:+.2f}%")
        
        # 风险调整收益对比
        small_avg_sharpe = df_small['risk_adjusted_return'].mean()
        base_avg_sharpe = df_base['risk_adjusted_return'].mean()
        
        report.append(f"\n平均风险调整收益:")
        report.append(f"  Kronos-small: {small_avg_sharpe:.3f}")
        report.append(f"  Kronos-base:  {base_avg_sharpe:.3f}")
        report.append(f"  差异:         {base_avg_sharpe - small_avg_sharpe:+.3f}")
        
        # 排名一致性分析
        rank_correlation = np.corrcoef(df_small['rank'], df_base['rank'])[0, 1]
        avg_rank_change = np.mean(np.abs(rank_changes))
        max_rank_change = max(np.abs(rank_changes))
        
        report.append(f"\n排名一致性:")
        report.append(f"  排名相关系数: {rank_correlation:.3f}")
        report.append(f"  平均排名变化: {avg_rank_change:.1f}位")
        report.append(f"  最大排名变化: {max_rank_change}位")
        
        # 3. 预测细节对比
        report.append(f"\n🔍 预测细节对比")
        report.append("-" * 50)
        
        total_comparisons = len(self.comparison_results)
        direction_agreements = sum(1 for comp in self.comparison_results if comp.get('direction_agree', False))
        
        report.append(f"预测方向一致性: {direction_agreements}/{total_comparisons} ({direction_agreements/total_comparisons*100:.1f}%)")
        
        # 价格相关性统计
        correlations = [comp.get('price_correlation', 0) for comp in self.comparison_results]
        avg_correlation = np.mean(correlations)
        min_correlation = min(correlations)
        max_correlation = max(correlations)
        
        report.append(f"价格序列相关性:")
        report.append(f"  平均: {avg_correlation:.3f}")
        report.append(f"  范围: {min_correlation:.3f} ~ {max_correlation:.3f}")
        
        # 预测时间对比
        time_diffs = [comp.get('time_diff', 0) for comp in self.comparison_results]
        avg_time_diff = np.mean(time_diffs)
        
        report.append(f"\n预测时间对比:")
        report.append(f"  Base模型比Small模型平均慢: {avg_time_diff:.2f}秒")
        
        # 4. 个股分析亮点
        report.append(f"\n🎯 个股分析亮点")
        report.append("-" * 50)
        
        # 找出排名变化最大的股票
        best_small_stock = df_small.iloc[0]['symbol']
        best_base_stock = df_base.iloc[0]['symbol']
        
        if best_small_stock != best_base_stock:
            report.append(f"排名差异:")
            report.append(f"  Small模型最推荐: {best_small_stock}")
            report.append(f"  Base模型最推荐:  {best_base_stock}")
        else:
            report.append(f"两个模型一致推荐: {best_small_stock}")
        
        # 预测分歧最大的股票
        return_diffs = [comp.get('return_diff', 0) for comp in self.comparison_results]
        max_diff_idx = np.argmax(return_diffs)
        max_diff_stock = self.comparison_results[max_diff_idx]
        
        report.append(f"\n预测分歧最大股票: {max_diff_stock['symbol']}")
        report.append(f"  Small模型预期收益: {max_diff_stock['small_result']['avg_return']*100:+.2f}%")
        report.append(f"  Base模型预期收益:  {max_diff_stock['base_result']['avg_return']*100:+.2f}%")
        report.append(f"  收益率差异:        {max_diff_stock['return_diff']*100:.2f}%")
        
        # 5. 模型特点总结
        report.append(f"\n💡 模型特点总结")
        report.append("-" * 50)
        
        if small_avg_return > base_avg_return:
            report.append("📈 Kronos-small模型倾向于更乐观的预测")
        else:
            report.append("📈 Kronos-base模型倾向于更乐观的预测")
        
        if small_avg_sharpe > base_avg_sharpe:
            report.append("🛡️  Kronos-small模型风险调整收益更高")
        else:
            report.append("🛡️  Kronos-base模型风险调整收益更高")
        
        if rank_correlation > 0.8:
            report.append("🤝 两个模型在股票排序上高度一致")
        elif rank_correlation > 0.5:
            report.append("🤔 两个模型在股票排序上中等一致")
        else:
            report.append("⚠️  两个模型在股票排序上存在显著分歧")
        
        report.append("\n" + "=" * 100)
        report.append("💡 建议:")
        if rank_correlation > 0.7 and direction_agreements >= total_comparisons * 0.8:
            report.append("  两个模型预测较为一致，可以选择速度更快的Small模型")
        else:
            report.append("  两个模型存在差异，建议集成使用或根据具体需求选择")
        
        report.append("⚠️  免责声明: 本报告仅基于历史数据分析，不构成投资建议")
        report.append("=" * 100)
        
        return "\n".join(report)

def main():
    """主程序"""
    print("🔬 Kronos模型对比分析系统启动")
    print("=" * 60)
    
    # 配置参数
    config = {
        'data_path': '../../data/us',
        'lookback_days': 150,
        'prediction_days': 5,
        'device': 'cpu'
    }
    
    # 股票池
    stock_pool = ['AAPL', 'NVDA', 'TSLA', 'MSFT', 'SPY']
    
    print(f"📋 股票池: {', '.join(stock_pool)}")
    print(f"🔧 配置: 回望{config['lookback_days']}天, 预测{config['prediction_days']}天")
    print("-" * 60)
    
    try:
        # 初始化对比系统
        comparison_system = ModelComparison(**config)
        
        # 执行模型对比
        df_small, df_base = comparison_system.compare_models(stock_pool)
        
        # 生成对比报告
        report = comparison_system.generate_comparison_report(df_small, df_base)
        print(report)
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"model_comparison_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📁 详细报告已保存至: {report_file}")
        
        # 保存排序数据
        if len(df_small) > 0 and len(df_base) > 0:
            small_file = f"kronos_small_ranking_{timestamp}.csv"
            base_file = f"kronos_base_ranking_{timestamp}.csv"
            
            df_small.to_csv(small_file, index=False)
            df_base.to_csv(base_file, index=False)
            
            print(f"📁 Small模型排序数据: {small_file}")
            print(f"📁 Base模型排序数据: {base_file}")
        
        print("\n🎉 模型对比分析完成!")
        
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        print(f"❌ 程序执行失败: {str(e)}")

if __name__ == "__main__":
    main()