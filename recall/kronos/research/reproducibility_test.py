#!/usr/bin/env python3
"""
Kronos模型可重现性测试
分析模型预测的随机性和稳定性

主要分析：
1. 同一模型多次预测的变异性
2. 随机种子的影响
3. 采样参数对结果稳定性的影响
4. 模型预测的置信区间

作者：基于Kronos项目
日期：2025-08-23
"""

import os
import pandas as pd
import numpy as np
import sys
import torch
from datetime import datetime, timedelta
import logging
import random

# 添加项目路径
sys.path.append("../")
from model import Kronos, KronosTokenizer, KronosPredictor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ReproducibilityTest:
    """Kronos模型可重现性测试系统"""
    
    def __init__(self, data_path: str = "../../data/us"):
        self.data_path = data_path
        self.tokenizer = None
        self.model = None
        self.predictor = None
        
        logger.info("初始化可重现性测试系统")

    def load_model(self, model_name: str = "small"):
        """加载指定模型"""
        logger.info(f"加载Kronos-{model_name}模型...")
        
        self.tokenizer = KronosTokenizer.from_pretrained("NeoQuasar/Kronos-Tokenizer-base")
        
        if model_name == "small":
            self.model = Kronos.from_pretrained("NeoQuasar/Kronos-small")
        elif model_name == "base":
            self.model = Kronos.from_pretrained("NeoQuasar/Kronos-base")
        else:
            raise ValueError(f"未知模型: {model_name}")
        
        self.predictor = KronosPredictor(
            model=self.model,
            tokenizer=self.tokenizer,
            device="cpu",
            max_context=512
        )

    def set_random_seeds(self, seed: int):
        """设置所有随机种子"""
        random.seed(seed)
        np.random.seed(seed)
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)
        
        # 确保PyTorch的确定性行为
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

    def load_stock_data(self, symbol: str) -> pd.DataFrame:
        """加载股票数据"""
        file_path = os.path.join(self.data_path, symbol, "1d.parquet")
        df = pd.read_parquet(file_path)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df['amount'] = df['volume'] * df['close']
        df = df.sort_values('timestamp').reset_index(drop=True)
        return df

    def single_prediction(self, symbol: str, df: pd.DataFrame, seed: int, 
                         T: float = 0.8, top_p: float = 0.9, sample_count: int = 1) -> dict:
        """单次预测（设置种子）"""
        self.set_random_seeds(seed)
        
        lookback_days = 150
        prediction_days = 5
        
        # 准备数据
        end_idx = len(df) - 1
        start_idx = max(0, end_idx - lookback_days + 1)
        historical_data = df.iloc[start_idx:end_idx+1]
        
        x_df = historical_data[['open', 'high', 'low', 'close', 'volume', 'amount']]
        x_timestamp = historical_data['timestamp']
        
        # 生成未来时间戳
        last_time = x_timestamp.iloc[-1]
        future_timestamps = [last_time + timedelta(days=i) for i in range(1, prediction_days + 1)]
        y_timestamp = pd.Series(future_timestamps)
        
        # 预测
        pred_df = self.predictor.predict(
            df=x_df,
            x_timestamp=x_timestamp,
            y_timestamp=y_timestamp,
            pred_len=prediction_days,
            T=T,
            top_p=top_p,
            sample_count=sample_count,
            verbose=False
        )
        
        current_price = historical_data['close'].iloc[-1]
        avg_pred_price = pred_df['close'].mean()
        final_pred_price = pred_df['close'].iloc[-1]
        avg_return = (avg_pred_price - current_price) / current_price
        final_return = (final_pred_price - current_price) / current_price
        
        return {
            'seed': seed,
            'current_price': current_price,
            'avg_pred_price': avg_pred_price,
            'final_pred_price': final_pred_price,
            'avg_return': avg_return,
            'final_return': final_return,
            'pred_prices': pred_df['close'].tolist()
        }

    def multiple_predictions(self, symbol: str, n_runs: int = 10, model_name: str = "small") -> pd.DataFrame:
        """多次预测分析变异性"""
        logger.info(f"对{symbol}进行{n_runs}次预测，使用{model_name}模型")
        
        self.load_model(model_name)
        df = self.load_stock_data(symbol)
        
        results = []
        
        for i in range(n_runs):
            seed = 42 + i  # 使用不同的随机种子
            result = self.single_prediction(symbol, df, seed)
            result['run'] = i + 1
            result['symbol'] = symbol
            results.append(result)
            
            logger.info(f"Run {i+1}: 平均收益 {result['avg_return']*100:.2f}%, "
                       f"最终收益 {result['final_return']*100:.2f}%")
        
        return pd.DataFrame(results)

    def test_parameter_sensitivity(self, symbol: str) -> dict:
        """测试参数敏感性"""
        logger.info(f"测试{symbol}的参数敏感性")
        
        df = self.load_stock_data(symbol)
        
        # 测试不同的参数组合
        test_configs = [
            {'T': 0.5, 'top_p': 0.9, 'sample_count': 1, 'name': 'Conservative'},
            {'T': 0.8, 'top_p': 0.9, 'sample_count': 1, 'name': 'Default'},
            {'T': 1.0, 'top_p': 0.9, 'sample_count': 1, 'name': 'Random'},
            {'T': 0.8, 'top_p': 0.7, 'sample_count': 1, 'name': 'Focused'},
            {'T': 0.8, 'top_p': 0.95, 'sample_count': 1, 'name': 'Diverse'},
            {'T': 0.8, 'top_p': 0.9, 'sample_count': 3, 'name': 'Ensemble'},
        ]
        
        results = {}
        
        for config in test_configs:
            name = config.pop('name')
            result = self.single_prediction(symbol, df, seed=42, **config)
            result['config'] = name
            results[name] = result
            
            logger.info(f"{name}: 平均收益 {result['avg_return']*100:.2f}%")
        
        return results

    def analyze_variability(self, results_df: pd.DataFrame) -> dict:
        """分析预测变异性"""
        symbol = results_df['symbol'].iloc[0]
        
        # 基础统计
        avg_return_stats = {
            'mean': results_df['avg_return'].mean(),
            'std': results_df['avg_return'].std(),
            'min': results_df['avg_return'].min(),
            'max': results_df['avg_return'].max(),
            'range': results_df['avg_return'].max() - results_df['avg_return'].min()
        }
        
        final_return_stats = {
            'mean': results_df['final_return'].mean(),
            'std': results_df['final_return'].std(),
            'min': results_df['final_return'].min(),
            'max': results_df['final_return'].max(),
            'range': results_df['final_return'].max() - results_df['final_return'].min()
        }
        
        # 价格变异性
        all_pred_prices = []
        for prices in results_df['pred_prices']:
            all_pred_prices.extend(prices)
        
        price_variability = {
            'mean': np.mean(all_pred_prices),
            'std': np.std(all_pred_prices),
            'cv': np.std(all_pred_prices) / np.mean(all_pred_prices)  # 变异系数
        }
        
        # 方向一致性
        positive_returns = (results_df['avg_return'] > 0).sum()
        direction_consistency = positive_returns / len(results_df)
        
        return {
            'symbol': symbol,
            'n_runs': len(results_df),
            'avg_return_stats': avg_return_stats,
            'final_return_stats': final_return_stats,
            'price_variability': price_variability,
            'direction_consistency': direction_consistency
        }

    def generate_report(self, variability_analysis: dict, parameter_results: dict) -> str:
        """生成详细报告"""
        symbol = variability_analysis['symbol']
        
        report = []
        report.append("=" * 80)
        report.append(f"Kronos模型可重现性分析报告 - {symbol}")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("=" * 80)
        
        # 变异性分析
        report.append(f"\n📊 预测变异性分析 ({variability_analysis['n_runs']}次运行)")
        report.append("-" * 60)
        
        avg_stats = variability_analysis['avg_return_stats']
        report.append(f"平均预期收益率:")
        report.append(f"  均值:     {avg_stats['mean']*100:+.2f}%")
        report.append(f"  标准差:   {avg_stats['std']*100:.2f}%")
        report.append(f"  范围:     {avg_stats['min']*100:+.2f}% ~ {avg_stats['max']*100:+.2f}%")
        report.append(f"  极差:     {avg_stats['range']*100:.2f}%")
        
        final_stats = variability_analysis['final_return_stats']
        report.append(f"\n最终预期收益率:")
        report.append(f"  均值:     {final_stats['mean']*100:+.2f}%")
        report.append(f"  标准差:   {final_stats['std']*100:.2f}%")
        report.append(f"  范围:     {final_stats['min']*100:+.2f}% ~ {final_stats['max']*100:+.2f}%")
        report.append(f"  极差:     {final_stats['range']*100:.2f}%")
        
        direction_pct = variability_analysis['direction_consistency'] * 100
        report.append(f"\n方向一致性: {direction_pct:.1f}% (看涨比例)")
        
        price_var = variability_analysis['price_variability']
        report.append(f"价格预测变异系数: {price_var['cv']:.3f}")
        
        # 参数敏感性分析
        report.append(f"\n🔧 参数敏感性分析")
        report.append("-" * 60)
        
        for config_name, result in parameter_results.items():
            report.append(f"{config_name:>12}: 平均收益 {result['avg_return']*100:+.2f}%, "
                         f"最终收益 {result['final_return']*100:+.2f}%")
        
        # 稳定性评估
        report.append(f"\n📈 稳定性评估")
        report.append("-" * 40)
        
        if avg_stats['std'] < 0.005:  # 标准差小于0.5%
            stability = "高稳定性"
        elif avg_stats['std'] < 0.015:  # 标准差小于1.5%
            stability = "中等稳定性"
        else:
            stability = "低稳定性"
        
        report.append(f"整体稳定性评级: {stability}")
        
        if direction_pct > 80:
            direction_stability = "方向高度一致"
        elif direction_pct > 60:
            direction_stability = "方向较为一致"
        else:
            direction_stability = "方向存在分歧"
        
        report.append(f"方向稳定性: {direction_stability}")
        
        # 建议
        report.append(f"\n💡 建议")
        report.append("-" * 30)
        
        if avg_stats['std'] > 0.01:  # 标准差超过1%
            report.append("⚠️  预测变异性较大，建议:")
            report.append("   1. 增加sample_count进行集成预测")
            report.append("   2. 降低温度参数T以减少随机性")
            report.append("   3. 使用多次预测的平均值")
        else:
            report.append("✅ 预测相对稳定，当前参数配置合理")
        
        if direction_pct < 70:
            report.append("⚠️  方向预测不够稳定，需要谨慎使用")
        
        report.append("\n" + "=" * 80)
        
        return "\n".join(report)

def main():
    """主程序"""
    print("🔬 Kronos模型可重现性测试启动")
    print("=" * 50)
    
    # 测试配置
    test_symbol = "SPY"  # 选择一个有代表性的股票
    n_runs = 8  # 减少运行次数以节省时间
    model_name = "small"
    
    print(f"📋 测试股票: {test_symbol}")
    print(f"🔧 运行次数: {n_runs}")
    print(f"🤖 使用模型: Kronos-{model_name}")
    print("-" * 50)
    
    try:
        # 初始化测试系统
        test_system = ReproducibilityTest()
        
        # 1. 多次预测变异性测试
        print("🎲 执行多次预测变异性测试...")
        results_df = test_system.multiple_predictions(test_symbol, n_runs, model_name)
        
        # 2. 参数敏感性测试
        print("\n⚙️  执行参数敏感性测试...")
        param_results = test_system.test_parameter_sensitivity(test_symbol)
        
        # 3. 分析变异性
        print("\n📊 分析预测变异性...")
        variability_analysis = test_system.analyze_variability(results_df)
        
        # 4. 生成报告
        report = test_system.generate_report(variability_analysis, param_results)
        print("\n" + report)
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存报告
        report_file = f"reproducibility_report_{test_symbol}_{model_name}_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # 保存详细数据
        data_file = f"reproducibility_data_{test_symbol}_{model_name}_{timestamp}.csv"
        results_df.to_csv(data_file, index=False)
        
        print(f"\n📁 报告已保存至: {report_file}")
        print(f"📁 详细数据已保存至: {data_file}")
        
        print("\n🎉 可重现性测试完成!")
        
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        print(f"❌ 测试失败: {str(e)}")

if __name__ == "__main__":
    main()