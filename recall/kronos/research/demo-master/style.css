/* --- Global Styles & Variables --- */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --light-color: #ecf0f1;
    --dark-color: #34495e;
    --background-color: #f4f7f6;
    --card-bg-color: #ffffff;
    --text-color: #333;
    --subtle-text-color: #6c757d;
    --border-color: #e9ecef;
}

body {
    font-family: 'Inter', 'Lato', sans-serif;
    line-height: 1.7;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-color);
}

.container {
    max-width: 960px;
    margin: auto;
    padding: 0 1.5rem;
}

h1, h2, h3 {
    font-family: 'Roboto Slab', serif;
    color: var(--dark-color);
    line-height: 1.3;
}

h1 { font-size: 2.8rem; }
h2 { font-size: 2.2rem; border-bottom: 3px solid var(--border-color); padding-bottom: 0.6rem; margin-bottom: 1.5rem; }
h3 { font-size: 1.4rem; color: var(--secondary-color); }

p {
    margin-bottom: 1rem;
}

/* --- Header --- */
header {
    background: var(--secondary-color);
    color: var(--light-color);
    padding: 2.5rem 0;
    text-align: center;
    border-bottom: 5px solid var(--primary-color);
}

header .project-title {
    margin: 0 0 0.5rem 0;
    color: var(--light-color);
    font-size: 2.8rem;
}

header .subtitle {
    font-size: 1.25rem;
    color: #bdc3c7;
    margin-bottom: 2rem;
    font-style: italic;
}

/* UPDATED: Style for the GitHub link to ensure high visibility in all states */
header .repo-link,
header .repo-link:link,
header .repo-link:visited {
    color: var(--light-color); /* Ensures normal and visited links are light-colored */
    background-color: transparent;
    border: 2px solid var(--primary-color);
    padding: 0.6rem 1.5rem;
    border-radius: 50px; /* Pill shape */
    text-decoration: none;
    font-weight: 600;
    transition: background-color 0.3s, color 0.3s;
}

header .repo-link:hover,
header .repo-link:active {
    background-color: var(--primary-color);
    color: var(--card-bg-color);
}


/* --- Main Content & Sections --- */
main {
    margin: 2.5rem auto;
}

section {
    background: var(--card-bg-color);
    margin-bottom: 2.5rem;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.07);
}

/* --- Dashboard Section --- */
#live-dashboard .metadata {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    font-style: italic;
    color: var(--subtle-text-color);
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.metrics-container {
    display: flex;
    justify-content: space-around;
    gap: 1.5rem;
    text-align: center;
    margin-bottom: 2rem;
}

.metric-card {
    flex: 1;
    min-width: 220px;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: transform 0.2s, box-shadow 0.2s;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.metric-card h3 {
    margin-top: 0;
    font-size: 1.1rem;
}

.metric-value {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0.5rem 0;
}

.metric-desc {
    font-size: 0.9rem;
    color: var(--subtle-text-color);
}

.chart-wrapper p {
    max-width: 80ch;
}

.chart-container {
    width: 100%;
    text-align: center;
    margin-top: 1rem;
}

.chart-img {
    max-width: 100%;
    height: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

/* --- Methodology & About Sections --- */
#methodology ul {
    list-style-type: disc;
    padding-left: 20px;
}

#methodology li {
    margin-bottom: 0.75rem;
}

.project-links {
    margin-top: 1.5rem;
    display: flex;
    gap: 1rem;
}

.btn {
    text-decoration: none;
    padding: 0.8rem 1.8rem;
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
}
.btn-primary {
    background-color: var(--primary-color);
    color: white;
}
.btn-primary:hover {
    background-color: #2980b9;
}
.btn-secondary {
    background-color: var(--border-color);
    color: var(--dark-color);
}
.btn-secondary:hover {
    background-color: #dcdfe2;
}
.btn.disabled {
    background-color: #e0e0e0;
    color: #a0a0a0;
    cursor: not-allowed;
    pointer-events: none;
}


/* --- Footer --- */
footer {
    text-align: center;
    padding: 2rem 0;
    background: var(--dark-color);
    color: var(--light-color);
    font-size: 0.9rem;
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
    header .project-title { font-size: 2.2rem; }
    h2 { font-size: 1.8rem; }
    .container { padding: 0 1rem; }

    .metrics-container {
        flex-direction: column;
    }
    #live-dashboard .metadata {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}