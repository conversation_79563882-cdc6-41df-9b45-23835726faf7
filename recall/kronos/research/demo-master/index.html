<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kronos Live Forecast | BTC/USDT</title>
    <link rel="icon" type="image/png" href="img/logo.png">
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&family=Roboto+Slab:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <div class="container">
            <h1 class="project-title">Kronos: Live Probabilistic Forecast</h1>
            <p class="subtitle">A Demo of "Kronos: A Foundation Model for the Language of Financial Markets"</p>
            <a href="https://github.com/shiyu-coder/Kronos" target="_blank" class="repo-link">View Project on GitHub</a>
        </div>
    </header>

    <main class="container">
        <!-- Section 1: The Live Dashboard -->
        <section id="live-dashboard">
            <h2>Live BTC/USDT Forecast Dashboard</h2>
            <div class="metadata">
                <span>Last Updated (UTC): <strong id="update-time">2025-08-22 22:03:00</strong></span>
                <span>Data Source: Binance | Interval: 1-Hour</span>
            </div>

            <div class="metrics-container">
                <div class="metric-card">
                    <h3>Upside Probability (Next 24h)</h3>
                    <p class="metric-value" id="upside-prob">16.7%</p>
                    <div class="metric-desc">The model's confidence that the price in 24 hours will be higher than the last known price.</div>
                </div>
                <div class="metric-card">
                    <h3>Volatility Amplification (Next 24h)</h3>
                    <p class="metric-value" id="vol-amp-prob">0.0%</p>
                    <div class="metric-desc">The probability that predicted volatility over the next 24h will exceed recent historical volatility.</div>
                </div>
            </div>

            <div class="chart-wrapper">
                <h3>24-Hour Probabilistic Forecast</h3>
                <p>The chart below shows the historical price (blue) and the probabilistic forecast (orange). The orange line is the mean of multiple Monte Carlo simulations, and the shaded area represents the full range of predicted outcomes, indicating forecast uncertainty.</p>
                <div class="chart-container">
                    <img src="prediction_chart.png" alt="BTC/USDT Price and Volume Forecast Chart" class="chart-img">
                </div>
            </div>
        </section>

        <!-- Section 2: Methodology -->
        <section id="methodology">
            <h2>Methodology Overview</h2>
            <p>This demo showcases the forecasting results of <strong>Kronos</strong>, a foundation model pre-trained on the "language" of financial markets. The predictions are generated using the following process:</p>
            <ul>
                <li><strong>Model:</strong> The `Kronos-small` (25M parameters) model is used to autoregressively predict future K-line data.</li>
                <li><strong>Data Context:</strong> The model uses the last 360 hours (~15 days) of BTC/USDT 1h K-line data from Binance as context for each new prediction.</li>
                <li><strong>Probabilistic Forecasting:</strong> We employ Monte Carlo sampling (N=30 paths) to generate a distribution of possible future price trajectories, not just a single point forecast.</li>
                <li><strong>Derived Insights:</strong> The resulting distribution is analyzed to produce the mean forecast (solid line), the uncertainty range (shaded area), and the key probability metrics shown above.</li>
            </ul>
        </section>

        <!-- Section 3: About the Project -->
        <section id="about-project">
            <h2>About The Kronos Project</h2>
            <p>Kronos is the first open-source foundation model for financial candlesticks (K-lines), trained on data from over 45 global exchanges. It is designed to serve as a unified model for diverse quantitative finance tasks. </p>
            <div class="project-links">
                <a href="https://github.com/shiyu-coder/Kronos" class="btn btn-primary" target="_blank">View on GitHub</a>
                <a href="https://arxiv.org/abs/2508.02739" class="btn btn-secondary" target="_blank">Read the Paper</a>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2025 The Kronos Project. Licensed under the MIT License.</p>
        </div>
    </footer>
</body>
</html>