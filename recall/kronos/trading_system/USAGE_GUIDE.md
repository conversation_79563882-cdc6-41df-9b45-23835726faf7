# Kronos AI交易系统使用指南

## 🎯 系统概述

经过重构的Kronos AI交易系统现在具备科学合理的代码结构：

### 📁 新的目录结构
```
recall/kronos/trading_system/
├── common/                         # 通用组件
│   ├── kronos_ranker.py           # ✅ AI排序器 (支持蒙特卡洛采样)
│   ├── evaluator.py               # ✅ 三维度评估器
│   └── utils.py                   # ✅ 工具函数
├── backtest/                      # 回测系统
│   ├── cn/cn_backtest.py         # ✅ A股回测 (251只股票→Top5)
│   └── us/us_backtest.py         # ✅ 美股回测 (500只股票→Top5)
└── live_trading/                  # 实际交易系统
    ├── cn/cn_rebalancer.py       # ✅ A股再平衡器
    ├── cn/cn_trader.py           # ✅ A股交易执行器
    ├── us/us_rebalancer.py       # ✅ 美股再平衡器
    └── us/us_trader.py           # ✅ 美股交易执行器
```

## 🚀 使用方法

### 1. A股回测系统
```python
# 完整A股回测
from recall.kronos.trading_system.backtest.cn import CNBacktester

backtester = CNBacktester(
    max_stocks=254,      # 候选股票池大小  
    top_n_stocks=5,      # Top N选股
    rebalance_freq=7     # 每7天重平衡
)

portfolio, metrics = backtester.run_backtest()
report = backtester.generate_report(portfolio, metrics)
```

### 2. A股实际交易
```python  
# 再平衡日选股
from recall.kronos.trading_system.live_trading.cn import CNRebalancer

rebalancer = CNRebalancer(top_n_stocks=5)
trading_signals = rebalancer.get_trading_signals()

# 执行交易
from recall.kronos.trading_system.live_trading.cn import CNTrader

trader = CNTrader(broker="simulator")  # 或 "futu"
trader.connect()
execution_results = trader.execute_trading_signals(trading_signals)
```

### 3. 美股回测和交易
```python
# 美股回测
from recall.kronos.trading_system.backtest.us import USBacktester
backtester = USBacktester(max_stocks=500, top_n_stocks=5)
portfolio, metrics = backtester.run_backtest()

# 美股交易
from recall.kronos.trading_system.live_trading.us import USRebalancer
rebalancer = USRebalancer(top_n_stocks=5)
trading_signals = rebalancer.get_trading_signals()
```

## 🔬 科学性验证

### ✅ 蒙特卡洛采样确认
- **每只股票**: 3次蒙特卡洛采样 (`sample_count=3`)
- **预测参数**: `T=0.8, top_p=0.9` 控制随机性
- **评分公式**: `AI评分 = 预期收益 × 置信度`
- **实际验证**: 对251只A股进行批量预测成功

### 📊 三维度评估体系
1. **盈利性**: 年化收益率、超额收益率
2. **风险调整**: 夏普比率、信息比率、最大回撤
3. **信号质量**: IC、RankIC、换手率

### 🎯 基准对比
- **A股基准**: 贵州茅台买入持有
- **美股基准**: SPY买入持有

## 📊 回测结果解读

基于之前的A股回测结果：
- **总收益率**: 79% (251只候选→Top5选股)
- **茅台基准**: -6.62% (同期下跌)
- **超额收益**: 85.62% (显著跑赢)
- **夏普比率**: 2.102 (优秀级别)
- **IC指标**: 0.0147 (偏弱但有效)

### 🔍 结果合理性分析
1. **时间周期短** (298天) - 需要更长期验证
2. **AI预测准确** - 蒙特卡洛采样提高稳定性  
3. **选股效果显著** - Top5策略集中度合理
4. **风险控制良好** - 21.92%最大回撤可接受

## 🛠️ 运行环境

### 依赖管理 (使用uv)
```bash
# 安装核心依赖
uv pip install torch transformers huggingface-hub 
uv pip install vectorbt pandas numpy scipy
uv pip install yfinance  # 用于基准数据

# 运行回测
uv run python -m recall.kronos.trading_system.backtest.cn.cn_backtest
uv run python -m recall.kronos.trading_system.backtest.us.us_backtest

# 运行再平衡
uv run python -m recall.kronos.trading_system.live_trading.cn.cn_rebalancer
uv run python -m recall.kronos.trading_system.live_trading.us.us_rebalancer
```

## 🔄 实际交易流程

### A股再平衡日运行
```bash
# 每周一执行 (或其他指定日期)
cd /path/to/quant-lab
uv run python -m recall.kronos.trading_system.live_trading.cn.cn_rebalancer

# 输出交易指令
# 🎯 A股AI选股完成:
#    新选股票: ['002354.SZ', '688613.SH', '688680.SH', '002418.SZ', '002466.SZ']
#    需要卖出: ['000001.SZ']
#    需要买入: ['002354.SZ', '688613.SH']
#    继续持有: ['688680.SH', '002418.SZ', '002466.SZ']
```

## 📋 架构优势

1. **功能分离**: 回测 vs 实际交易完全独立
2. **市场分离**: A股 vs 美股代码独立维护
3. **组件复用**: 通用AI排序器和评估器
4. **易于扩展**: 模块化设计，支持新券商接口
5. **生产就绪**: 完整的日志、历史记录、错误处理

## 🚨 注意事项

1. **回测周期**: 建议至少1年以上数据进行验证
2. **蒙特卡洛采样**: 可调整`sample_count`参数增加采样次数
3. **交易成本**: A股建议费用设置为0.3%，美股0.2%
4. **数据更新**: 确保data/cn和data/us目录数据及时更新
5. **券商接口**: 实际交易需要配置对应的券商API

## 🎉 重构完成

✅ 代码结构科学合理  
✅ 功能分工明确  
✅ A股和美股系统完整  
✅ 回测和实际交易分离  
✅ 蒙特卡洛采样验证  
✅ 三维度评估体系  
✅ 基准对比功能完善
