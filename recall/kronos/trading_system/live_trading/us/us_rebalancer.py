#!/usr/bin/env python3
"""
美股再平衡器 - 实际交易系统核心

在每个再平衡日运行的股票选择和排序逻辑
用于实际交易环境，输出当天需要调仓的股票清单

主要功能：
1. 加载最新美股数据
2. 运行Kronos AI排序
3. 生成交易指令
4. 记录选股历史

作者: AI Assistant
日期: 2025-01-24
版本: v1.0 (生产版)
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Set, Optional
from datetime import datetime, timedelta
import logging
import json

# 添加项目路径
project_root = Path(__file__).parents[5]
sys.path.insert(0, str(project_root))

from recall.kronos.trading_system.common import KronosStockRanker
from recall.kronos.trading_system.common.utils import (
    load_stock_tickers, load_stock_data_from_directory
)

logger = logging.getLogger(__name__)

class USRebalancer:
    """美股再平衡器 - 用于实际交易"""
    
    def __init__(self, top_n_stocks: int = 5, max_stocks: int = 500):
        """
        初始化美股再平衡器
        
        Args:
            top_n_stocks: 选择的Top N股票数量
            max_stocks: 股票池最大大小
        """
        self.market = 'us'
        self.top_n_stocks = top_n_stocks
        self.max_stocks = max_stocks
        self.data_path = "data/us"
        
        # 初始化Kronos排序器
        self.kronos_ranker = KronosStockRanker(device="cpu")
        
        # 历史记录文件
        self.history_file = "us_rebalance_history.json"
        
        logger.info(f"✅ 美股再平衡器初始化完成 - Top{top_n_stocks}选股策略")
    
    def get_current_portfolio(self) -> Set[str]:
        """
        获取当前持仓
        从历史记录文件或券商API获取
        
        Returns:
            当前持仓股票集合
        """
        try:
            if Path(self.history_file).exists():
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                    
                if history and 'selections' in history:
                    # 获取最近一次的选股结果
                    latest_selection = history['selections'][-1]
                    return set(latest_selection['selected_stocks'])
            
            logger.info("未找到历史持仓记录，假设空仓")
            return set()
            
        except Exception as e:
            logger.error(f"获取当前持仓失败: {e}")
            return set()
    
    def run_rebalance(self, as_of_date: datetime = None) -> Dict:
        """
        执行再平衡选股
        
        Args:
            as_of_date: 基准日期，默认为当前日期
            
        Returns:
            包含交易指令的字典
        """
        if as_of_date is None:
            as_of_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        as_of_timestamp = pd.Timestamp(as_of_date)
        
        logger.info(f"🔄 美股再平衡选股开始 - {as_of_date.date()}")
        
        try:
            # 1. 加载最新美股数据
            logger.info("📊 加载最新美股数据...")
            stock_data = load_stock_data_from_directory(
                data_path=self.data_path,
                max_stocks=self.max_stocks
            )
            
            if len(stock_data) < 50:
                raise ValueError(f"可用美股数据不足: {len(stock_data)}")
            
            symbols = list(stock_data.keys())
            logger.info(f"美股候选池: {len(symbols)} 只股票")
            
            # 2. 运行Kronos AI排序
            logger.info("🤖 执行美股Kronos AI排序...")
            ranked_predictions = self.kronos_ranker.rank_stocks_large_scale(
                symbols=symbols,
                stock_data=stock_data,
                as_of_date=as_of_timestamp,
                market='us'
            )
            
            if not ranked_predictions:
                raise ValueError("AI排序无结果")
            
            # 3. 选择TopN股票
            new_selected_stocks = [symbol for symbol, _, _ in ranked_predictions[:self.top_n_stocks]]
            new_portfolio = set(new_selected_stocks)
            
            # 4. 计算交易指令
            current_portfolio = self.get_current_portfolio()
            
            stocks_to_sell = current_portfolio - new_portfolio
            stocks_to_buy = new_portfolio - current_portfolio
            stocks_to_hold = current_portfolio & new_portfolio
            
            # 5. 生成交易报告
            rebalance_result = {
                'date': as_of_date.isoformat(),
                'selected_stocks': new_selected_stocks,
                'stocks_to_sell': list(stocks_to_sell),
                'stocks_to_buy': list(stocks_to_buy),
                'stocks_to_hold': list(stocks_to_hold),
                'ai_scores': {symbol: score for symbol, score, _ in ranked_predictions[:10]},  # 保存Top10评分
                'total_candidates': len(symbols),
                'successful_predictions': len(ranked_predictions)
            }
            
            # 6. 记录选股历史
            self._save_rebalance_history(rebalance_result)
            
            # 7. 输出交易指令
            logger.info(f"🎯 美股AI选股完成:")
            logger.info(f"   新选股票: {new_selected_stocks}")
            logger.info(f"   需要卖出: {list(stocks_to_sell)}")
            logger.info(f"   需要买入: {list(stocks_to_buy)}")
            logger.info(f"   继续持有: {list(stocks_to_hold)}")
            
            return rebalance_result
            
        except Exception as e:
            logger.error(f"❌ 美股再平衡失败: {e}")
            raise
    
    def _save_rebalance_history(self, rebalance_result: Dict):
        """保存再平衡历史记录"""
        try:
            # 读取现有历史
            if Path(self.history_file).exists():
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            else:
                history = {'selections': [], 'created_at': datetime.now().isoformat()}
            
            # 添加新记录
            history['selections'].append(rebalance_result)
            history['last_updated'] = datetime.now().isoformat()
            
            # 保持最近50次记录
            if len(history['selections']) > 50:
                history['selections'] = history['selections'][-50:]
            
            # 保存到文件
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📝 再平衡历史已保存到 {self.history_file}")
            
        except Exception as e:
            logger.error(f"保存再平衡历史失败: {e}")
    
    def get_trading_signals(self, as_of_date: datetime = None) -> Dict:
        """
        获取交易信号 (用于对接交易系统)
        
        Args:
            as_of_date: 基准日期
            
        Returns:
            标准化的交易信号字典
        """
        rebalance_result = self.run_rebalance(as_of_date)
        
        # 转换为标准交易信号格式
        trading_signals = {
            'timestamp': rebalance_result['date'],
            'market': 'us',
            'strategy': 'kronos_ai_top5',
            'signals': {
                'BUY': [
                    {'symbol': symbol, 'weight': 1.0/self.top_n_stocks, 'priority': 'HIGH'}
                    for symbol in rebalance_result['stocks_to_buy']
                ],
                'SELL': [
                    {'symbol': symbol, 'weight': 0.0, 'priority': 'HIGH'}
                    for symbol in rebalance_result['stocks_to_sell']
                ],
                'HOLD': [
                    {'symbol': symbol, 'weight': 1.0/self.top_n_stocks, 'priority': 'MEDIUM'}
                    for symbol in rebalance_result['stocks_to_hold']
                ]
            },
            'metadata': {
                'total_positions': self.top_n_stocks,
                'position_size': 1.0/self.top_n_stocks,
                'ai_model': 'Kronos-AI',
                'candidates_evaluated': rebalance_result['total_candidates']
            }
        }
        
        return trading_signals
    
    def get_rebalance_history(self, days: int = 30) -> List[Dict]:
        """
        获取再平衡历史记录
        
        Args:
            days: 获取最近N天的记录
            
        Returns:
            历史记录列表
        """
        try:
            if not Path(self.history_file).exists():
                return []
            
            with open(self.history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
            
            if 'selections' not in history:
                return []
            
            # 筛选最近N天的记录
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_records = []
            
            for record in history['selections']:
                record_date = datetime.fromisoformat(record['date'])
                if record_date >= cutoff_date:
                    recent_records.append(record)
            
            return recent_records
            
        except Exception as e:
            logger.error(f"获取再平衡历史失败: {e}")
            return []

def main():
    """美股再平衡器主函数"""
    print("🔄 美股Kronos AI再平衡器")
    print("=" * 50)
    
    try:
        # 创建再平衡器
        rebalancer = USRebalancer(top_n_stocks=5)
        
        # 执行再平衡
        result = rebalancer.run_rebalance()
        
        # 输出结果
        print(f"\n✅ 再平衡完成:")
        print(f"📅 日期: {result['date']}")
        print(f"🎯 新选股票: {result['selected_stocks']}")
        print(f"🔄 需要调仓: {len(result['stocks_to_buy']) + len(result['stocks_to_sell'])} 只")
        
        if result['stocks_to_buy']:
            print(f"📈 买入: {result['stocks_to_buy']}")
        if result['stocks_to_sell']:
            print(f"📉 卖出: {result['stocks_to_sell']}")
        if result['stocks_to_hold']:
            print(f"💎 持有: {result['stocks_to_hold']}")
        
        print(f"\n📊 AI评分 (Top5):")
        for symbol, score in list(result['ai_scores'].items())[:5]:
            print(f"   {symbol}: {score:.4f}")
        
        return result
        
    except Exception as e:
        print(f"❌ 再平衡失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = main()
