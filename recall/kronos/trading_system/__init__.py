"""
Kronos AI量化交易系统

统一的A股和美股AI交易解决方案
包含回测和实际交易功能

使用示例：
```python
# A股回测
from recall.kronos.trading_system.backtest.cn import CNBacktester
backtester = CNBacktester()
portfolio, metrics = backtester.run_backtest()

# A股实际交易
from recall.kronos.trading_system.live_trading.cn import CNRebalancer
rebalancer = CNRebalancer()
signals = rebalancer.get_trading_signals()

# 美股回测  
from recall.kronos.trading_system.backtest.us import USBacktester
backtester = USBacktester()
portfolio, metrics = backtester.run_backtest()

# 美股实际交易
from recall.kronos.trading_system.live_trading.us import USRebalancer
rebalancer = USRebalancer() 
signals = rebalancer.get_trading_signals()
```

主要组件：
- common: 通用AI排序器和评估器
- backtest: A股和美股回测系统
- live_trading: A股和美股实际交易系统

作者: AI Assistant
版本: v2.0
日期: 2025-01-24
"""

# 通用组件
from .common import KronosStockRanker, BacktestEvaluator

# 回测系统
from .backtest.cn import CNBacktester
from .backtest.us import USBacktester

# 实际交易系统
from .live_trading.cn import CNRebalancer, CNTrader
from .live_trading.us import USRebalancer, USTrader

__version__ = "2.0.0"
__author__ = "AI Assistant"

# 快速访问接口
def run_cn_backtest():
    """快速运行A股回测"""
    from .backtest.cn.cn_backtest import main
    return main()

def run_us_backtest():
    """快速运行美股回测"""
    from .backtest.us.us_backtest import main
    return main()

def run_cn_rebalance():
    """快速运行A股再平衡"""
    from .live_trading.cn.cn_rebalancer import main
    return main()

def run_us_rebalance():
    """快速运行美股再平衡"""
    from .live_trading.us.us_rebalancer import main
    return main()
