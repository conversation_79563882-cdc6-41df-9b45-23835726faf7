#!/usr/bin/env python3
"""
通用工具函数

主要功能：
1. 数据处理工具
2. 时间计算工具
3. 股票池加载工具
4. 数据验证工具

作者: AI Assistant
日期: 2025-01-24
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

def load_stock_tickers(market: str) -> List[str]:
    """
    加载股票代码列表
    
    Args:
        market: 市场类型 ("us" 或 "cn")
        
    Returns:
        股票代码列表
    """
    if market == "us":
        ticker_file = "data/tickers/tickers_us.csv"
    elif market == "cn":
        ticker_file = "data/tickers/tickers_cn.csv"
    else:
        raise ValueError(f"不支持的市场类型: {market}")
    
    try:
        df = pd.read_csv(ticker_file)
        tickers = df['Ticker'].tolist()
        
        # A股特殊处理：确保使用正确的yfinance格式
        if market == "cn":
            tickers = [ticker.replace('.SH', '.SS') if ticker.endswith('.SH') else ticker 
                      for ticker in tickers]
        
        logger.info(f"加载{market.upper()}股票代码: {len(tickers)}只")
        return tickers
        
    except Exception as e:
        logger.error(f"加载{market}股票代码失败: {e}")
        return []

def load_stock_data_from_directory(data_path: str, max_stocks: int = None) -> Dict[str, pd.DataFrame]:
    """
    从目录加载股票数据
    
    Args:
        data_path: 数据目录路径
        max_stocks: 最大股票数量限制
        
    Returns:
        股票数据字典
    """
    data_dir = Path(data_path)
    if not data_dir.exists():
        raise FileNotFoundError(f"数据目录不存在: {data_dir}")
    
    stock_data = {}
    loaded_count = 0
    stock_dirs = [d for d in data_dir.iterdir() if d.is_dir()]
    total_dirs = len(stock_dirs)
    
    logger.info(f"发现 {total_dirs} 个股票目录，开始加载...")
    
    for i, stock_dir in enumerate(stock_dirs):
        if max_stocks and loaded_count >= max_stocks:
            break
            
        if i % 50 == 0:
            logger.info(f"扫描进度: {i}/{total_dirs} ({i/total_dirs*100:.1f}%)")
            
        symbol = stock_dir.name
        data_file = stock_dir / "1d.parquet"
        
        if data_file.exists():
            try:
                df = pd.read_parquet(data_file)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.sort_values('timestamp').reset_index(drop=True)
                
                # 确保有amount列
                if 'amount' not in df.columns:
                    df['amount'] = df['volume'] * df['close']
                
                # 数据质量检查
                if len(df) >= 250 and df['close'].iloc[-1] > 1.0:
                    stock_data[symbol] = df
                    loaded_count += 1
                    
            except Exception as e:
                logger.debug(f"{symbol}: 数据加载失败 - {e}")
    
    logger.info(f"✅ 数据加载完成: {len(stock_data)}只股票")
    return stock_data

def calculate_actual_returns(price_matrix: pd.DataFrame, 
                           as_of_date: pd.Timestamp, 
                           forward_days: int = 7) -> Dict[str, float]:
    """
    计算实际收益率 (用于IC计算)
    
    Args:
        price_matrix: 价格矩阵
        as_of_date: 基准日期
        forward_days: 向前计算天数
        
    Returns:
        股票实际收益率字典
    """
    try:
        current_idx = price_matrix.index.get_loc(as_of_date)
        future_idx = min(current_idx + forward_days, len(price_matrix) - 1)
        
        actual_returns = {}
        for symbol in price_matrix.columns:
            current_price = price_matrix.iloc[current_idx][symbol]
            future_price = price_matrix.iloc[future_idx][symbol]
            
            if pd.notna(current_price) and pd.notna(future_price) and current_price > 0:
                actual_returns[symbol] = (future_price - current_price) / current_price
                
        return actual_returns
        
    except:
        return {}

def create_price_matrix(stock_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
    """
    创建价格矩阵
    
    Args:
        stock_data: 股票数据字典
        
    Returns:
        价格矩阵DataFrame
    """
    # 获取所有日期
    all_dates = set()
    for df in stock_data.values():
        all_dates.update(df['timestamp'])
    all_dates = sorted(all_dates)
    
    # 构建价格矩阵
    price_data = {}
    for symbol, df in stock_data.items():
        df_indexed = df.set_index('timestamp')
        price_series = df_indexed['close'].reindex(all_dates).fillna(method='ffill')
        price_data[symbol] = price_series
    
    price_matrix = pd.DataFrame(price_data)
    price_matrix.index = pd.to_datetime(all_dates)
    price_matrix = price_matrix.dropna()
    
    return price_matrix

def validate_data_quality(df: pd.DataFrame, symbol: str) -> bool:
    """
    验证股票数据质量
    
    Args:
        df: 股票数据
        symbol: 股票代码
        
    Returns:
        是否通过验证
    """
    if df.empty or len(df) < 200:
        return False
        
    # 检查必要的列
    required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
    if not all(col in df.columns for col in required_columns):
        return False
    
    # 检查价格有效性
    if df['close'].iloc[-1] <= 0:
        return False
    
    # 检查数据完整性
    if df[required_columns].isnull().any().any():
        return False
    
    return True

def format_performance_report(metrics, market: str, benchmark_name: str = None) -> str:
    """
    格式化性能报告
    
    Args:
        metrics: 性能指标对象
        market: 市场类型
        benchmark_name: 基准名称
        
    Returns:
        格式化的报告字符串
    """
    market_name = "美股" if market == "us" else "A股"
    
    if benchmark_name is None:
        benchmark_name = "SPY" if market == "us" else "贵州茅台"
    
    report = f"""
🎯 增强版Kronos AI {market_name}三维度评估报告
{"=" * 80}

📈 【1. 策略盈利能力】
年化收益率:        {metrics.annual_return:.2f}%
年化超额收益 (vs{benchmark_name}): {metrics.annual_excess_return:.2f}%

🛡️ 【2. 风险调整后收益】
夏普比率:          {metrics.sharpe_ratio:.3f}
信息比率 (IR):     {metrics.information_ratio:.3f}
最大回撤:          {metrics.max_drawdown:.2f}%

🔍 【3. 信号质量诊断】
信息系数 (IC):     {metrics.mean_ic:.4f}
等级IC (RankIC):   {metrics.mean_rank_ic:.4f}
IC信息比率:        {metrics.ic_ir:.3f}
换手率:            {metrics.turnover_rate:.2f}

📊 【其他统计指标】
胜率:              {metrics.win_rate:.2f}%
总交易次数:        {metrics.total_trades}
平均持仓期:        {metrics.avg_holding_period:.1f}天
"""
    
    return report
