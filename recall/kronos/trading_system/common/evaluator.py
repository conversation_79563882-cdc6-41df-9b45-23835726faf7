#!/usr/bin/env python3
"""
回测性能评估器 - 通用组件
提供三维度专业评估体系：盈利性、风险调整收益、信号质量

主要功能：
1. 加载基准数据
2. 计算IC和RankIC指标
3. 计算换手率
4. 综合性能指标计算

作者: AI Assistant
日期: 2025-01-24
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Set
from datetime import datetime, timedelta
from scipy import stats
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

# 可选依赖处理
try:
    import yfinance as yf
    HAS_YFINANCE = True
except ImportError:
    HAS_YFINANCE = False
    logger.warning("yfinance not installed, benchmark loading may be limited")

try:
    import vectorbt as vbt
    HAS_VECTORBT = True
except ImportError:
    HAS_VECTORBT = False
    logger.warning("vectorbt not installed, some metrics may be unavailable")

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    # 盈利性指标
    annual_return: float
    annual_excess_return: float
    
    # 风险调整指标  
    sharpe_ratio: float
    information_ratio: float
    max_drawdown: float
    
    # 信号质量指标
    mean_ic: float
    mean_rank_ic: float
    ic_ir: float  # IC信息比率
    turnover_rate: float
    
    # 统计指标
    win_rate: float
    total_trades: int
    avg_holding_period: float

class BacktestEvaluator:
    """回测性能评估器"""
    
    def __init__(self):
        self.benchmark_data = None
        
    def load_benchmark_us(self, start_date: str, end_date: str) -> pd.DataFrame:
        """加载美股SPY基准数据"""
        logger.info("📊 加载SPY基准数据...")
        
        if not HAS_YFINANCE:
            logger.warning("yfinance未安装，使用默认基准收益")
            return pd.DataFrame()
        
        try:
            spy = yf.Ticker("SPY")
            benchmark = spy.history(start=start_date, end=end_date)
            
            if not benchmark.empty:
                benchmark = benchmark.reset_index()
                benchmark['timestamp'] = benchmark['Date']
                benchmark = benchmark[['timestamp', 'Close']].rename(columns={'Close': 'close'})
                benchmark['timestamp'] = pd.to_datetime(benchmark['timestamp'])
                benchmark['benchmark_return'] = benchmark['close'].pct_change()
                
                self.benchmark_data = benchmark
                logger.info(f"✅ SPY基准数据加载完成: {len(benchmark)}条数据")
                return benchmark
            else:
                logger.warning("⚠️ SPY数据获取失败，使用默认基准")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"❌ SPY基准数据加载失败: {e}")
            return pd.DataFrame()
    
    def load_benchmark_cn(self, start_date: str, end_date: str, 
                         benchmark_symbol: str = "600519.SS") -> pd.DataFrame:
        """
        加载A股基准数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            benchmark_symbol: 基准股票代码，默认为贵州茅台
        """
        benchmark_name = "贵州茅台" if benchmark_symbol == "600519.SS" else benchmark_symbol
        logger.info(f"📊 加载{benchmark_name}基准数据...")
        
        if not HAS_YFINANCE:
            logger.warning("yfinance未安装，使用默认基准收益")
            return pd.DataFrame()
        
        try:
            ticker = yf.Ticker(benchmark_symbol)
            benchmark = ticker.history(start=start_date, end=end_date)
            
            # 如果600519.SS失败，尝试其他格式
            if benchmark.empty and benchmark_symbol == "600519.SS":
                logger.warning("尝试不同的茅台代码格式...")
                ticker = yf.Ticker("600519.SH")
                benchmark = ticker.history(start=start_date, end=end_date)
            
            if not benchmark.empty:
                benchmark = benchmark.reset_index()
                benchmark['timestamp'] = benchmark['Date']
                benchmark = benchmark[['timestamp', 'Close']].rename(columns={'Close': 'close'})
                benchmark['timestamp'] = pd.to_datetime(benchmark['timestamp'])
                benchmark['benchmark_return'] = benchmark['close'].pct_change()
                
                self.benchmark_data = benchmark
                logger.info(f"✅ {benchmark_name}基准数据加载完成: {len(benchmark)}条数据")
                return benchmark
            else:
                logger.warning(f"⚠️ {benchmark_name}数据获取失败，使用默认基准")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"❌ {benchmark_name}基准数据加载失败: {e}")
            return pd.DataFrame()
    
    def calculate_ic_metrics(self, predictions_history: List[Dict], 
                           actual_returns_history: List[Dict]) -> Tuple[float, float, float]:
        """计算IC和RankIC指标"""
        logger.info("🔍 计算信号质量指标 (IC/RankIC)...")
        
        ic_series = []
        rank_ic_series = []
        
        for pred_dict, actual_dict in zip(predictions_history, actual_returns_history):
            # 找到共同股票
            common_symbols = set(pred_dict.keys()) & set(actual_dict.keys())
            
            if len(common_symbols) < 5:  # 至少需要5只股票计算相关性
                continue
                
            # 提取预测评分和实际收益
            pred_scores = [pred_dict[symbol] for symbol in common_symbols]
            actual_returns = [actual_dict[symbol] for symbol in common_symbols]
            
            # 计算IC (皮尔逊相关系数)
            ic, _ = stats.pearsonr(pred_scores, actual_returns)
            ic_series.append(ic)
            
            # 计算RankIC (斯皮尔曼等级相关系数)
            rank_ic, _ = stats.spearmanr(pred_scores, actual_returns)  
            rank_ic_series.append(rank_ic)
        
        mean_ic = np.mean(ic_series) if ic_series else 0
        mean_rank_ic = np.mean(rank_ic_series) if rank_ic_series else 0
        ic_ir = np.mean(ic_series) / np.std(ic_series) if ic_series and np.std(ic_series) > 0 else 0
        
        logger.info(f"IC指标 - 均值: {mean_ic:.4f}, RankIC: {mean_rank_ic:.4f}, IC_IR: {ic_ir:.4f}")
        
        return mean_ic, mean_rank_ic, ic_ir
    
    def calculate_turnover_rate(self, portfolio_history: List[Set]) -> float:
        """计算换手率"""
        if len(portfolio_history) < 2:
            return 0.0
            
        turnovers = []
        for i in range(1, len(portfolio_history)):
            prev_portfolio = portfolio_history[i-1]
            curr_portfolio = portfolio_history[i]
            
            # 计算变化的股票数量
            total_positions = len(prev_portfolio | curr_portfolio)
            unchanged_positions = len(prev_portfolio & curr_portfolio)
            
            if total_positions > 0:
                turnover = 1 - (unchanged_positions / max(len(prev_portfolio), len(curr_portfolio)))
                turnovers.append(turnover)
        
        return np.mean(turnovers) if turnovers else 0.0
    
    def calculate_comprehensive_metrics(self, portfolio, predictions_history: List[Dict], 
                                      actual_returns_history: List[Dict],
                                      portfolio_history: List[Set]) -> PerformanceMetrics:
        """计算综合评估指标"""
        logger.info("📊 计算三维度综合评估指标...")
        
        if not HAS_VECTORBT:
            raise ImportError("需要安装vectorbt: pip install vectorbt")
        
        # 基础统计
        stats = portfolio.stats()
        
        # 1. 盈利性指标
        total_return = stats.get('Total Return [%]', 0)
        period_days = stats.get('Period', timedelta(days=365)).days
        annual_return = total_return * (365 / period_days) if period_days > 0 else total_return
        
        # 计算超额收益 (相对于基准)
        if self.benchmark_data is not None:
            benchmark_annual_return = self._calculate_benchmark_return()
            annual_excess_return = annual_return - benchmark_annual_return
        else:
            # 使用默认基准收益
            default_benchmark = 10.0  # 美股假设10%，A股假设8%
            annual_excess_return = annual_return - default_benchmark
        
        # 2. 风险调整指标
        sharpe_ratio = stats.get('Sharpe Ratio', 0)
        max_drawdown = stats.get('Max Drawdown [%]', 0)
        
        # 信息比率计算
        returns = portfolio.returns()
        excess_returns = returns  # 简化处理，实际应减去基准收益
        information_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() > 0 else 0
        
        # 3. 信号质量指标
        mean_ic, mean_rank_ic, ic_ir = self.calculate_ic_metrics(predictions_history, actual_returns_history)
        turnover_rate = self.calculate_turnover_rate(portfolio_history)
        
        # 其他指标
        win_rate = stats.get('Win Rate [%]', 0)
        total_trades = stats.get('Total Trades', 0)
        
        # 平均持仓期计算
        avg_holding_period = self._calculate_avg_holding_period(portfolio)
        
        return PerformanceMetrics(
            annual_return=annual_return,
            annual_excess_return=annual_excess_return,
            sharpe_ratio=sharpe_ratio,
            information_ratio=information_ratio,
            max_drawdown=max_drawdown,
            mean_ic=mean_ic,
            mean_rank_ic=mean_rank_ic,
            ic_ir=ic_ir,
            turnover_rate=turnover_rate,
            win_rate=win_rate,
            total_trades=total_trades,
            avg_holding_period=avg_holding_period
        )
    
    def _calculate_benchmark_return(self) -> float:
        """计算基准收益率"""
        if self.benchmark_data is None or len(self.benchmark_data) < 2:
            return 10.0  # 默认基准
            
        total_return = (self.benchmark_data['close'].iloc[-1] / self.benchmark_data['close'].iloc[0] - 1) * 100
        days = (self.benchmark_data['timestamp'].iloc[-1] - self.benchmark_data['timestamp'].iloc[0]).days
        annual_return = total_return * (365 / days) if days > 0 else 0
        
        return annual_return
    
    def _calculate_avg_holding_period(self, portfolio) -> float:
        """计算平均持仓期"""
        try:
            trades = portfolio.trades.records_readable
            if len(trades) == 0:
                return 0.0
                
            holding_periods = []
            for _, trade in trades.iterrows():
                if pd.notna(trade.get('Exit Timestamp')) and pd.notna(trade.get('Entry Timestamp')):
                    duration = (trade['Exit Timestamp'] - trade['Entry Timestamp']).days
                    holding_periods.append(duration)
            
            return np.mean(holding_periods) if holding_periods else 0.0
            
        except:
            return 0.0
