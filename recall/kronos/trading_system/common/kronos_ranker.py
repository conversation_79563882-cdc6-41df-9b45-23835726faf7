#!/usr/bin/env python3
"""
Kronos AI股票排序器 - 通用组件
支持A股和美股的AI排序功能

主要功能：
1. 加载Kronos AI模型
2. 批量预测股票收益
3. 计算AI评分并排序
4. 支持蒙特卡洛采样

作者: AI Assistant
日期: 2025-01-24
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

# 添加Kronos路径
current_dir = Path(__file__).parent
project_root = current_dir.parents[3]  # 回到项目根目录
kronos_path = project_root / "recall" / "kronos"
sys.path.insert(0, str(kronos_path))
sys.path.insert(0, str(kronos_path / "model"))

class KronosStockRanker:
    """通用Kronos AI股票排序器"""
    
    def __init__(self, device: str = "cpu"):
        self.device = device
        self.tokenizer = None
        self.model = None
        self.predictor = None
        
        # 加载Kronos模型
        self._load_kronos_model()
    
    def _load_kronos_model(self):
        """加载Kronos预训练模型"""
        try:
            logger.info("🤖 加载Kronos AI模型...")
            
            # 导入Kronos模块
            from kronos import Kronos, KronosTokenizer, KronosPredictor
            
            # 从Hugging Face加载预训练模型
            logger.info("📥 从Hugging Face加载Kronos-Tokenizer-base...")
            self.tokenizer = KronosTokenizer.from_pretrained("NeoQuasar/Kronos-Tokenizer-base")
            
            logger.info("📥 从Hugging Face加载Kronos-small (99MB)...")  
            self.model = Kronos.from_pretrained("NeoQuasar/Kronos-small")
            
            # 创建预测器
            logger.info("🔧 创建Kronos预测器...")
            self.predictor = KronosPredictor(
                model=self.model,
                tokenizer=self.tokenizer, 
                device=self.device,
                max_context=512
            )
            
            logger.info("✅ Kronos AI模型加载成功!")
            
        except Exception as e:
            logger.error(f"❌ Kronos模型加载失败: {e}")
            raise RuntimeError(f"无法加载Kronos模型: {e}")
    
    def predict_stock_returns_batch(self, symbols: List[str], stock_data: Dict[str, pd.DataFrame], 
                                   as_of_date: pd.Timestamp, market: str = "us") -> Dict[str, Dict]:
        """
        批量预测股票收益
        
        Args:
            symbols: 股票代码列表
            stock_data: 股票历史数据
            as_of_date: 预测截止日期
            market: 市场类型 ("us" 或 "cn")
            
        Returns:
            预测结果字典
        """
        market_name = "美股" if market == "us" else "A股"
        logger.info(f"🚀 批量预测 {len(symbols)} 只{market_name} (截止{as_of_date.date()})")
        
        predictions = {}
        successful_count = 0
        
        progress_interval = 50 if market == "us" else 25  # A股更频繁的进度显示
        
        for i, symbol in enumerate(symbols):
            if i % progress_interval == 0:
                logger.info(f"进度: {i+1}/{len(symbols)} ({(i+1)/len(symbols)*100:.1f}%)")
            
            if symbol not in stock_data:
                continue
                
            # 预测单只股票
            pred_result = self.predict_stock_returns(symbol, stock_data[symbol], as_of_date)
            
            if pred_result and pred_result.get('success', False):
                predictions[symbol] = pred_result
                successful_count += 1
        
        logger.info(f"✅ {market_name}批量预测完成: {successful_count}/{len(symbols)} 成功")
        return predictions
    
    def predict_stock_returns(self, symbol: str, stock_data: pd.DataFrame, 
                            as_of_date: pd.Timestamp) -> Optional[Dict]:
        """
        预测单只股票收益
        
        Args:
            symbol: 股票代码
            stock_data: 股票历史数据
            as_of_date: 预测截止日期
            
        Returns:
            预测结果
        """
        try:
            # 严格使用as_of_date之前的数据
            historical_data = stock_data[stock_data['timestamp'] <= as_of_date].copy()
            
            if len(historical_data) < 200:  # 需要足够的历史数据
                return None
            
            # 准备Kronos模型需要的数据格式
            lookback_days = min(400, len(historical_data) - 10)
            pred_len = 5
            
            # 获取最近的历史数据
            recent_data = historical_data.tail(lookback_days).copy()
            
            # 确保有amount列
            if 'amount' not in recent_data.columns:
                recent_data['amount'] = recent_data['volume'] * recent_data['close']
            
            # 准备输入数据
            x_df = recent_data[['open', 'high', 'low', 'close', 'volume', 'amount']].copy()
            x_timestamp = recent_data['timestamp'].copy()
            
            # 生成未来时间戳
            last_time = x_timestamp.iloc[-1]
            future_dates = [last_time + timedelta(days=i) for i in range(1, pred_len + 1)]
            y_timestamp = pd.Series(future_dates)
            
            # 调用Kronos模型预测 (蒙特卡洛采样)
            pred_result = self.predictor.predict(
                df=x_df,
                x_timestamp=x_timestamp,
                y_timestamp=y_timestamp,
                pred_len=pred_len,
                T=0.8,
                top_p=0.9,
                sample_count=3,  # 蒙特卡洛采样次数
                verbose=False
            )
            
            # 计算预测收益率
            current_price = x_df['close'].iloc[-1]
            predicted_final_price = pred_result['close'].iloc[-1]
            predicted_return = (predicted_final_price - current_price) / current_price
            
            # 计算置信度
            pred_volatility = pred_result['close'].std() / pred_result['close'].mean()
            confidence = max(0, 1 - pred_volatility * 2)
            
            return {
                'symbol': symbol,
                'success': True,
                'current_price': current_price,
                'predicted_return': predicted_return,
                'predicted_final_price': predicted_final_price,
                'confidence': confidence,
                'prediction_horizon': pred_len,
                'model_type': 'Kronos-AI'
            }
            
        except Exception as e:
            return {'symbol': symbol, 'success': False, 'error': str(e)}
    
    def rank_stocks_large_scale(self, symbols: List[str], stock_data: Dict[str, pd.DataFrame], 
                               as_of_date: pd.Timestamp, market: str = "us") -> List[Tuple[str, float, Dict]]:
        """
        大规模股票AI排序
        
        Args:
            symbols: 股票代码列表
            stock_data: 股票历史数据
            as_of_date: 排序截止日期
            market: 市场类型 ("us" 或 "cn")
            
        Returns:
            按AI评分排序的股票列表
        """
        market_name = "美股" if market == "us" else "A股"
        logger.info(f"🎯 {market_name}大规模Kronos AI排序: {len(symbols)}只股票")
        
        # 批量预测
        predictions = self.predict_stock_returns_batch(symbols, stock_data, as_of_date, market)
        
        # 计算AI评分并排序
        scored_predictions = []
        for symbol, pred_result in predictions.items():
            # Kronos AI评分 = 预期收益 * 置信度  
            ai_score = pred_result['predicted_return'] * pred_result['confidence']
            scored_predictions.append((symbol, ai_score, pred_result))
        
        # 按评分排序
        scored_predictions.sort(key=lambda x: x[1], reverse=True)
        
        if scored_predictions:
            top_5 = [symbol for symbol, _, _ in scored_predictions[:5]]
            logger.info(f"🏆 {market_name}大规模排序Top5: {top_5}")
        
        return scored_predictions
