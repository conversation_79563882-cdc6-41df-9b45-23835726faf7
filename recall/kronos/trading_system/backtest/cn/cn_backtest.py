#!/usr/bin/env python3
"""
A股Kronos AI回测系统

基于原有的enhanced_kronos_cn_backtest.py重构
主要功能：
1. 加载A股数据 (data/cn/)
2. Kronos AI排序和选股
3. VectorBT回测执行
4. 三维度性能评估

作者: AI Assistant
日期: 2025-01-24
版本: v2.0 (重构版)
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, List
from datetime import datetime, timedelta
import logging

# 添加项目路径
project_root = Path(__file__).parents[5]
sys.path.insert(0, str(project_root))

from recall.kronos.trading_system.common import KronosStockRanker, BacktestEvaluator
from recall.kronos.trading_system.common.utils import (
    load_stock_tickers, load_stock_data_from_directory, 
    create_price_matrix, calculate_actual_returns,
    format_performance_report
)

try:
    import vectorbt as vbt
    HAS_VECTORBT = True
except ImportError:
    HAS_VECTORBT = False

logger = logging.getLogger(__name__)

class CNBacktester:
    """A股Kronos AI回测系统"""
    
    def __init__(self, max_stocks: int = 254, top_n_stocks: int = 5, 
                 rebalance_freq: int = 7):
        """
        初始化A股回测系统
        
        Args:
            max_stocks: 最大股票池大小
            top_n_stocks: Top N选股数量
            rebalance_freq: 重平衡频率(天)
        """
        self.market = 'cn'
        self.max_stocks = max_stocks
        self.top_n_stocks = top_n_stocks
        self.rebalance_freq = rebalance_freq
        self.data_path = "data/cn"
        
        # 初始化组件
        self.kronos_ranker = KronosStockRanker(device="cpu")
        self.evaluator = BacktestEvaluator()
        
        # 历史记录 (用于IC计算)
        self.predictions_history = []
        self.actual_returns_history = []
        self.portfolio_history = []
        
        logger.info(f"✅ A股Kronos回测系统初始化完成")
    
    def load_cn_stock_data(self) -> Dict[str, pd.DataFrame]:
        """加载A股数据池"""
        logger.info(f"📊 加载A股数据池 (目标: {self.max_stocks}只股票)...")
        
        stock_data = load_stock_data_from_directory(
            data_path=self.data_path,
            max_stocks=self.max_stocks
        )
        
        if len(stock_data) < 50:
            raise ValueError(f"A股数据不足: {len(stock_data)}, 至少需要50只")
        
        return stock_data
    
    def generate_trading_signals(self, stock_data: Dict[str, pd.DataFrame]) -> Dict:
        """生成A股交易信号 - 每周重平衡，TopN持仓"""
        logger.info(f"🚀 生成A股AI交易信号 (每{self.rebalance_freq}天重平衡, Top{self.top_n_stocks}持仓)...")
        
        symbols = list(stock_data.keys())
        logger.info(f"A股股票池规模: {len(symbols)} 只股票")
        
        # 创建价格矩阵
        price_matrix = create_price_matrix(stock_data)
        logger.info(f"📊 A股价格矩阵形状: {price_matrix.shape}")
        
        # 设置重平衡日期
        rebalance_dates = price_matrix.index[::self.rebalance_freq]
        logger.info(f"重平衡次数: {len(rebalance_dates)} (每{self.rebalance_freq}天调仓)")
        
        entries = pd.DataFrame(False, index=price_matrix.index, columns=price_matrix.columns)
        exits = pd.DataFrame(False, index=price_matrix.index, columns=price_matrix.columns)
        
        current_portfolio = set()
        
        for i, rebalance_date in enumerate(rebalance_dates):
            try:
                logger.info(f"🤖 [{i+1}/{len(rebalance_dates)}] A股大规模AI选股 - {rebalance_date.date()}")
                
                # A股大规模Kronos AI排序
                ranked_predictions = self.kronos_ranker.rank_stocks_large_scale(
                    symbols=symbols,
                    stock_data=stock_data,
                    as_of_date=rebalance_date - pd.Timedelta(days=1),
                    market='cn'
                )
                
                if not ranked_predictions:
                    logger.warning("A股AI排序无结果，保持当前投资组合")
                    continue
                
                # 选择TopN股票
                selected_stocks = set([symbol for symbol, _, _ in ranked_predictions[:self.top_n_stocks]])
                
                # 记录预测历史(用于IC计算)
                predictions_dict = {symbol: score for symbol, score, _ in ranked_predictions}
                self.predictions_history.append(predictions_dict)
                
                # 计算实际收益(用于IC计算)
                actual_returns = calculate_actual_returns(
                    price_matrix, rebalance_date, forward_days=self.rebalance_freq
                )
                self.actual_returns_history.append(actual_returns)
                
                # 记录投资组合历史(用于换手率计算)
                self.portfolio_history.append(selected_stocks.copy())
                
                # 生成交易信号
                stocks_to_sell = current_portfolio - selected_stocks
                stocks_to_buy = selected_stocks - current_portfolio
                
                for symbol in stocks_to_sell:
                    if symbol in exits.columns:
                        exits.loc[rebalance_date, symbol] = True
                
                for symbol in stocks_to_buy:
                    if symbol in entries.columns:
                        entries.loc[rebalance_date, symbol] = True
                
                current_portfolio = selected_stocks
                
                logger.info(f"   🎯 A股选中Top{self.top_n_stocks}: {list(selected_stocks)}")
                
            except Exception as e:
                logger.error(f"A股重平衡失败 {rebalance_date}: {e}")
        
        return {
            'entries': entries,
            'exits': exits,
            'price_matrix': price_matrix,
            'rebalance_dates': rebalance_dates
        }
    
    def run_backtest(self, init_cash: float = 1000000, fees: float = 0.003, 
                    slippage: float = 0.002) -> tuple:
        """
        运行A股完整回测
        
        Args:
            init_cash: 初始资金
            fees: 交易费用
            slippage: 滑点
            
        Returns:
            (portfolio, metrics) 投资组合和性能指标
        """
        logger.info("🚀 启动A股Kronos AI回测...")
        
        if not HAS_VECTORBT:
            raise ImportError("需要安装vectorbt: pip install vectorbt")
        
        # 1. 加载A股数据
        stock_data = self.load_cn_stock_data()
        
        # 2. 加载茅台基准数据
        self.evaluator.load_benchmark_cn(
            start_date=(datetime.now() - timedelta(days=1500)).strftime('%Y-%m-%d'),
            end_date=datetime.now().strftime('%Y-%m-%d')
        )
        
        # 3. 生成交易信号
        signals = self.generate_trading_signals(stock_data)
        
        # 4. 执行VectorBT回测
        logger.info("📊 执行A股VectorBT回测...")
        
        price_matrix = signals['price_matrix']
        entries = signals['entries']
        exits = signals['exits']
        
        # 数据对齐
        common_index = price_matrix.index.intersection(entries.index).intersection(exits.index)
        common_columns = price_matrix.columns.intersection(entries.columns).intersection(exits.columns)
        
        price_matrix = price_matrix.reindex(index=common_index, columns=common_columns)
        entries = entries.reindex(index=common_index, columns=common_columns)
        exits = exits.reindex(index=common_index, columns=common_columns)
        
        logger.info(f"A股回测数据维度: {price_matrix.shape}")
        
        # 创建投资组合
        position_size = 1.0 / self.top_n_stocks  # 等权重分配
        portfolio = vbt.Portfolio.from_signals(
            close=price_matrix,
            entries=entries,
            exits=exits,
            size=position_size,
            size_type='percent',
            fees=fees,
            slippage=slippage,
            init_cash=init_cash,
            freq='D',
            group_by=True,
            cash_sharing=True,
            call_seq='auto'
        )
        
        # 5. 计算性能指标
        logger.info("📊 计算A股三维度评估指标...")
        
        metrics = self.evaluator.calculate_comprehensive_metrics(
            portfolio=portfolio,
            predictions_history=self.predictions_history,
            actual_returns_history=self.actual_returns_history,
            portfolio_history=self.portfolio_history
        )
        
        logger.info("✅ A股回测执行完成!")
        return portfolio, metrics
    
    def generate_report(self, portfolio, metrics, save_to_file: bool = True) -> str:
        """生成A股回测报告"""
        
        report = format_performance_report(metrics, market='cn')
        
        if save_to_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"cn_kronos_backtest_{timestamp}.txt"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("A股Kronos AI回测报告\n")
                f.write("=" * 60 + "\n")
                f.write(f"股票池: {self.max_stocks}只A股\n")
                f.write(f"持仓规模: Top{self.top_n_stocks}股票\n")
                f.write(f"重平衡频率: 每{self.rebalance_freq}天调仓\n")
                f.write(f"基准对比: 贵州茅台买入持有\n")
                f.write(f"生成时间: {datetime.now()}\n\n")
                f.write(report)
                
                # VectorBT完整统计
                f.write("\nVectorBT完整统计:\n")
                f.write(str(portfolio.stats()))
            
            logger.info(f"📁 A股回测报告已保存: {report_file}")
        
        return report

def main():
    """A股回测主函数"""
    print("🚀 A股Kronos AI量化回测系统")
    print("=" * 80)
    print("✅ 254只A股全市场排序")
    print("✅ Top5持仓，每周重平衡") 
    print("✅ 三维度专业评估体系")
    print("✅ 贵州茅台基准对比")
    print("=" * 80)
    
    try:
        # 创建回测器
        backtester = CNBacktester(
            max_stocks=254,
            top_n_stocks=5,
            rebalance_freq=7
        )
        
        # 运行回测
        portfolio, metrics = backtester.run_backtest()
        
        # 生成报告
        report = backtester.generate_report(portfolio, metrics)
        print(report)
        
        # 性能评估总结
        print(f"🔬 【A股专业评估总结】:")
        
        if metrics.information_ratio > 1.0:
            ir_assessment = "优秀 ⭐⭐⭐"
        elif metrics.information_ratio > 0.5:
            ir_assessment = "良好 ⭐⭐"
        else:
            ir_assessment = "一般 ⭐"
        print(f"- 信息比率评级: {ir_assessment}")
        
        if abs(metrics.mean_ic) > 0.05:
            ic_assessment = "信号稳定有效 ✅"
        elif abs(metrics.mean_ic) > 0.02:
            ic_assessment = "信号中等有效 ⚠️"
        else:
            ic_assessment = "信号较弱 ❌"
        print(f"- IC信号质量: {ic_assessment}")
        
        if metrics.max_drawdown < 15:
            risk_assessment = "低风险 🟢"
        elif metrics.max_drawdown < 30:
            risk_assessment = "中等风险 🟡"
        else:
            risk_assessment = "高风险 🔴"
        print(f"- 风险水平: {risk_assessment}")
        
        if metrics.annual_excess_return > 5:
            alpha_assessment = "显著跑赢茅台 🎯"
        elif metrics.annual_excess_return > 0:
            alpha_assessment = "略微跑赢茅台 📈"
        else:
            alpha_assessment = "未跑赢茅台基准 📉"
        print(f"- Alpha表现: {alpha_assessment}")
        
        return portfolio, metrics
        
    except Exception as e:
        print(f"❌ A股回测失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    portfolio, metrics = main()
