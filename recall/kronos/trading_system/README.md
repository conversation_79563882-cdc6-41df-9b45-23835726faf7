# Kronos AI交易系统架构

## 📁 目录结构

```
recall/kronos/trading_system/
├── common/                    # 通用组件
│   ├── __init__.py
│   ├── kronos_ranker.py       # Kronos AI排序器 (通用)
│   ├── evaluator.py           # 回测评估器 (通用)
│   └── utils.py               # 工具函数 (通用)
├── backtest/                  # 回测系统
│   ├── cn/                    # A股回测
│   │   ├── __init__.py
│   │   └── cn_backtest.py     # A股完整回测系统
│   ├── us/                    # 美股回测
│   │   ├── __init__.py
│   │   └── us_backtest.py     # 美股完整回测系统
│   └── __init__.py
├── live_trading/              # 实际交易系统
│   ├── cn/                    # A股实际交易
│   │   ├── __init__.py
│   │   ├── cn_trader.py       # A股交易执行器
│   │   └── cn_rebalancer.py   # A股再平衡器
│   ├── us/                    # 美股实际交易
│   │   ├── __init__.py
│   │   ├── us_trader.py       # 美股交易执行器
│   │   └── us_rebalancer.py   # 美股再平衡器
│   └── __init__.py
├── __init__.py
└── README.md
```

## 🎯 功能分工

### 1. 通用组件 (common/)
- **kronos_ranker.py**: Kronos AI股票排序器，支持A股和美股
- **evaluator.py**: 回测性能评估器，三维度指标计算
- **utils.py**: 数据处理、时间计算等通用工具

### 2. 回测系统 (backtest/)
- **cn_backtest.py**: A股完整回测流程，包含历史数据回放
- **us_backtest.py**: 美股完整回测流程，包含历史数据回放

### 3. 实际交易系统 (live_trading/)
- **cn_trader.py**: A股实际交易执行，连接券商API
- **cn_rebalancer.py**: A股再平衡日运行的选股排序逻辑
- **us_trader.py**: 美股实际交易执行，连接美股券商API
- **us_rebalancer.py**: 美股再平衡日运行的选股排序逻辑

## 🔄 使用流程

### 回测模式
```python
from recall.kronos.trading_system.backtest.cn import cn_backtest
# 或
from recall.kronos.trading_system.backtest.us import us_backtest

# 运行A股回测
results = cn_backtest.run_backtest()

# 运行美股回测
results = us_backtest.run_backtest()
```

### 实际交易模式
```python
from recall.kronos.trading_system.live_trading.cn import cn_rebalancer

# 在再平衡日运行
selected_stocks = cn_rebalancer.get_top_stocks()
```

## 📊 设计原则

1. **功能分离**: 回测与实际交易完全分离
2. **市场分离**: A股与美股代码独立
3. **组件复用**: 通用功能统一实现
4. **结构清晰**: 每个文件职责明确
5. **易于维护**: 模块化设计，便于扩展

## 🚀 迁移计划

1. ✅ 创建新目录结构
2. 🔄 提取通用组件
3. 📝 重构A股回测代码
4. 📝 重构美股回测代码
5. 🔧 创建实际交易代码
6. 🗑️ 清理旧代码文件
