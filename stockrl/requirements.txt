# E2E Reinforcement Learning Stock Trading System
# Requirements file for pip installation

# Core data science libraries
numpy>=1.21.0
pandas>=1.3.0
matplotlib>=3.5.0
seaborn>=0.11.0

# Machine learning and reinforcement learning
stable-baselines3>=1.7.0
sb3-contrib>=1.7.0
gymnasium>=0.28.0
torch>=1.12.0
tensorboard>=2.8.0

# Data fetching
yfinance>=0.2.0

# Utilities
tqdm>=4.64.0
scikit-learn>=1.1.0

# Optional: For better performance
# psutil>=5.9.0
# numba>=0.56.0

# Development tools (optional)
# jupyter>=1.0.0
# ipython>=8.0.0
# pytest>=7.0.0 

pyarrow
fastparquet