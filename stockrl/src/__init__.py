# src/__init__.py
"""
E2E Reinforcement Learning Stock Trading System

This package contains the core components for an end-to-end reinforcement learning
stock trading system:

- config.py: Configuration settings for the trading system
- data_manager.py: Data acquisition and management module
- environment.py: Market simulation environment (core)
- agent.py: Custom agent implementations (future use)
- utils.py: Utility functions
"""

__version__ = "0.1.0" 