# src/macro_data.py
"""
宏观经济指标数据管理模块
获取VIX、国债收益率等市场环境指标
"""

import yfinance as yf
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, Optional
import os

MACRO_CACHE_DIR = Path("data_cache/macro")


def get_macro_indicators(start_date: str, end_date: str) -> pd.DataFrame:
    """
    获取宏观经济指标数据
    
    Args:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        
    Returns:
        pd.DataFrame: 包含宏观指标的DataFrame
    """
    MACRO_CACHE_DIR.mkdir(parents=True, exist_ok=True)
    cache_file = MACRO_CACHE_DIR / f"macro_{start_date}_{end_date}.parquet"
    
    if os.path.exists(cache_file):
        print(f"Loading macro data from cache: {cache_file}")
        return pd.read_parquet(cache_file)
    
    print("Fetching macro indicators...")
    
    # 宏观指标映射
    macro_tickers = {
        '^VIX': 'VIX',           # 波动率指数
        '^TNX': 'TNX',           # 10年期国债收益率
        '^IRX': 'IRX',           # 3个月国债收益率  
        'DXY': 'DXY',            # 美元指数
        '^GSPC': 'SPX'           # 标普500指数（用于计算相对强度）
    }
    
    macro_data = {}
    
    for ticker, name in macro_tickers.items():
        try:
            print(f"  Fetching {name} ({ticker})...")
            data = yf.download(ticker, start=start_date, end=end_date, auto_adjust=True)
            if not data.empty and 'Close' in data.columns:
                # 处理Close列，确保是一维数据
                close_data = data['Close']
                if hasattr(close_data, 'values'):
                    values = close_data.values
                    if values.ndim > 1:
                        values = values.flatten()
                    macro_data[name] = values
                    macro_data[f'{name}_dates'] = data.index
                else:
                    macro_data[name] = close_data
                    macro_data[f'{name}_dates'] = data.index
            else:
                print(f"  Warning: No data for {ticker}")
        except Exception as e:
            print(f"  Error fetching {ticker}: {e}")
    
    # 找到公共日期范围
    if macro_data:
        # 使用VIX的日期作为基准（最重要的指标）
        if 'VIX_dates' in macro_data:
            base_dates = macro_data['VIX_dates']
        else:
            # 找到第一个日期序列
            date_keys = [k for k in macro_data.keys() if k.endswith('_dates')]
            if date_keys:
                base_dates = macro_data[date_keys[0]]
            else:
                raise ValueError("No date information found in macro data")
        
        # 构建DataFrame
        df_data = {'Date': base_dates}
        
        for name in ['VIX', 'TNX', 'IRX', 'DXY', 'SPX']:
            if name in macro_data:
                dates = macro_data[f'{name}_dates']
                values = macro_data[name]
                
                # 对齐日期 - 修复fillna方法
                aligned_values = pd.Series(values, index=dates).reindex(base_dates).ffill()
                df_data[name] = aligned_values.values
        
        df = pd.DataFrame(df_data)
        
        # 计算衍生指标
        df = _calculate_macro_features(df)
        
        # 缓存数据
        df.to_parquet(cache_file)
        print(f"Macro data cached to: {cache_file}")
        
        return df
    
    else:
        print("Warning: No macro data available")
        # 返回空的DataFrame，包含期望的列
        empty_df = pd.DataFrame(columns=['Date', 'VIX', 'VIX_MA', 'TERM_SPREAD', 'DXY_CHANGE'])
        return empty_df


def _calculate_macro_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算宏观指标的衍生特征
    
    Args:
        df: 原始宏观数据
        
    Returns:
        pd.DataFrame: 包含衍生特征的数据
    """
    # VIX移动平均（恐慌程度）
    if 'VIX' in df.columns:
        df['VIX_MA'] = df['VIX'].rolling(window=20).mean()
        df['VIX_SPIKE'] = (df['VIX'] / df['VIX_MA'] - 1).fillna(0)  # VIX突增指标
    
    # 利率期限结构（收益率曲线）
    if 'TNX' in df.columns and 'IRX' in df.columns:
        df['TERM_SPREAD'] = df['TNX'] - df['IRX']  # 期限利差
        df['TERM_SPREAD_MA'] = df['TERM_SPREAD'].rolling(window=20).mean()
    
    # 美元指数变化
    if 'DXY' in df.columns:
        df['DXY_CHANGE'] = df['DXY'].pct_change().fillna(0)
        df['DXY_MA'] = df['DXY'].rolling(window=20).mean()
    
    # 市场状态指标（基于VIX和利差）
    if 'VIX' in df.columns and 'TERM_SPREAD' in df.columns:
        # 定义市场状态：0=平静, 1=紧张, 2=危机
        df['MARKET_REGIME'] = 0
        df.loc[df['VIX'] > 20, 'MARKET_REGIME'] = 1  # 紧张
        df.loc[df['VIX'] > 30, 'MARKET_REGIME'] = 2  # 危机
        df.loc[df['TERM_SPREAD'] < 0.5, 'MARKET_REGIME'] = np.maximum(df['MARKET_REGIME'], 1)  # 收益率曲线平坦
    
    # 填充缺失值
    df = df.ffill().bfill().fillna(0)
    
    return df


def get_current_macro_features(df: pd.DataFrame, current_date: pd.Timestamp) -> np.ndarray:
    """
    获取当前日期的宏观特征向量
    
    Args:
        df: 宏观数据DataFrame
        current_date: 当前日期
        
    Returns:
        np.ndarray: 宏观特征向量
    """
    if df.empty:
        # 如果没有宏观数据，返回零向量
        return np.zeros(6, dtype=np.float32)
    
    # 找到最接近的日期
    df['Date'] = pd.to_datetime(df['Date'])
    closest_date_idx = (df['Date'] - pd.to_datetime(current_date)).abs().argmin()
    row = df.iloc[closest_date_idx]
    
    # 提取关键特征
    features = [
        row.get('VIX', 0) / 100,              # VIX (归一化到0-1)
        row.get('VIX_SPIKE', 0),              # VIX突增
        row.get('TERM_SPREAD', 0) / 10,       # 期限利差 (归一化)
        row.get('DXY_CHANGE', 0) * 100,       # 美元变化 (放大)
        row.get('MARKET_REGIME', 0) / 2,      # 市场状态 (0-1)
        row.get('TNX', 0) / 10                # 10年期利率 (归一化)
    ]
    
    return np.array(features, dtype=np.float32)


def test_macro_data():
    """测试宏观数据获取功能"""
    print("Testing macro data fetching...")
    
    df = get_macro_indicators("2023-01-01", "2024-01-01")
    print(f"Macro data shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")
    
    if not df.empty:
        print(f"Date range: {df['Date'].min()} to {df['Date'].max()}")
        print("\nSample data:")
        print(df.head())
        
        # 测试特征提取
        test_date = df['Date'].iloc[50]
        features = get_current_macro_features(df, test_date)
        print(f"\nMacro features for {test_date}: {features}")
    
    return df


if __name__ == "__main__":
    test_macro_data()