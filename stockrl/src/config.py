# src/config.py
"""
Configuration settings for the E2E RL trading system
"""

# 使用几支相关性不同的美股作为MVP的资产池
TICKERS = ["AAPL", "MSFT", "GOOG", "JPM"]

# 定义训练和测试的时间段
TRAIN_START_DATE = "2019-01-01"
TRAIN_END_DATE = "2024-01-01"
TEST_START_DATE = "2024-01-01"
TEST_END_DATE = "2025-06-30"

# 环境参数
LOOKBACK_WINDOW_SIZE = 60  # Agent能"看到"过去多少天的数据
INITIAL_PORTFOLIO_VALUE = 10000  # 初始资金
TRANSACTION_COST_RATE = 0.0003  # 交易成本率 (0.03%) - 更符合现代算法交易成本

# 训练参数
TOTAL_TIMESTEPS = 10000  # 快速验证改进效果
VERBOSE = 1  # 训练过程中的详细输出

# 模型保存路径
MODEL_SAVE_PATH = "models/ppo_trader_mvp"

# 日志和输出目录
LOGS_DIR = "logs"
TENSORBOARD_LOG_DIR = "logs/tensorboard_logs"

# 图表设置
PLOT_FIGSIZE = (12, 6) 