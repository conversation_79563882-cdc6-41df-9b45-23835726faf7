# src/data_manager.py
"""
Data acquisition and management module for the E2E RL trading system.
Handles data fetching from yfinance and local caching.
"""

import yfinance as yf
import pandas as pd
import os
from pathlib import Path
from typing import List, Union
import numpy as np

CACHE_DIR = Path("data_cache")


def get_data(tickers: List[str], start_date: str, end_date: str) -> pd.DataFrame:
    """
    获取股票数据，支持本地缓存以避免重复下载
    
    Args:
        tickers: 股票代码列表
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        
    Returns:
        pd.DataFrame: 包含股票数据的DataFrame，格式为长格式
    """
    CACHE_DIR.mkdir(exist_ok=True)
    cache_file = CACHE_DIR / f"{'_'.join(tickers)}_{start_date}_{end_date}.parquet"

    if os.path.exists(cache_file):
        print(f"Loading data from cache: {cache_file}")
        return pd.read_parquet(cache_file)
    
    print(f"Fetching data from yfinance for {tickers}...")
    try:
        df = yf.download(tickers, start=start_date, end=end_date, auto_adjust=True)
        
        # 处理单个股票的情况
        if isinstance(df.columns, pd.MultiIndex):
            # 多重索引，我们将其转换为更易用的长格式
            df = df.stack(level=1, future_stack=True).rename_axis(['Date', 'Ticker']).reset_index()
        else:
            # 单个股票，手动添加Ticker列
            df = df.reset_index()
            df['Ticker'] = tickers[0]
        
        # 确保数据完整性
        df = df.dropna()
        
        # 保存到缓存
        df.to_parquet(cache_file)
        print(f"Data saved to cache: {cache_file}")
        
        return df
    except Exception as e:
        print(f"Error fetching data: {e}")
        raise


def preprocess_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    对原始数据进行预处理
    
    Args:
        df: 原始数据DataFrame
        
    Returns:
        pd.DataFrame: 预处理后的数据
    """
    # 确保日期列是datetime类型
    df['Date'] = pd.to_datetime(df['Date'])
    
    # 按日期排序
    df = df.sort_values(['Date', 'Ticker']).reset_index(drop=True)
    
    # 计算日收益率
    df['Returns'] = df.groupby('Ticker')['Close'].pct_change()
    
    # 计算移动平均
    df['MA_5'] = df.groupby('Ticker')['Close'].rolling(window=5).mean().reset_index(0, drop=True)
    df['MA_20'] = df.groupby('Ticker')['Close'].rolling(window=20).mean().reset_index(0, drop=True)
    
    # 计算波动率 (过去20天的收益率标准差)
    df['Volatility'] = df.groupby('Ticker')['Returns'].rolling(window=20).std().reset_index(0, drop=True)
    
    return df


def calculate_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算技术指标
    
    Args:
        df: 预处理后的数据
        
    Returns:
        pd.DataFrame: 包含技术指标的数据
    """
    # RSI (相对强弱指数)
    def calculate_rsi(prices, window=14):
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    # MACD (移动平均收敛发散)
    def calculate_macd(prices, fast=12, slow=26, signal=9):
        exp1 = prices.ewm(span=fast).mean()
        exp2 = prices.ewm(span=slow).mean()
        macd = exp1 - exp2
        signal_line = macd.ewm(span=signal).mean()
        histogram = macd - signal_line
        return macd, signal_line, histogram
    
    # 为每个股票计算技术指标
    for ticker in df['Ticker'].unique():
        mask = df['Ticker'] == ticker
        prices = df.loc[mask, 'Close']
        
        # RSI
        df.loc[mask, 'RSI'] = calculate_rsi(prices)
        
        # MACD
        macd, signal_line, histogram = calculate_macd(prices)
        df.loc[mask, 'MACD'] = macd
        df.loc[mask, 'MACD_Signal'] = signal_line
        df.loc[mask, 'MACD_Histogram'] = histogram
        
        # 布林带
        df.loc[mask, 'BB_Upper'] = df.loc[mask, 'MA_20'] + (df.loc[mask, 'Volatility'] * 2)
        df.loc[mask, 'BB_Lower'] = df.loc[mask, 'MA_20'] - (df.loc[mask, 'Volatility'] * 2)
    
    return df


def normalize_features(df: pd.DataFrame, feature_columns: List[str]) -> pd.DataFrame:
    """
    对特征进行归一化处理
    
    Args:
        df: 包含特征的数据
        feature_columns: 需要归一化的特征列名
        
    Returns:
        pd.DataFrame: 归一化后的数据
    """
    df_normalized = df.copy()
    
    for feature in feature_columns:
        if feature in df.columns:
            # 使用Z-score归一化
            mean = df[feature].mean()
            std = df[feature].std()
            df_normalized[f'{feature}_norm'] = (df[feature] - mean) / (std + 1e-8)
    
    return df_normalized


if __name__ == "__main__":
    # 测试数据获取功能
    from src.config import TICKERS, TRAIN_START_DATE, TRAIN_END_DATE
    
    print("Testing data manager...")
    data = get_data(TICKERS, TRAIN_START_DATE, TRAIN_END_DATE)
    print(f"Data shape: {data.shape}")
    print(f"Columns: {data.columns.tolist()}")
    print(f"Date range: {data['Date'].min()} to {data['Date'].max()}")
    print(f"Tickers: {data['Ticker'].unique()}")
    
    # 预处理数据
    processed_data = preprocess_data(data)
    print(f"Processed data shape: {processed_data.shape}")
    print(f"New columns: {processed_data.columns.tolist()}") 