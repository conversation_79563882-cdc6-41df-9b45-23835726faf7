# src/environment.py
"""
Market simulation environment for the E2E RL trading system.
This is the core component that simulates the trading environment.
"""

import gymnasium as gym
import numpy as np
import pandas as pd
from gymnasium import spaces
from typing import Optional, Dict, Any, Tuple
from src.config import LOOKBACK_WINDOW_SIZE, INITIAL_PORTFOLIO_VALUE, TRANSACTION_COST_RATE
from src.macro_data import get_macro_indicators, get_current_macro_features


class TradingEnv(gym.Env):
    """
    强化学习交易环境
    
    这个环境模拟了真实的股票交易市场，包括：
    - 股票价格变化
    - 交易成本
    - 组合管理
    - 风险管理
    """
    
    metadata = {"render_modes": ["human"]}
    
    def __init__(self, df: pd.DataFrame, initial_amount: float = INITIAL_PORTFOLIO_VALUE):
        """
        初始化交易环境
        
        Args:
            df: 股票数据DataFrame
            initial_amount: 初始资金
        """
        super().__init__()
        
        self.df = df.copy()
        self.tickers = sorted(df['Ticker'].unique())
        self.dates = sorted(df['Date'].unique())
        self.initial_amount = initial_amount
        
        # 风险管理参数
        self.max_position_size = 0.4  # 单一资产最大仓位40%
        self.max_total_position = 0.95  # 总仓位最大95%（保留5%现金）
        self.stop_loss_threshold = -0.15  # 止损阈值-15%
        self.max_drawdown_threshold = -0.20  # 最大回撤阈值-20%
        self.volatility_threshold = 0.06  # 波动率阈值6%
        
        # 动作空间：M个股票的目标权重 + 1个现金的权重
        # 使用Box空间，允许连续的权重值 [0, 1]
        self.action_space = spaces.Box(
            low=0, high=1, 
            shape=(len(self.tickers) + 1,), 
            dtype=np.float32
        )
        
        # 状态空间优化：精简特征集合，避免维度灾难
        # 核心价格特征 (去除Open/High/Low，只保留Close和Volume)
        self.price_features = 3  # Close, Volume, Returns  
        # 精选技术指标 (只保留最有效的指标)
        self.technical_features = 2  # MA_20, RSI (去除相关性高的MA_5和MACD)
        self.portfolio_features = len(self.tickers) + 1  # 持仓权重 + 现金权重
        
        # 缩短回望窗口 (从60天减少到30天)
        self.lookback_window = min(30, LOOKBACK_WINDOW_SIZE)
        
        # 宏观特征维度
        self.macro_features = 6  # VIX, VIX_SPIKE, TERM_SPREAD, DXY_CHANGE, MARKET_REGIME, TNX
        
        obs_dim = (
            self.lookback_window * len(self.tickers) * (self.price_features + self.technical_features) +
            self.portfolio_features +
            self.macro_features +  # 新增宏观特征
            1  # 当前组合总价值（归一化）
        )
        
        self.observation_space = spaces.Box(
            low=-np.inf, high=np.inf, 
            shape=(obs_dim,), 
            dtype=np.float32
        )
        
        # 环境状态
        self._start_tick = self.lookback_window
        self._current_tick = self._start_tick
        self.portfolio_value = self.initial_amount
        self._last_portfolio_value = self.initial_amount
        self.peak_portfolio_value = self.initial_amount  # 用于计算回撤
        
        # 基准跟踪 (等权重组合)
        self.benchmark_value = self.initial_amount
        self._last_benchmark_value = self.initial_amount
        
        # 初始化权重：第一天全部是现金
        self._weights = np.array([0.0] * len(self.tickers) + [1.0], dtype=np.float32)
        
        # 记录历史数据
        self.history = {
            'portfolio_values': [],
            'benchmark_values': [],
            'portfolio_returns': [],
            'benchmark_returns': [],
            'weights': [],
            'actions': [],
            'rewards': [],
            'risk_events': []  # 记录风险事件
        }
        
        # 预处理数据
        self._preprocess_data()
        
        # 获取宏观经济数据
        self._load_macro_data()
    
    def _preprocess_data(self):
        """预处理数据，添加技术指标"""
        # 计算收益率
        self.df['Returns'] = self.df.groupby('Ticker')['Close'].pct_change()
        
        # 计算移动平均
        self.df['MA_5'] = self.df.groupby('Ticker')['Close'].rolling(window=5).mean().reset_index(0, drop=True)
        self.df['MA_20'] = self.df.groupby('Ticker')['Close'].rolling(window=20).mean().reset_index(0, drop=True)
        
        # 计算RSI
        def calculate_rsi(prices, window=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        # 计算MACD
        def calculate_macd(prices, fast=12, slow=26):
            exp1 = prices.ewm(span=fast).mean()
            exp2 = prices.ewm(span=slow).mean()
            macd = exp1 - exp2
            return macd
        
        # 为每个股票计算技术指标
        for ticker in self.tickers:
            mask = self.df['Ticker'] == ticker
            prices = self.df.loc[mask, 'Close']
            
            self.df.loc[mask, 'RSI'] = calculate_rsi(prices)
            self.df.loc[mask, 'MACD'] = calculate_macd(prices)
        
        # 填充缺失值
        self.df = self.df.bfill().ffill()
        
        # 优化后的特征集合：只保留最有效的特征
        # 不在预处理阶段进行归一化，避免数据泄漏
        self.numeric_features = ['Close', 'Volume', 'Returns', 'MA_20', 'RSI']
    
    def _load_macro_data(self):
        """加载宏观经济数据"""
        try:
            # 使用股票数据的日期范围获取宏观数据
            start_date = self.dates[0].strftime('%Y-%m-%d')
            end_date = self.dates[-1].strftime('%Y-%m-%d')
            
            self.macro_df = get_macro_indicators(start_date, end_date)
            print(f"Loaded macro data: {len(self.macro_df)} records")
            
        except Exception as e:
            print(f"Warning: Failed to load macro data: {e}")
            # 创建空的宏观数据
            self.macro_df = pd.DataFrame(columns=['Date', 'VIX', 'VIX_SPIKE', 'TERM_SPREAD', 'DXY_CHANGE', 'MARKET_REGIME', 'TNX'])
    
    def reset(self, seed: Optional[int] = None) -> Tuple[np.ndarray, Dict[str, Any]]:
        """重置环境到初始状态"""
        super().reset(seed=seed)
        
        self._current_tick = self._start_tick
        self.portfolio_value = self.initial_amount
        self._last_portfolio_value = self.initial_amount
        self.benchmark_value = self.initial_amount
        self._last_benchmark_value = self.initial_amount
        self._weights = np.array([0.0] * len(self.tickers) + [1.0], dtype=np.float32)
        self.peak_portfolio_value = self.initial_amount  # 重置峰值
        
        # 重置历史记录
        self.history = {
            'portfolio_values': [self.initial_amount],
            'benchmark_values': [self.initial_amount], 
            'portfolio_returns': [0.0],
            'benchmark_returns': [0.0],
            'weights': [self._weights.copy()],
            'actions': [],
            'rewards': [],
            'risk_events': []
        }
        
        return self._get_observation(), {}
    
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, bool, Dict[str, Any]]:
        """执行一步交易"""
        # 1. 归一化动作（确保总权重为1）
        action = np.array(action, dtype=np.float32)
        action = np.clip(action, 0, 1)
        weights = action / (np.sum(action) + 1e-8)
        
        # 2. 应用风险管理
        adjusted_weights = self._apply_risk_management(weights)
        
        # 3. 计算交易成本
        weight_changes = np.abs(adjusted_weights - self._weights)
        transaction_costs = np.sum(weight_changes) * TRANSACTION_COST_RATE * self.portfolio_value
        
        # 4. 更新权重
        self._weights = adjusted_weights
        
        # 5. 获取当前和前一天的价格数据
        current_date = self.dates[self._current_tick]
        prev_date = self.dates[self._current_tick - 1]
        
        current_data = self.df[self.df['Date'] == current_date]
        prev_data = self.df[self.df['Date'] == prev_date]
        
        # 确保数据按ticker排序
        current_prices = current_data.set_index('Ticker').loc[self.tickers, 'Close'].values
        prev_prices = prev_data.set_index('Ticker').loc[self.tickers, 'Close'].values
        
        # 6. 计算股票收益率
        stock_returns = (current_prices / prev_prices) - 1
        
        # 7. 计算组合收益
        portfolio_return = np.sum(stock_returns * self._weights[:-1])  # 不包括现金
        
        # 8. 计算基准收益 (等权重组合)
        equal_weights = np.ones(len(self.tickers)) / len(self.tickers)
        benchmark_return = np.sum(stock_returns * equal_weights)
        
        # 9. 更新组合价值和基准价值
        self.portfolio_value = self.portfolio_value * (1 + portfolio_return) - transaction_costs
        self.benchmark_value = self.benchmark_value * (1 + benchmark_return)
        
        # 10. 计算奖励 (使用相对收益和风险调整)
        reward = self._calculate_reward(portfolio_return, benchmark_return, transaction_costs)
        
        # 11. 更新历史记录
        self.history['portfolio_values'].append(self.portfolio_value)
        self.history['benchmark_values'].append(self.benchmark_value)
        self.history['portfolio_returns'].append(portfolio_return)
        self.history['benchmark_returns'].append(benchmark_return)
        self.history['weights'].append(self._weights.copy())
        self.history['actions'].append(action.copy())
        self.history['rewards'].append(reward)
        
        # 11. 检查是否结束
        self._current_tick += 1
        terminated = self._current_tick >= len(self.dates) - 1
        truncated = self.portfolio_value <= 0  # 破产
        
        # 12. 更新上一期组合价值
        self._last_portfolio_value = self.portfolio_value
        
        # 13. 构建info字典
        info = {
            'portfolio_value': self.portfolio_value,
            'portfolio_return': portfolio_return,
            'transaction_costs': transaction_costs,
            'weights': self._weights.copy(),
            'date': current_date
        }
        
        return self._get_observation(), reward, terminated, truncated, info
    
    def _apply_risk_management(self, weights: np.ndarray) -> np.ndarray:
        """
        应用风险管理规则
        
        Args:
            weights: 原始权重
            
        Returns:
            np.ndarray: 风险调整后的权重
        """
        risk_events = []
        adjusted_weights = weights.copy()
        
        # 1. 单一资产仓位限制
        for i in range(len(self.tickers)):
            if adjusted_weights[i] > self.max_position_size:
                risk_events.append(f"Position limit exceeded for {self.tickers[i]}: {adjusted_weights[i]:.3f} -> {self.max_position_size}")
                adjusted_weights[i] = self.max_position_size
        
        # 2. 总仓位限制
        total_stock_weight = np.sum(adjusted_weights[:-1])
        if total_stock_weight > self.max_total_position:
            risk_events.append(f"Total position limit exceeded: {total_stock_weight:.3f} -> {self.max_total_position}")
            # 按比例缩减各股票仓位
            scale_factor = self.max_total_position / total_stock_weight
            adjusted_weights[:-1] *= scale_factor
        
        # 3. 检查止损条件
        if len(self.history['portfolio_values']) > 0:
            current_return = (self.portfolio_value - self.initial_amount) / self.initial_amount
            if current_return < self.stop_loss_threshold:
                risk_events.append(f"Stop-loss triggered: {current_return:.3f} < {self.stop_loss_threshold}")
                # 强制平仓：全部转为现金
                adjusted_weights[:-1] = 0.0
        
        # 4. 检查最大回撤
        if self.portfolio_value > self.peak_portfolio_value:
            self.peak_portfolio_value = self.portfolio_value
        
        current_drawdown = (self.portfolio_value - self.peak_portfolio_value) / self.peak_portfolio_value
        if current_drawdown < self.max_drawdown_threshold:
            risk_events.append(f"Max drawdown exceeded: {current_drawdown:.3f} < {self.max_drawdown_threshold}")
            # 减少风险仓位
            adjusted_weights[:-1] *= 0.5  # 减半股票仓位
        
        # 5. 波动率控制
        if len(self.history['portfolio_returns']) >= 10:
            recent_returns = self.history['portfolio_returns'][-10:]
            volatility = np.std(recent_returns)
            if volatility > self.volatility_threshold:
                risk_events.append(f"High volatility detected: {volatility:.3f} > {self.volatility_threshold}")
                # 降低风险仓位
                adjusted_weights[:-1] *= 0.8  # 减少20%股票仓位
        
        # 6. 重新计算现金权重（不进行全体归一化）
        total_stock_weight = np.sum(adjusted_weights[:-1])
        cash_weight = 1.0 - total_stock_weight
        
        # 确保现金权重非负
        if cash_weight < 0:
            # 如果现金权重为负，按比例缩减股票仓位
            scale_factor = 1.0 / total_stock_weight
            adjusted_weights[:-1] *= scale_factor
            cash_weight = 0.0
        
        adjusted_weights[-1] = cash_weight
        
        # 记录风险事件
        if risk_events:
            self.history['risk_events'].extend(risk_events)
        
        return adjusted_weights
    
    def _calculate_reward(self, portfolio_return: float, benchmark_return: float, transaction_costs: float) -> float:
        """
        风险调整的相对奖励函数
        
        Args:
            portfolio_return: 组合收益率
            benchmark_return: 基准收益率  
            transaction_costs: 交易成本
            
        Returns:
            float: 奖励值
        """
        # 1. 相对收益奖励 (跑赢基准获得正奖励)
        outperformance = portfolio_return - benchmark_return
        base_reward = outperformance * 2000  # 放大相对收益信号
        
        # 2. 风险调整奖励 (基于波动率的夏普比率思想)
        risk_adjusted_reward = 0.0
        if len(self.history['portfolio_returns']) >= 10:
            recent_returns = self.history['portfolio_returns'][-10:]
            portfolio_volatility = np.std(recent_returns)
            
            if portfolio_volatility > 0:
                # 风险调整收益 = 平均收益 / 波动率
                avg_return = np.mean(recent_returns)
                risk_adjusted_reward = (avg_return / portfolio_volatility) * 100
        
        # 3. 适度成本惩罚
        cost_penalty = -transaction_costs / self.portfolio_value * 1000
        
        # 4. 持仓质量奖励 (惩罚长期亏损持仓)
        position_quality_reward = 0.0
        if len(self.history['portfolio_values']) > 5:
            recent_values = self.history['portfolio_values'][-5:]
            trend = (recent_values[-1] - recent_values[0]) / recent_values[0]
            if trend > 0:
                position_quality_reward = trend * 500  # 奖励上升趋势
            else:
                position_quality_reward = trend * 200  # 较小惩罚下降趋势
        
        # 5. 总奖励
        total_reward = base_reward + risk_adjusted_reward + cost_penalty + position_quality_reward
        
        return total_reward
    
    def _get_observation(self) -> np.ndarray:
        """获取当前状态观察（优化特征降维版本）"""
        # 1. 获取历史价格数据（使用优化后的回望窗口）
        start_date = self.dates[self._current_tick - self.lookback_window]
        end_date = self.dates[self._current_tick - 1]
        
        history_data = self.df[
            (self.df['Date'] >= start_date) & 
            (self.df['Date'] <= end_date)
        ]
        
        # 2. 构建价格和技术指标矩阵（使用严格的滚动窗口归一化）
        features = []
        
        for ticker in self.tickers:
            ticker_data = history_data[history_data['Ticker'] == ticker]
            
            for feature in self.numeric_features:
                if feature in ticker_data.columns:
                    feature_values = ticker_data[feature].values
                    
                    # 严格避免数据泄漏的归一化
                    if self._current_tick > self.lookback_window + 20:
                        # 使用观察窗口之前的历史数据计算统计量
                        # 获取当前ticker在整个数据集中的位置
                        ticker_full_data = self.df[self.df['Ticker'] == ticker]
                        ticker_dates = ticker_full_data['Date'].values
                        
                        # 找到当前观察窗口结束时间之前20天的数据
                        current_obs_end = self.dates[self._current_tick - 1]
                        historical_mask = ticker_dates < current_obs_end
                        historical_feature_data = ticker_full_data[historical_mask][feature].values
                        
                        if len(historical_feature_data) >= 20:
                            # 使用最近20天的历史数据计算统计量
                            rolling_mean = np.mean(historical_feature_data[-20:])
                            rolling_std = np.std(historical_feature_data[-20:])
                        else:
                            # 使用所有可用历史数据
                            rolling_mean = np.mean(historical_feature_data)
                            rolling_std = np.std(historical_feature_data)
                        
                        # 对观察窗口进行归一化
                        normalized_values = (feature_values - rolling_mean) / (rolling_std + 1e-8)
                    else:
                        # 如果历史数据不足，使用简单归一化
                        if len(feature_values) > 1:
                            normalized_values = (feature_values - np.mean(feature_values)) / (np.std(feature_values) + 1e-8)
                        else:
                            normalized_values = feature_values  # 单个值不能归一化
                    
                    # 确保数据长度匹配
                    if len(normalized_values) == self.lookback_window:
                        features.extend(normalized_values)
                    else:
                        # 如果数据不足，用前向填充
                        if len(normalized_values) < self.lookback_window:
                            padded_values = np.pad(normalized_values, 
                                                 (self.lookback_window - len(normalized_values), 0), 
                                                 'edge')  # 使用边缘值填充
                        else:
                            padded_values = normalized_values[-self.lookback_window:]  # 取最后N个值
                        features.extend(padded_values)
        
        # 3. 添加当前持仓权重
        features.extend(self._weights)
        
        # 4. 添加宏观经济特征
        current_date = self.dates[self._current_tick - 1]
        macro_features = get_current_macro_features(self.macro_df, current_date)
        features.extend(macro_features)
        
        # 5. 添加当前组合价值（归一化）
        normalized_portfolio_value = self.portfolio_value / self.initial_amount
        features.append(normalized_portfolio_value)
        
        return np.array(features, dtype=np.float32)
    
    def render(self, mode: str = "human") -> None:
        """渲染环境（可选）"""
        if mode == "human":
            print(f"Day {self._current_tick}: Portfolio Value = ${self.portfolio_value:.2f}")
            print(f"Weights: {self._weights}")
    
    def get_portfolio_performance(self) -> Dict[str, float]:
        """获取组合表现统计（包含基准比较）"""
        if len(self.history['portfolio_returns']) < 2:
            return {}
        
        portfolio_returns = np.array(self.history['portfolio_returns'])
        benchmark_returns = np.array(self.history['benchmark_returns'])
        portfolio_values = np.array(self.history['portfolio_values'])
        benchmark_values = np.array(self.history['benchmark_values'])
        
        # 计算组合统计指标
        total_return = (portfolio_values[-1] - portfolio_values[0]) / portfolio_values[0]
        annualized_return = (1 + total_return) ** (252 / len(portfolio_returns)) - 1
        volatility = np.std(portfolio_returns) * np.sqrt(252)
        sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
        
        # 计算基准统计指标
        benchmark_total_return = (benchmark_values[-1] - benchmark_values[0]) / benchmark_values[0]
        benchmark_annualized_return = (1 + benchmark_total_return) ** (252 / len(benchmark_returns)) - 1
        benchmark_volatility = np.std(benchmark_returns) * np.sqrt(252)
        benchmark_sharpe = benchmark_annualized_return / benchmark_volatility if benchmark_volatility > 0 else 0
        
        # 相对表现
        outperformance = annualized_return - benchmark_annualized_return
        tracking_error = np.std(portfolio_returns - benchmark_returns) * np.sqrt(252)
        information_ratio = outperformance / tracking_error if tracking_error > 0 else 0
        
        # 最大回撤
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak
        max_drawdown = np.min(drawdown)
        
        return {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'final_value': portfolio_values[-1],
            'benchmark_total_return': benchmark_total_return,
            'benchmark_annualized_return': benchmark_annualized_return,
            'benchmark_volatility': benchmark_volatility,
            'benchmark_sharpe': benchmark_sharpe,
            'outperformance': outperformance,
            'tracking_error': tracking_error,
            'information_ratio': information_ratio
        }


if __name__ == "__main__":
    # 测试环境
    from src.data_manager import get_data
    from src.config import TICKERS, TRAIN_START_DATE, TRAIN_END_DATE
    
    print("Testing TradingEnv...")
    
    # 获取数据
    data = get_data(TICKERS, TRAIN_START_DATE, TRAIN_END_DATE)
    
    # 创建环境
    env = TradingEnv(data)
    
    print(f"Action space: {env.action_space}")
    print(f"Observation space: {env.observation_space}")
    
    # 测试环境
    obs, info = env.reset()
    print(f"Initial observation shape: {obs.shape}")
    
    # 随机动作测试
    for i in range(5):
        action = env.action_space.sample()
        obs, reward, terminated, truncated, info = env.step(action)
        print(f"Step {i+1}: Reward={reward:.4f}, Portfolio Value=${info['portfolio_value']:.2f}")
        
        if terminated or truncated:
            break
    
    # 显示性能
    performance = env.get_portfolio_performance()
    print(f"Performance: {performance}") 