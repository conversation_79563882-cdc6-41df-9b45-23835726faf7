### 演进式产品需求文档 (PRD)

核心思想是：**平衡“模型、特征、奖励”这三大支柱的共同发展，并采用更小步、更聚焦的迭代方式。**

# **产品需求文档 (PRD) v1.1: 智能交易系统演进路线图**

## **核心理念**

1.  **模型架构 (Architecture)**：提升模型的记忆与决策能力。
2.  **特征工程 (Features)**：为模型提供更丰富、更有效的市场洞察。
3.  **奖励机制 (Rewards)**：更精准地引导模型学习符合我们预期的交易行为。

-----

## **Phase 1: 奠定坚实基础 (Foundation)**

**🎯 阶段目标**: 解决最核心的**长期记忆**和**信用分配**问题，并丰富输入信息，为后续所有优化打下最坚实的基础。

#### **1.1 引入长时记忆模型 (模型架构)**

  * **任务**: 将模型从 `MlpPolicy` 升级为 `MlpLstmPolicy` (或 `MlpGruPolicy`)。
  * **目的**: 赋予 Agent 状态记忆能力，使其能够理解时间序列的上下文，这是学习波段交易的**先决条件**。
  * **成功指标**:
      * LSTM/GRU 模型能够稳定训练并收敛。
      * 在相同的特征和奖励下，新模型相比 MVP 在夏普比率上有可测量的提升。

#### **1.2 丰富化特征工程 (特征工程)**

  * **任务**: 在 `environment.py` 中增加更多维度的特征，例如：
      * **波动率指标**: ATR (平均真实波幅)，衡量市场波动性。
      * **动量指标**: ROC (变动率)，衡量价格动能。
      * **宏观指标 (可选)**: 如 VIX 指数，作为市场恐慌情绪的代理变量。
  * **目的**: 让 Agent 不仅看到“价格”，还能看到“价格背后的市场状态”，做出更明智的判断。
  * **成功指标**:
      * 加入新特征后，模型的最终累计收益和风险调整后收益（夏普比率）得到改善。

#### **1.3 引入已实现盈亏奖励 (奖励机制)**

  * **任务**:
    1.  构建一个基础的 `PositionTracker`，仅用于跟踪每笔交易的已实现盈亏 (Realized PnL)。
    2.  在奖励函数中加入一个**正比于已实现利润**的奖励项。
  * **目的**: 这是解决信用分配问题最直接、最简单的方法。它直接奖励“成功完成一笔波段交易并获利了结”的行为。
  * **成功指标**:
      * Agent 的平均持仓周期相比 MVP 有显著增长。
      * 波段交易的成功率（盈利平仓次数 / 总平仓次数）超过 50%。

-----

## **Phase 2: 精炼交易逻辑 (Refinement)**

**🎯 阶段目标**: 在具备记忆和丰富特征的基础上，进一步优化交易行为，使其更接近专业交易员的逻辑，并开始关注决策的可解释性。

#### **2.1 设计多目标奖励函数 (奖励机制)**

  * **任务**: 在 Phase 1 的基础上，扩展奖励函数，引入更多目标，如原始 PRD 中提到的**持仓质量**和**风险调整后收益**。
      * 例如：惩罚持有长期亏损的头寸；奖励夏普比率高的持仓组合。
  * **目的**: 从“能赚钱”向“赚得更聪明、更稳健”演进。
  * **成功指标**:
      * 相比 Phase 1，最大回撤降低 10% 以上。
      * 投资组合的收益波动率下降。

#### **2.2 实现动态风险管理 (模型架构/特征)**

  * **任务**: 实现原始 PRD 中的 `AdaptiveRiskManager`。
    1.  构建一个简单的市场状态分类器（例如，基于长期均线判断牛熊市）。
    2.  将“市场状态”作为一个新的**特征**输入给 Agent。
    3.  Agent 将学会根据不同的市场状态，自主调整其风险暴露（仓位）。
  * **目的**: 赋予 Agent “见风使舵”的能力，在熊市中更保守，在牛市中更积极。
  * **成功指标**:
      * 模型在回测中的熊市阶段，平均仓位显著低于牛市阶段。
      * 在市场发生剧烈反转时，模型能有效控制回撤。

#### **2.3 引入模型可解释性 (辅助工具)**

  * **任务**: 使用 SHAP 等工具，对训练好的 LSTM 模型进行分析。
  * **目的**: 尝试理解“Agent 在做决策时，最关注哪些特征？”，打开“黑箱”，为后续优化提供方向。
  * **成功指标**:
      * 能生成特征重要性报告。
      * 能对数个关键决策点进行归因分析。

-----

## **Phase 3: 迈向集成策略系统 (Integration)**

**🎯 阶段目标**: 从单一决策模型演进为支持多策略、多层次决策的复杂系统，使其具备更高的适应性和健壮性。

#### **3.1 探索分层决策架构 (模型架构)**

  * **任务**: 实现原始 PRD 中的 `HierarchicalTradingAgent` 概念。
      * **高层 Agent**: 负责战略决策（例如，未来一个月的资产配置大方向）。它的决策频率较低（如每周一次）。
      * **低层 Agent**: 负责战术执行（例如，每日的具体交易操作），在高层 Agent 的指导下行动。
  * **目的**: 模仿基金经理（高层）和交易员（低层）的协作模式，分离长期战略和短期战术，提高决策效率和逻辑清晰度。
  * **成功指标**:
      * 分层架构成功搭建并能协同工作。
      * 系统在多变市场中的表现稳定性高于单一模型。

#### **3.2 设计复合动作空间 (动作空间)**

  * **任务**: 扩展动作空间，使其能够表达更复杂的指令，如原始 PRD 中提到的，除了目标权重外，增加如“个股止损幅度”、“是否执行再平衡”等指令。
  * **目的**: 赋予 Agent 更大的操作自由度和更精细的风险控制能力。
  * **成功指标**:
      * Agent 能够有效利用新的动作空间，实现更复杂的交易行为。

#### **3.3 实施策略组合与集成 (策略)**

  * **任务**: 训练多个不同风格的“专家”Agent（如趋势跟踪型、均值回归型），并构建一个顶层“裁判”Agent，根据市场状态动态决定采纳哪个专家的建议。
  * **目的**: 构建一个“全天候”的策略系统，通过不同策略的组合来对冲风险，适应更广泛的市场环境。
  * **成功指标**:
      * 策略组合的夏普比率高于任何一个单一策略。
      * 在历史上的特定市场风格转换期，系统表现平稳。