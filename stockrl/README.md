# End-to-End Reinforcement Learning Stock Trading System

An end-to-end reinforcement learning stock trading system that uses deep reinforcement learning (PPO algorithm) to develop intelligent trading strategies. The system models trading as a Markov Decision Process (MDP) where an agent learns optimal trading strategies through simulated market environments.

## 🎯 Project Goals

This project aims to build a complete end-to-end reinforcement learning trading system that:
- Models trading as a Markov Decision Process (MDP)
- Uses PPO (Proximal Policy Optimization) for intelligent decision making
- Incorporates realistic market constraints (transaction costs, risk management)
- Provides comprehensive performance evaluation against benchmarks

## 🏗️ System Architecture

### Core Components

- **Agent (PPO)**: Deep neural network based on PPO algorithm for trading decisions
- **Environment**: High-fidelity market simulator with transaction costs and risk management
- **State**: Comprehensive information including historical prices, technical indicators, and current positions
- **Action**: Continuous portfolio weight allocation decisions
- **Reward**: Multi-factor reward function considering returns, risk, and transaction costs

### Project Structure

```
stockrl/
├── data_cache/             # Data cache directory (auto-generated)
├── src/
│   ├── __init__.py
│   ├── config.py           # Configuration settings
│   ├── data_manager.py     # Data fetching and management
│   └── environment.py      # Trading environment (core)
├── models/                 # Model save directory (auto-generated)
├── logs/                   # Logs and output files (auto-generated)
│   ├── tensorboard_logs/   # TensorBoard logs
│   ├── training_progress.png # Training progress charts
│   ├── evaluation_results.png # Evaluation result charts
│   └── evaluation_results.csv # Evaluation results data
├── train.py                # Training script
├── evaluate.py             # Evaluation script
├── requirements.txt        # Dependencies
├── CLAUDE.md              # AI assistant guidance
└── README.md               # Project documentation
```

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Clone the repository
git clone https://github.com/leoncuhk/stockrl.git
cd stockrl

# Create virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

Edit `src/config.py` to modify key parameters:

```python
# Stock universe (default: 4 US stocks)
TICKERS = ["AAPL", "MSFT", "GOOG", "JPM"]

# Data time ranges
TRAIN_START_DATE = "2018-01-01"
TRAIN_END_DATE = "2023-01-01"
TEST_START_DATE = "2023-01-01"
TEST_END_DATE = "2024-01-01"

# Training parameters
TOTAL_TIMESTEPS = 100000  # Adjust based on computational resources
```

### 3. Train the Model

```bash
python train.py
```

The training process will:
- Automatically download and cache stock data
- Create the trading environment
- Train the PPO agent
- Save the trained model to `models/` directory
- Generate training progress visualizations

### 4. Evaluate the Model

```bash
python evaluate.py
```

The evaluation process will:
- Load the trained model
- Run backtesting on test data
- Compare against buy-and-hold benchmark
- Generate detailed performance reports and charts
- Save evaluation results to CSV files

### 5. Monitor Training (Optional)

```bash
# View TensorBoard logs
tensorboard --logdir=logs/tensorboard_logs
```

## 📊 Performance Metrics

The system automatically calculates key performance indicators:

- **Total Return**: Overall return during the investment period
- **Annualized Return**: Annualized return rate
- **Sharpe Ratio**: Risk-adjusted return metric
- **Maximum Drawdown**: Maximum loss from peak to trough
- **Win Rate**: Percentage of profitable trading days
- **Volatility**: Standard deviation of returns

## 🎯 Key Features

### 1. Intelligent State Design
- Historical price data (OHLCV)
- Technical indicators (MA, RSI, MACD)
- Current portfolio positions
- Normalized feature processing

### 2. Multi-Component Reward Function
- Base return reward (risk-adjusted)
- Transaction cost penalty
- Risk volatility penalty
- Portfolio stability bonus

### 3. High-Fidelity Environment
- Realistic transaction cost modeling
- Dynamic position sizing
- Risk management constraints
- Historical data replay

### 4. Comprehensive Evaluation
- Multi-dimensional performance analysis
- Benchmark comparison testing
- Visualization chart generation
- Detailed result exports

## 🔧 Customization Guide

### Adding New Features
Add new technical indicators in `src/environment.py` `_preprocess_data` method:

```python
# Add new technical indicators
self.df['NEW_INDICATOR'] = calculate_new_indicator(self.df)
```

### Modifying Reward Function
Adjust the reward logic in `src/environment.py` `_calculate_reward` method:

```python
def _calculate_reward(self, portfolio_return, transaction_costs):
    # Custom reward logic
    custom_reward = your_reward_function(portfolio_return, transaction_costs)
    return custom_reward
```

### Changing Stock Universe
Modify the `TICKERS` list in `src/config.py`:

```python
TICKERS = ["AAPL", "MSFT", "GOOG", "AMZN", "TSLA"]  # Add more stocks
```

### Hyperparameter Tuning
Modify PPO parameters in `train.py` `train_agent` function:

```python
model = PPO(
    'MlpPolicy',
    env,
    learning_rate=1e-4,    # Learning rate
    n_steps=4096,          # Steps per update
    batch_size=128,        # Batch size
    # ... other parameters
)
```

## 📈 Usage Recommendations

### Training Optimization
1. **Increase Training Steps**: For complex markets, use at least 100,000 steps
2. **Adjust Learning Rate**: Tune based on convergence behavior
3. **Feature Engineering**: Add more technical indicators and market information
4. **Reward Function**: Customize based on trading objectives

### Risk Management
1. **Capital Management**: Set reasonable initial capital and position limits
2. **Drawdown Control**: Monitor maximum drawdown and implement stop-loss
3. **Diversification**: Add more uncorrelated asset classes
4. **Real-time Monitoring**: Implement performance monitoring and alerts

## 🔍 FAQ

### Q: Training takes too long?
A: You can:
- Reduce `TOTAL_TIMESTEPS`
- Use shorter historical data periods
- Decrease `LOOKBACK_WINDOW_SIZE`
- Use GPU acceleration

### Q: Model performance is poor?
A: Try:
- Increase training timesteps
- Adjust reward function
- Add more features
- Optimize hyperparameters
- Use different RL algorithms

### Q: Data download errors?
A: Check:
- Network connection
- Stock ticker correctness
- Date range validity
- yfinance library version

### Q: How to use in live trading?
A: You need:
- Real-time data feed connection
- Trading interface implementation
- Enhanced risk controls
- Extensive paper trading testing

## 📚 Technical Documentation

### Key Classes

- `TradingEnv`: Core trading environment implementing Gymnasium interface
- `get_data()`: Data fetching function with caching mechanism
- `TradingCallback`: Training progress monitoring callback
- `evaluate_model()`: Model evaluation function

### Data Flow

1. Data Acquisition → 2. Preprocessing → 3. Environment Initialization → 4. Agent Training → 5. Model Evaluation

## 🤝 Contributing

Contributions are welcome! Please follow these steps:

1. Fork the project
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License. See `LICENSE` file for details.

## ⚠️ Risk Disclaimer

This system is for research and educational purposes only. Real trading involves risk. Use cautiously and never invest more than you can afford to lose. Past performance does not guarantee future results.