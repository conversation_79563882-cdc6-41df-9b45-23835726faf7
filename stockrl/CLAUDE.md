# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an end-to-end reinforcement learning stock trading system that uses deep reinforcement learning (PPO algorithm) to develop intelligent trading strategies. The system models trading as a Markov Decision Process (MDP) where an agent learns optimal trading strategies through simulated market environments.

### Core Architecture

The system consists of four main components:

1. **Agent (PPO)**: A deep neural network that makes trading decisions
2. **Environment (TradingEnv)**: High-fidelity market simulator with transaction costs and risk management
3. **Data Manager**: Handles stock data fetching and caching using yfinance
4. **Configuration**: Centralized settings for all system parameters

## Key Development Commands

### Environment Setup
```bash
# Create and activate virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Training and Evaluation
```bash
# Train the RL agent
python train.py

# Evaluate trained model on test data
python evaluate.py

# Test individual components
python -m src.data_manager  # Test data fetching
python -m src.environment   # Test trading environment
```

### Monitoring and Analysis
```bash
# View TensorBoard logs
tensorboard --logdir=logs/tensorboard_logs

# Check generated outputs
ls logs/  # Contains training_progress.png, evaluation_results.csv, evaluation_results.png
```

## Code Architecture

### Data Flow
1. **Data Acquisition** (`src/data_manager.py`): Downloads stock data via yfinance with local caching
2. **Preprocessing** (`src/environment.py._preprocess_data()`): Adds technical indicators (RSI, MACD, moving averages)
3. **Environment Creation** (`src/environment.py`): Wraps data in Gymnasium-compatible trading environment
4. **Agent Training** (`train.py`): PPO agent learns through environment interaction
5. **Evaluation** (`evaluate.py`): Backtesting on out-of-sample data with benchmark comparison

### Core Classes

- **`TradingEnv`** (`src/environment.py:15`): Main RL environment implementing Gymnasium interface
  - Action space: Continuous portfolio weights for each asset + cash
  - Observation space: Historical prices + technical indicators + current portfolio state
  - Risk management: Position limits, stop-loss, drawdown control, volatility management
  
- **`get_data()`** (`src/data_manager.py:17`): Data fetching with Parquet caching
- **`TradingCallback`** (`train.py:25`): Custom training progress monitoring
- **`evaluate_model()`** (`evaluate.py:48`): Comprehensive model evaluation with metrics

### Key Configuration Parameters

All configurable parameters are centralized in `src/config.py`:

- **`TICKERS`**: List of stock symbols (default: ["AAPL", "MSFT", "GOOG", "JPM"])
- **`LOOKBACK_WINDOW_SIZE`**: Historical data window for agent observation (default: 60 days)
- **`TOTAL_TIMESTEPS`**: Training duration (default: 100,000)
- **`TRANSACTION_COST_RATE`**: Trading cost simulation (default: 0.0003)
- **`INITIAL_PORTFOLIO_VALUE`**: Starting capital (default: $10,000)

## Data Management

### Caching System
- Stock data is automatically cached in `data_cache/` directory as Parquet files
- Cache filenames include tickers and date ranges: `AAPL_MSFT_GOOG_JPM_2018-01-01_2023-01-01.parquet`
- Subsequent runs with same parameters use cached data

### Data Structure
Data is stored in long format with columns:
- `Date`: Trading date
- `Ticker`: Stock symbol  
- `Open`, `High`, `Low`, `Close`, `Volume`: OHLCV data
- Technical indicators added during preprocessing: `Returns`, `MA_5`, `MA_20`, `RSI`, `MACD`

## Risk Management Features

The trading environment includes sophisticated risk management:

1. **Position Limits**: Max 40% in single asset, 95% total equity exposure
2. **Stop Loss**: Automatic liquidation at -15% portfolio loss
3. **Drawdown Control**: Position reduction at -20% drawdown from peak
4. **Volatility Management**: Dynamic position sizing based on recent volatility
5. **Transaction Costs**: Realistic cost modeling (0.03% per trade)

## Model Training

### PPO Hyperparameters
Optimized hyperparameters in `train.py:89`:
- Learning rate: 1e-4
- Batch size: 128  
- Number of steps: 4096
- Training epochs: 20
- Entropy coefficient: 0.01

### Reward Function
Multi-component reward in `src/environment.py:300`:
- Base return reward (risk-adjusted)
- Transaction cost penalty
- Volatility penalty for excessive risk
- Stability bonus for consistent positive returns

## Evaluation Metrics

The system calculates comprehensive performance metrics:
- Total Return, Annualized Return, Volatility
- Sharpe Ratio, Maximum Drawdown, Win Rate
- Benchmark comparison against equal-weight buy-and-hold strategy

## File Outputs

Training and evaluation generate several outputs in `logs/`:
- `training_progress.png`: Training reward and portfolio value curves
- `evaluation_results.png`: Performance comparison charts
- `evaluation_results.csv`: Detailed backtest results
- `tensorboard_logs/`: TensorBoard training logs

## Dependencies

Core dependencies from `requirements.txt`:
- **stable-baselines3**: RL algorithms (PPO)
- **gymnasium**: RL environment interface
- **yfinance**: Stock data fetching
- **pandas/numpy**: Data manipulation
- **matplotlib/seaborn**: Visualization
- **torch**: Neural network backend
- **tensorboard**: Training monitoring

## Testing

Run individual component tests:
```bash
python -m src.data_manager    # Test data fetching and caching
python -m src.environment     # Test trading environment functionality
```

## Common Modifications

### Adding New Assets
Modify `TICKERS` in `src/config.py` and retrain:
```python
TICKERS = ["AAPL", "MSFT", "GOOG", "AMZN", "TSLA"]
```

### Adjusting Risk Parameters
Modify risk thresholds in `TradingEnv.__init__()` (`src/environment.py:44`):
```python
self.max_position_size = 0.3  # Reduce max single position to 30%
self.stop_loss_threshold = -0.10  # Tighter stop loss at -10%
```

### Custom Technical Indicators
Add indicators in `TradingEnv._preprocess_data()` (`src/environment.py:99`):
```python
# Add Bollinger Bands
self.df['BB_Upper'] = self.df['MA_20'] + (self.df['Close'].rolling(20).std() * 2)
self.df['BB_Lower'] = self.df['MA_20'] - (self.df['Close'].rolling(20).std() * 2)
```

### Hyperparameter Tuning
Modify PPO parameters in `train.py:89` and increase `TOTAL_TIMESTEPS` in config for better performance.