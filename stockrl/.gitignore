# Project-specific directories
logs/
models/
data_cache/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints

# pytest
.pytest_cache/
.coverage
htmlcov/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
*.tmp
*.temp
*.log

# TensorBoard logs (already covered by logs/ but being explicit)
tensorboard_logs/

# Model checkpoints and weights
*.zip
*.pkl
*.h5
*.pth
*.pt

# Data files (large datasets)
*.csv
*.parquet
*.h5
*.hdf5
*.json
data/
raw_data/

# OS generated files
.DS_Store?
ehthumbs.db
Icon?
Thumbs.db

# PyCharm
.idea/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs documentation
/site

# Backup files
*.bak
*.backup