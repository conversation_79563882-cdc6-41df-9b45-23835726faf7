# train.py
"""
Training script for the E2E RL trading system.
This script trains a PPO agent on historical stock data.
"""

import os
import numpy as np
import pandas as pd
from stable_baselines3 import PPO
from sb3_contrib import RecurrentPPO
from stable_baselines3.common.vec_env import DummyVecEnv
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.callbacks import BaseCallback
import matplotlib.pyplot as plt

from src.data_manager import get_data
from src.environment import TradingEnv
from src.config import (
    TICKERS, TRAIN_START_DATE, TRAIN_END_DATE, 
    TOTAL_TIMESTEPS, VERBOSE, MODEL_SAVE_PATH, PLOT_FIGSIZE,
    LOGS_DIR, TENSORBOARD_LOG_DIR
)


class TradingCallback(BaseCallback):
    """
    自定义回调函数，用于监控训练过程
    """
    
    def __init__(self, verbose=0):
        super().__init__(verbose)
        self.rewards = []
        self.portfolio_values = []
        
    def _on_step(self) -> bool:
        # 获取当前奖励
        if len(self.locals.get('rewards', [])) > 0:
            reward = self.locals['rewards'][0]
            self.rewards.append(reward)
            
        # 获取环境信息
        if hasattr(self.training_env, 'envs'):
            env = self.training_env.envs[0]
            if hasattr(env, 'portfolio_value'):
                self.portfolio_values.append(env.portfolio_value)
                
        # 每1000步输出一次进度
        if self.n_calls % 1000 == 0:
            if len(self.rewards) > 0:
                recent_rewards = self.rewards[-100:] if len(self.rewards) >= 100 else self.rewards
                avg_reward = np.mean(recent_rewards)
                print(f"Step {self.n_calls}: Average reward (last 100): {avg_reward:.4f}")
                
        return True


def create_env(df: pd.DataFrame) -> DummyVecEnv:
    """
    创建向量化环境
    
    Args:
        df: 训练数据
        
    Returns:
        DummyVecEnv: 向量化环境
    """
    def make_env():
        env = TradingEnv(df)
        env = Monitor(env)
        return env
    
    return DummyVecEnv([make_env])


def train_agent(env: DummyVecEnv, total_timesteps: int = TOTAL_TIMESTEPS, use_lstm: bool = True):
    """
    训练PPO智能体
    
    Args:
        env: 训练环境
        total_timesteps: 总训练步数
        
    Returns:
        PPO: 训练好的模型
    """
    if use_lstm:
        print("Initializing RecurrentPPO (LSTM) agent...")
        # 创建RecurrentPPO模型（LSTM记忆能力）
        model = RecurrentPPO(
            'MlpLstmPolicy',
            env,
            verbose=VERBOSE,
            learning_rate=1e-4,  # 降低学习率，提高稳定性
            n_steps=2048,        # LSTM训练需要较少步数
            batch_size=64,       # LSTM适合较小批量
            n_epochs=10,         # 减少训练轮数避免过拟合
            gamma=0.995,         # 提高折扣因子，更重视长期收益
            gae_lambda=0.98,     # 调整GAE参数
            clip_range=0.2,      # LSTM适合较大裁剪范围
            clip_range_vf=None,
            normalize_advantage=True,
            ent_coef=0.005,      # 降低熵系数，LSTM本身有探索能力
            vf_coef=0.5,
            max_grad_norm=0.5,
            tensorboard_log=TENSORBOARD_LOG_DIR
        )
    else:
        print("Initializing PPO agent...")
        # 创建PPO模型（优化超参数）
        model = PPO(
            'MlpPolicy',
            env,
            verbose=VERBOSE,
            learning_rate=1e-4,  # 降低学习率，提高稳定性
            n_steps=4096,        # 增加步数，收集更多经验
            batch_size=128,      # 增加批量大小，提高样本效率
            n_epochs=20,         # 增加训练轮数
            gamma=0.995,         # 提高折扣因子，更重视长期收益
            gae_lambda=0.98,     # 调整GAE参数
            clip_range=0.1,      # 降低裁剪范围，更保守的策略更新
            clip_range_vf=None,
            normalize_advantage=True,
            ent_coef=0.01,       # 增加熵系数，促进探索
            vf_coef=0.5,
            max_grad_norm=0.5,
            tensorboard_log=TENSORBOARD_LOG_DIR
        )
    
    print(f"Starting training for {total_timesteps} timesteps...")
    
    # 创建回调函数
    callback = TradingCallback(verbose=1)
    
    # 开始训练
    model.learn(
        total_timesteps=total_timesteps,
        callback=callback,
        progress_bar=True
    )
    
    print("Training completed!")
    
    # 绘制训练过程
    if len(callback.rewards) > 0:
        plt.figure(figsize=PLOT_FIGSIZE)
        
        # 绘制奖励曲线
        plt.subplot(2, 1, 1)
        plt.plot(callback.rewards)
        plt.title('Training Rewards')
        plt.xlabel('Steps')
        plt.ylabel('Reward')
        plt.grid(True)
        
        # 绘制组合价值曲线
        if len(callback.portfolio_values) > 0:
            plt.subplot(2, 1, 2)
            plt.plot(callback.portfolio_values)
            plt.title('Portfolio Value During Training')
            plt.xlabel('Steps')
            plt.ylabel('Portfolio Value ($)')
            plt.grid(True)
        
        plt.tight_layout()
        plt.savefig(os.path.join(LOGS_DIR, 'training_progress.png'))
        plt.show()
    
    return model


def evaluate_random_agent(env: DummyVecEnv, episodes: int = 1) -> dict:
    """
    评估随机智能体作为基线
    
    Args:
        env: 环境
        episodes: 评估轮数
        
    Returns:
        dict: 评估结果
    """
    print("Evaluating random agent baseline...")
    
    total_rewards = []
    portfolio_values = []
    
    for episode in range(episodes):
        obs = env.reset()
        done = False
        episode_reward = 0
        
        while not done:
            # 随机动作
            action = env.action_space.sample()
            # 对于DummyVecEnv，需要reshape成(1, action_dim)
            action = action.reshape(1, -1)
            obs, reward, done, info = env.step(action)
            episode_reward += reward[0]
            
            # 获取组合价值
            if hasattr(env.envs[0].unwrapped, 'portfolio_value'):
                portfolio_values.append(env.envs[0].unwrapped.portfolio_value)
                
        total_rewards.append(episode_reward)
    
    performance = {
        'mean_reward': np.mean(total_rewards),
        'std_reward': np.std(total_rewards),
        'final_portfolio_value': portfolio_values[-1] if portfolio_values else 0
    }
    
    print(f"Random agent performance: {performance}")
    return performance


def main():
    """主训练函数"""
    print("=" * 60)
    print("E2E Reinforcement Learning Stock Trading System")
    print("=" * 60)
    
    # 1. 创建必要的目录
    os.makedirs(LOGS_DIR, exist_ok=True)
    os.makedirs(os.path.dirname(MODEL_SAVE_PATH), exist_ok=True)
    
    # 2. 加载训练数据
    print("Loading training data...")
    train_df = get_data(TICKERS, TRAIN_START_DATE, TRAIN_END_DATE)
    print(f"Data loaded: {len(train_df)} rows, {len(train_df['Date'].unique())} days")
    print(f"Tickers: {TICKERS}")
    print(f"Date range: {TRAIN_START_DATE} to {TRAIN_END_DATE}")
    
    # 2. 创建环境
    print("\nCreating training environment...")
    env = create_env(train_df)
    print(f"Environment created successfully")
    print(f"Action space: {env.action_space}")
    print(f"Observation space: {env.observation_space}")
    
    # 3. 评估随机基线
    print("\nEvaluating random baseline...")
    random_performance = evaluate_random_agent(env, episodes=1)
    
    # 4. 训练智能体
    print("\nTraining RL agent...")
    model = train_agent(env, TOTAL_TIMESTEPS, use_lstm=True)
    
    # 5. 保存模型
    print(f"\nSaving model to {MODEL_SAVE_PATH}...")
    os.makedirs(os.path.dirname(MODEL_SAVE_PATH), exist_ok=True)
    model.save(MODEL_SAVE_PATH)
    print("Model saved successfully!")
    
    # 6. 快速评估训练好的模型
    print("\nQuick evaluation of trained model...")
    obs = env.reset()
    done = False
    episode_reward = 0
    step_count = 0
    
    while not done and step_count < 100:  # 限制步数避免过长
        action, _states = model.predict(obs, deterministic=True)
        obs, reward, done, info = env.step(action)
        episode_reward += reward[0]
        step_count += 1
        
        if step_count % 20 == 0:
            portfolio_value = env.envs[0].unwrapped.portfolio_value
            print(f"Step {step_count}: Portfolio Value = ${portfolio_value:.2f}")
    
    final_portfolio_value = env.envs[0].unwrapped.portfolio_value
    print(f"\nTraining completed!")
    print(f"Final portfolio value: ${final_portfolio_value:.2f}")
    print(f"Total return: {((final_portfolio_value - 10000) / 10000) * 100:.2f}%")
    
    # 7. 获取训练环境的性能统计
    if hasattr(env.envs[0].unwrapped, 'get_portfolio_performance'):
        performance = env.envs[0].unwrapped.get_portfolio_performance()
        print(f"\nTraining Performance:")
        for key, value in performance.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
            else:
                print(f"  {key}: {value}")
    
    print("\nNext steps:")
    print("1. Run 'python evaluate.py' to test the model on unseen data")
    print("2. Check 'training_progress.png' for training visualization")
    print("3. Modify hyperparameters in config.py and retrain if needed")


if __name__ == "__main__":
    main() 