# evaluate.py
"""
Evaluation and backtesting script for the E2E RL trading system.
This script evaluates the trained model on out-of-sample test data.
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from stable_baselines3 import PPO
from sb3_contrib import RecurrentPPO
from stable_baselines3.common.vec_env import DummyVecEnv
from stable_baselines3.common.monitor import Monitor
from typing import Dict, List, Tuple
import seaborn as sns

from src.data_manager import get_data
from src.environment import TradingEnv
from src.config import (
    TICKERS, TEST_START_DATE, TEST_END_DATE, 
    MODEL_SAVE_PATH, PLOT_FIGSIZE,
    LOGS_DIR, INITIAL_PORTFOLIO_VALUE
)

# 设置图表样式
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


def create_env(df: pd.DataFrame) -> DummyVecEnv:
    """
    创建评估环境
    
    Args:
        df: 测试数据
        
    Returns:
        DummyVecEnv: 向量化环境
    """
    def make_env():
        env = TradingEnv(df)
        env = Monitor(env)
        return env
    
    return DummyVecEnv([make_env])


def evaluate_model(model: PPO, env: DummyVecEnv, episodes: int = 1) -> Dict:
    """
    评估训练好的模型
    
    Args:
        model: 训练好的PPO模型
        env: 评估环境
        episodes: 评估轮数
        
    Returns:
        Dict: 评估结果
    """
    print(f"Evaluating model for {episodes} episodes...")
    
    results = {
        'portfolio_values': [],
        'weights_history': [],
        'returns': [],
        'actions': [],
        'rewards': [],
        'dates': []
    }
    
    for episode in range(episodes):
        obs = env.reset()
        done = False
        episode_reward = 0
        step_count = 0
        
        while not done:
            # 使用训练好的模型预测动作
            action, _states = model.predict(obs, deterministic=True)
            obs, reward, done, info = env.step(action)
            
            episode_reward += reward[0]
            step_count += 1
            
            # 记录历史数据
            trading_env = env.envs[0].unwrapped
            results['portfolio_values'].append(trading_env.portfolio_value)
            results['weights_history'].append(trading_env._weights.copy())
            results['actions'].append(action[0].copy())
            results['rewards'].append(reward[0])
            
            # 获取当前日期
            if hasattr(trading_env, '_current_tick') and hasattr(trading_env, 'dates'):
                current_date = trading_env.dates[min(trading_env._current_tick - 1, len(trading_env.dates) - 1)]
                results['dates'].append(current_date)
            
            # 计算单步收益率
            if len(results['portfolio_values']) > 1:
                prev_value = results['portfolio_values'][-2]
                current_value = results['portfolio_values'][-1]
                step_return = (current_value - prev_value) / prev_value if prev_value > 0 else 0
                results['returns'].append(step_return)
            else:
                results['returns'].append(0)
        
        print(f"Episode {episode + 1}: Total reward = {episode_reward:.2f}, Final portfolio value = ${trading_env.portfolio_value:.2f}")
    
    return results


def calculate_benchmark_performance(df: pd.DataFrame) -> Dict:
    """
    计算买入持有基准策略的表现
    
    Args:
        df: 测试数据
        
    Returns:
        Dict: 基准表现数据
    """
    print("Calculating buy-and-hold benchmark...")
    
    # 计算等权重买入持有策略
    pivot_df = df.pivot(index='Date', columns='Ticker', values='Close')
    pivot_df = pivot_df.loc[:, TICKERS]
    
    # 计算日收益率
    returns = pivot_df.pct_change().dropna()
    
    # 等权重组合收益率
    equal_weight_returns = returns.mean(axis=1)
    
    # 计算累积组合价值
    cumulative_returns = (1 + equal_weight_returns).cumprod()
    portfolio_values = cumulative_returns * INITIAL_PORTFOLIO_VALUE
    
    # 计算个股表现
    individual_performance = {}
    for ticker in TICKERS:
        stock_returns = (pivot_df[ticker] / pivot_df[ticker].iloc[0]) * INITIAL_PORTFOLIO_VALUE
        individual_performance[ticker] = stock_returns.values
    
    return {
        'portfolio_values': portfolio_values.values,
        'returns': equal_weight_returns.values,
        'dates': pivot_df.index.tolist(),
        'individual_stocks': individual_performance
    }


def plot_performance_comparison(rl_results: Dict, benchmark_results: Dict):
    """
    绘制性能比较图
    
    Args:
        rl_results: 强化学习结果
        benchmark_results: 基准结果
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 组合价值对比
    axes[0, 0].plot(rl_results['portfolio_values'], label='RL Agent', linewidth=2)
    axes[0, 0].plot(benchmark_results['portfolio_values'], label='Buy & Hold', linewidth=2)
    axes[0, 0].set_title('Portfolio Value Comparison')
    axes[0, 0].set_xlabel('Days')
    axes[0, 0].set_ylabel('Portfolio Value ($)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 累积收益率对比
    rl_cumulative = np.cumprod(1 + np.array(rl_results['returns'])) - 1
    benchmark_cumulative = np.cumprod(1 + benchmark_results['returns']) - 1
    
    axes[0, 1].plot(rl_cumulative * 100, label='RL Agent', linewidth=2)
    axes[0, 1].plot(benchmark_cumulative * 100, label='Buy & Hold', linewidth=2)
    axes[0, 1].set_title('Cumulative Returns Comparison')
    axes[0, 1].set_xlabel('Days')
    axes[0, 1].set_ylabel('Cumulative Return (%)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 权重变化图
    if len(rl_results['weights_history']) > 0:
        weights_array = np.array(rl_results['weights_history'])
        for i, ticker in enumerate(TICKERS):
            axes[1, 0].plot(weights_array[:, i], label=ticker, linewidth=2)
        axes[1, 0].plot(weights_array[:, -1], label='Cash', linewidth=2, linestyle='--')
        axes[1, 0].set_title('Portfolio Weights Over Time')
        axes[1, 0].set_xlabel('Days')
        axes[1, 0].set_ylabel('Weight')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 奖励分布
    if len(rl_results['rewards']) > 0:
        axes[1, 1].hist(rl_results['rewards'], bins=50, alpha=0.7, edgecolor='black')
        axes[1, 1].set_title('Reward Distribution')
        axes[1, 1].set_xlabel('Reward')
        axes[1, 1].set_ylabel('Frequency')
        axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(LOGS_DIR, 'evaluation_results.png'), dpi=300, bbox_inches='tight')
    plt.show()


def calculate_performance_metrics(results: Dict) -> Dict:
    """
    计算性能指标
    
    Args:
        results: 结果数据
        
    Returns:
        Dict: 性能指标
    """
    portfolio_values = np.array(results['portfolio_values'])
    returns = np.array(results['returns'])
    
    # 基本统计
    total_return = (portfolio_values[-1] - portfolio_values[0]) / portfolio_values[0]
    annualized_return = (1 + total_return) ** (252 / len(returns)) - 1
    volatility = np.std(returns) * np.sqrt(252)
    sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
    
    # 最大回撤
    peak = np.maximum.accumulate(portfolio_values)
    drawdown = (portfolio_values - peak) / peak
    max_drawdown = np.min(drawdown)
    
    # 胜率
    win_rate = np.sum(returns > 0) / len(returns) if len(returns) > 0 else 0
    
    # 平均持仓时间 (简化版)
    weights_array = np.array(results.get('weights_history', []))
    if len(weights_array) > 0:
        # 计算非现金仓位的平均权重
        avg_stock_weight = np.mean(np.sum(weights_array[:, :-1], axis=1))
    else:
        avg_stock_weight = 0
    
    return {
        'total_return': total_return,
        'annualized_return': annualized_return,
        'volatility': volatility,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'win_rate': win_rate,
        'avg_stock_weight': avg_stock_weight,
        'final_value': portfolio_values[-1],
        'total_trades': len(returns)
    }


def print_performance_summary(rl_metrics: Dict, benchmark_metrics: Dict):
    """
    打印性能总结
    
    Args:
        rl_metrics: 强化学习指标
        benchmark_metrics: 基准指标
    """
    print("\n" + "=" * 80)
    print("PERFORMANCE SUMMARY")
    print("=" * 80)
    
    metrics_names = [
        ('Total Return', 'total_return', '%'),
        ('Annualized Return', 'annualized_return', '%'),
        ('Volatility', 'volatility', '%'),
        ('Sharpe Ratio', 'sharpe_ratio', ''),
        ('Max Drawdown', 'max_drawdown', '%'),
        ('Win Rate', 'win_rate', '%'),
        ('Final Value', 'final_value', '$'),
    ]
    
    print(f"{'Metric':<20} {'RL Agent':<15} {'Buy & Hold':<15} {'Difference':<15}")
    print("-" * 80)
    
    for name, key, unit in metrics_names:
        rl_value = rl_metrics.get(key, 0)
        benchmark_value = benchmark_metrics.get(key, 0)
        difference = rl_value - benchmark_value
        
        if unit == '%':
            rl_str = f"{rl_value*100:.2f}%"
            benchmark_str = f"{benchmark_value*100:.2f}%"
            diff_str = f"{difference*100:.2f}%"
        elif unit == '$':
            rl_str = f"${rl_value:.2f}"
            benchmark_str = f"${benchmark_value:.2f}"
            diff_str = f"${difference:.2f}"
        else:
            rl_str = f"{rl_value:.4f}"
            benchmark_str = f"{benchmark_value:.4f}"
            diff_str = f"{difference:.4f}"
        
        print(f"{name:<20} {rl_str:<15} {benchmark_str:<15} {diff_str:<15}")
    
    print("\n" + "=" * 80)


def main():
    """主评估函数"""
    print("=" * 60)
    print("E2E RL Trading System - Model Evaluation")
    print("=" * 60)
    
    # 1. 创建必要的目录
    os.makedirs(LOGS_DIR, exist_ok=True)
    
    # 2. 检查模型是否存在
    if not os.path.exists(MODEL_SAVE_PATH + ".zip"):
        print(f"Error: Model file not found at {MODEL_SAVE_PATH}")
        print("Please run 'python train.py' first to train the model.")
        return
    
    # 2. 加载测试数据
    print("Loading test data...")
    test_df = get_data(TICKERS, TEST_START_DATE, TEST_END_DATE)
    print(f"Test data loaded: {len(test_df)} rows, {len(test_df['Date'].unique())} days")
    print(f"Date range: {TEST_START_DATE} to {TEST_END_DATE}")
    
    # 3. 加载训练好的模型
    print("Loading trained model...")
    try:
        # 首先尝试加载RecurrentPPO模型
        model = RecurrentPPO.load(MODEL_SAVE_PATH)
        print("RecurrentPPO model loaded successfully!")
    except:
        # 如果失败，尝试加载PPO模型
        try:
            model = PPO.load(MODEL_SAVE_PATH)
            print("PPO model loaded successfully!")
        except Exception as e:
            print(f"Error loading model: {e}")
            return
    
    # 4. 创建评估环境
    print("Creating evaluation environment...")
    eval_env = create_env(test_df)
    
    # 5. 评估模型
    print("Evaluating RL agent...")
    rl_results = evaluate_model(model, eval_env, episodes=1)
    
    # 6. 计算基准性能
    print("Calculating benchmark performance...")
    benchmark_results = calculate_benchmark_performance(test_df)
    
    # 7. 计算性能指标
    print("Calculating performance metrics...")
    rl_metrics = calculate_performance_metrics(rl_results)
    benchmark_metrics = calculate_performance_metrics(benchmark_results)
    
    # 8. 打印性能总结
    print_performance_summary(rl_metrics, benchmark_metrics)
    
    # 9. 绘制性能比较图
    print("Generating performance charts...")
    plot_performance_comparison(rl_results, benchmark_results)
    
    # 10. 保存结果
    print("Saving evaluation results...")
    results_df = pd.DataFrame({
        'Date': rl_results['dates'][:len(rl_results['portfolio_values'])],
        'RL_Portfolio_Value': rl_results['portfolio_values'],
        'RL_Returns': rl_results['returns'],
        'Benchmark_Portfolio_Value': benchmark_results['portfolio_values'][:len(rl_results['portfolio_values'])],
        'Benchmark_Returns': benchmark_results['returns'][:len(rl_results['returns'])]
    })
    results_df.to_csv(os.path.join(LOGS_DIR, 'evaluation_results.csv'), index=False)
    print(f"Results saved to '{os.path.join(LOGS_DIR, 'evaluation_results.csv')}'")
    
    # 11. 总结
    print("\nEvaluation Summary:")
    print(f"  - RL Agent Final Value: ${rl_metrics['final_value']:.2f}")
    print(f"  - Buy & Hold Final Value: ${benchmark_metrics['final_value']:.2f}")
    print(f"  - RL Agent Total Return: {rl_metrics['total_return']*100:.2f}%")
    print(f"  - Buy & Hold Total Return: {benchmark_metrics['total_return']*100:.2f}%")
    print(f"  - RL Agent Sharpe Ratio: {rl_metrics['sharpe_ratio']:.4f}")
    print(f"  - Buy & Hold Sharpe Ratio: {benchmark_metrics['sharpe_ratio']:.4f}")
    
    outperformance = rl_metrics['total_return'] - benchmark_metrics['total_return']
    print(f"  - Outperformance: {outperformance*100:.2f}%")
    
    print("\nFiles generated:")
    print("  - evaluation_results.png (performance charts)")
    print("  - evaluation_results.csv (detailed results)")
    
    if outperformance > 0:
        print("\n🎉 Congratulations! Your RL agent outperformed the buy-and-hold benchmark!")
    else:
        print("\n📈 The RL agent underperformed the benchmark. Consider:")
        print("   1. Increasing training timesteps")
        print("   2. Tuning hyperparameters")
        print("   3. Improving the reward function")
        print("   4. Adding more features to the state space")


if __name__ == "__main__":
    main() 