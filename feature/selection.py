#!/usr/bin/env python3
"""
特征选择模块
专注于机器学习驱动的特征选择算法
"""

import pandas as pd
import numpy as np
from typing import List, Tuple, Optional
from sklearn.feature_selection import mutual_info_classif
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import roc_auc_score
import xgboost as xgb
import warnings

warnings.filterwarnings('ignore')

class FeatureSelector:
    """智能特征选择器"""
    
    def __init__(self, random_state: int = 42):
        self.random_state = random_state
    
    def select_by_xgboost(self, X: pd.DataFrame, y: pd.Series, top_k: int = 20) -> List[str]:
        """
        基于XGBoost特征重要性的特征选择
        
        Args:
            X: 特征矩阵
            y: 目标变量
            top_k: 选择的特征数量
        
        Returns:
            选择的特征名称列表
        """
        try:
            # 强化数据清洗 - 解决无穷值问题
            X_clean = X.copy()
            
            # 1. 移除无穷值和极值
            X_clean = X_clean.replace([np.inf, -np.inf], np.nan)
            
            # 2. 移除极值（超过99.9%分位数的值）
            for col in X_clean.select_dtypes(include=[np.number]).columns:
                q99 = X_clean[col].quantile(0.999)
                q01 = X_clean[col].quantile(0.001)
                X_clean[col] = X_clean[col].clip(lower=q01, upper=q99)
            
            # 3. 填充缺失值
            X_clean = X_clean.fillna(X_clean.median())
            
            # 4. 最终检查
            if X_clean.isin([np.inf, -np.inf]).any().any():
                print("警告: 仍有无穷值，使用0填充")
                X_clean = X_clean.replace([np.inf, -np.inf], 0)
            
            model = xgb.XGBClassifier(
                n_estimators=100,
                max_depth=6,
                random_state=self.random_state,
                eval_metric='logloss',
                n_jobs=1,  # 单线程保证一致性
                verbosity=0
            )
            model.fit(X_clean, y)
            
            feature_importance = pd.DataFrame({
                'feature': X.columns,
                'importance': model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            return feature_importance.head(top_k)['feature'].tolist()
            
        except Exception as e:
            print(f"XGBoost特征选择失败: {e}")
            return X.columns[:top_k].tolist()
    
    def select_by_mutual_info(self, X: pd.DataFrame, y: pd.Series, top_k: int = 20) -> List[str]:
        """
        基于互信息的特征选择
        
        Args:
            X: 特征矩阵
            y: 目标变量
            top_k: 选择的特征数量
        
        Returns:
            选择的特征名称列表
        """
        try:
            # 强化数据清洗 - 解决无穷值问题
            X_clean = X.copy()
            
            # 1. 移除无穷值
            X_clean = X_clean.replace([np.inf, -np.inf], np.nan)
            
            # 2. 极值裁剪
            for col in X_clean.select_dtypes(include=[np.number]).columns:
                q99 = X_clean[col].quantile(0.999)
                q01 = X_clean[col].quantile(0.001)
                X_clean[col] = X_clean[col].clip(lower=q01, upper=q99)
            
            # 3. 填充缺失值
            X_clean = X_clean.fillna(X_clean.median())
            y_clean = y.fillna(0)
            
            mi_scores = mutual_info_classif(X_clean, y_clean, random_state=self.random_state)
            mi_df = pd.DataFrame({
                'feature': X.columns,
                'mi_score': mi_scores
            }).sort_values('mi_score', ascending=False)
            
            return mi_df.head(top_k)['feature'].tolist()
            
        except Exception as e:
            print(f"互信息特征选择失败: {e}")
            return X.columns[:top_k].tolist()
    
    def select_combined(self, X: pd.DataFrame, y: pd.Series, top_k: int = 20) -> Tuple[List[str], float]:
        """
        组合特征选择方法（XGBoost + 互信息）
        
        Args:
            X: 特征矩阵
            y: 目标变量
            top_k: 选择的特征数量
        
        Returns:
            (选择的特征列表, 验证AUC)
        """
        # 数据预处理
        X_clean = X.fillna(0)
        y_clean = y.fillna(0)
        
        if len(X_clean) < 100 or len(y_clean.unique()) < 2:
            print("数据不足，返回前N个特征")
            return X.columns[:top_k].tolist(), 0.5
        
        # 确保随机种子一致性
        np.random.seed(self.random_state)
        xgb_features = self.select_by_xgboost(X_clean, y_clean, top_k)
        
        np.random.seed(self.random_state)  # 重新设置种子
        mi_features = self.select_by_mutual_info(X_clean, y_clean, top_k)
        
        # 特征组合：优先交集，不足时从并集补充
        intersection = sorted(list(set(xgb_features) & set(mi_features)))
        union = sorted(list(set(xgb_features) | set(mi_features)))
        
        selected_features = intersection.copy()
        for feature in union:
            if feature not in selected_features and len(selected_features) < top_k:
                selected_features.append(feature)
        
        # 验证特征质量
        validation_auc = self._validate_features(X_clean[selected_features[:top_k]], y_clean)
        
        return selected_features[:top_k], validation_auc
    
    def select_time_series_aware(self, X: pd.DataFrame, y: pd.Series, 
                                timestamps: pd.Series, top_k: int = 20, 
                                n_splits: int = 5) -> Tuple[List[str], float]:
        """
        时序感知的特征选择
        使用时序交叉验证评估特征质量
        
        Args:
            X: 特征矩阵
            y: 目标变量
            timestamps: 时间戳序列
            top_k: 选择的特征数量  
            n_splits: 交叉验证折数
        
        Returns:
            (选择的特征列表, 平均验证AUC)
        """
        # 数据预处理
        X_clean = X.fillna(0)
        y_clean = y.fillna(0)
        
        if len(X_clean) < 100 or len(y_clean.unique()) < 2:
            print("数据不足，使用组合方法")
            return self.select_combined(X, y, top_k)
        
        print(f"时序特征选择: {len(X_clean)} 样本, {len(X.columns)} 特征")
        
        # 时序交叉验证
        tscv = TimeSeriesSplit(n_splits=n_splits)
        feature_scores = {}
        validation_aucs = []
        
        for train_idx, val_idx in tscv.split(X_clean):
            X_train, X_val = X_clean.iloc[train_idx], X_clean.iloc[val_idx]
            y_train, y_val = y_clean.iloc[train_idx], y_clean.iloc[val_idx]
            
            # 检查目标变量分布
            if len(y_train.unique()) < 2 or len(y_val.unique()) < 2:
                continue
            
            try:
                # XGBoost特征重要性
                model = xgb.XGBClassifier(
                    n_estimators=100,
                    max_depth=4,
                    random_state=self.random_state,
                    objective='binary:logistic',
                    eval_metric='auc',
                    verbosity=0
                )
                model.fit(X_train, y_train)
                
                # 累计特征重要性
                importance = model.feature_importances_
                for i, feature in enumerate(X.columns):
                    if feature not in feature_scores:
                        feature_scores[feature] = []
                    feature_scores[feature].append(importance[i])
                
                # 计算验证AUC
                y_pred_proba = model.predict_proba(X_val)[:, 1]
                auc = roc_auc_score(y_val, y_pred_proba)
                validation_aucs.append(auc)
                
            except Exception as e:
                print(f"交叉验证轮次失败: {e}")
                continue
        
        # 选择最优特征
        if not feature_scores:
            print("时序验证失败，使用组合方法")
            return self.select_combined(X, y, top_k)
        
        # 计算平均重要性并选择特征
        avg_scores = {feature: np.mean(scores) for feature, scores in feature_scores.items()}
        selected_features = sorted(avg_scores.keys(), key=avg_scores.get, reverse=True)[:top_k]
        
        # 平均验证AUC
        mean_auc = np.mean(validation_aucs) if validation_aucs else 0.5
        
        return selected_features, mean_auc
    
    def _validate_features(self, X: pd.DataFrame, y: pd.Series) -> float:
        """
        验证选择的特征质量
        使用简单的80-20分割
        """
        try:
            if len(X) < 100 or len(y.unique()) < 2:
                return 0.5
            
            # 时序分割：前80%训练，后20%验证
            split_idx = int(len(X) * 0.8)
            X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
            y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
            
            if len(y_train.unique()) < 2 or len(y_test.unique()) < 2:
                return 0.5
            
            model = xgb.XGBClassifier(
                n_estimators=100,
                max_depth=4,
                random_state=self.random_state,
                objective='binary:logistic',
                eval_metric='auc',
                verbosity=0
            )
            model.fit(X_train, y_train)
            
            y_pred_proba = model.predict_proba(X_test)[:, 1]
            auc = roc_auc_score(y_test, y_pred_proba)
            
            return auc
            
        except Exception as e:
            return 0.5

class FeatureRiskAnalyzer:
    """特征风险分析器 - 专门用于检测数据泄漏"""
    
    @staticmethod
    def analyze_temporal_risk(feature_cols: List[str]) -> dict:
        """
        分析特征的时间泄漏风险
        
        Args:
            feature_cols: 特征列名列表
        
        Returns:
            按风险级别分类的特征字典
        """
        risk_assessment = {
            'high_risk': [],      # 高风险：包含当日数据
            'medium_risk': [],    # 中风险：可能包含当日数据  
            'low_risk': [],       # 低风险：技术指标
            'safe': []           # 安全：明确的滞后特征
        }
        
        for col in feature_cols:
            col_lower = col.lower()
            
            # 高风险特征：直接使用当日价格/成交量
            if any(keyword in col_lower for keyword in [
                'price_change', 'volume_change', 'trading_intensity', 
                'volume_price_trend', 'price_breakthrough'
            ]):
                if 'lag' not in col_lower:
                    risk_assessment['high_risk'].append(col)
                else:
                    risk_assessment['safe'].append(col)
            
            # 中风险特征：可能包含当日数据
            elif any(keyword in col_lower for keyword in ['close', 'open', 'high', 'low', 'volume']):
                if 'lag' not in col_lower and col_lower not in ['close_lag1', 'volume_lag1', 'high_lag1', 'low_lag1']:
                    risk_assessment['medium_risk'].append(col)
                else:
                    risk_assessment['safe'].append(col)
            
            # 低风险特征：技术指标（通常基于历史数据）
            elif any(keyword in col_lower for keyword in ['sma', 'ema', 'rsi', 'macd', 'bb_', 'atr']):
                risk_assessment['low_risk'].append(col)
            
            # 安全特征：明确标记为滞后的特征
            elif 'lag' in col_lower or any(suffix in col_lower for suffix in ['_lag1', '_5d', '_10d', '_20d']):
                risk_assessment['safe'].append(col)
            
            else:
                risk_assessment['low_risk'].append(col)
        
        return risk_assessment
    
    @staticmethod
    def get_safe_features(feature_cols: List[str], risk_level: str = 'low') -> List[str]:
        """
        获取安全特征列表
        
        Args:
            feature_cols: 特征列名列表
            risk_level: 风险级别 ('safe', 'low', 'medium', 'high')
        
        Returns:
            安全特征列表
        """
        risk_assessment = FeatureRiskAnalyzer.analyze_temporal_risk(feature_cols)
        
        if risk_level == 'safe':
            return risk_assessment['safe']
        elif risk_level == 'low':
            return risk_assessment['safe'] + risk_assessment['low_risk']
        elif risk_level == 'medium':
            return (risk_assessment['safe'] + risk_assessment['low_risk'] + 
                   risk_assessment['medium_risk'])
        else:  # 'high' - 返回所有特征
            return feature_cols
    
    @staticmethod
    def print_risk_summary(feature_cols: List[str]):
        """打印风险分析摘要"""
        risk_assessment = FeatureRiskAnalyzer.analyze_temporal_risk(feature_cols)
        
        print(f"\n=== 特征风险分析 ===")
        print(f"总特征数: {len(feature_cols)}")
        
        if risk_assessment['high_risk']:
            print(f"🔴 高风险特征 ({len(risk_assessment['high_risk'])}): {risk_assessment['high_risk'][:3]}{'...' if len(risk_assessment['high_risk']) > 3 else ''}")
        
        if risk_assessment['medium_risk']:
            print(f"⚠️  中风险特征 ({len(risk_assessment['medium_risk'])}): {risk_assessment['medium_risk'][:3]}{'...' if len(risk_assessment['medium_risk']) > 3 else ''}")
        
        print(f"✅ 安全特征: {len(risk_assessment['safe'])} 个")
        print(f"✅ 低风险特征: {len(risk_assessment['low_risk'])} 个")