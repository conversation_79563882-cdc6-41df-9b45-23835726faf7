"""
Feature Engineering Module
特征工程模块 - 模块化设计

模块结构:
- indicators.py: 技术指标计算
- selection.py: 特征选择算法  
- engineering.py: 主流程调度器

使用方式:
from feature.engineering import FeatureEngineer
"""

from .engineering import FeatureEngineer
from .indicators import TechnicalIndicators, AdvancedIndicators
from .selection import FeatureSelector, FeatureRiskAnalyzer

__version__ = "2.0.0"
__author__ = "Quantitative Trading System"

__all__ = [
    'FeatureEngineer',
    'TechnicalIndicators', 
    'AdvancedIndicators',
    'FeatureSelector',
    'FeatureRiskAnalyzer'
]