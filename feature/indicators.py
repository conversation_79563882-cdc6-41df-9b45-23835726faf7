#!/usr/bin/env python3
"""
技术指标计算模块
专注于纯技术指标计算，确保时间安全性，避免数据泄漏
"""

import pandas as pd
import numpy as np
from typing import Tuple, Optional
import warnings

warnings.filterwarnings('ignore')

class TechnicalIndicators:
    """技术指标计算器 - 时间安全版本"""
    
    @staticmethod
    def sma(data: pd.Series, window: int) -> pd.Series:
        """简单移动平均"""
        return data.rolling(window=window).mean()
    
    @staticmethod
    def ema(data: pd.Series, window: int) -> pd.Series:
        """指数移动平均"""
        return data.ewm(span=window).mean()
    
    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """MACD指标"""
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        macd = ema_fast - ema_slow
        macd_signal = TechnicalIndicators.ema(macd, signal)
        macd_histogram = macd - macd_signal
        return macd, macd_signal, macd_histogram
    
    @staticmethod
    def rsi(data: pd.Series, window: int = 14) -> pd.Series:
        """相对强弱指数"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    @staticmethod
    def bollinger_bands(data: pd.Series, window: int = 20, num_std: float = 2) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """布林带"""
        sma = TechnicalIndicators.sma(data, window)
        std = data.rolling(window=window).std()
        upper = sma + (std * num_std)
        lower = sma - (std * num_std)
        return upper, sma, lower
    
    @staticmethod
    def atr(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
        """平均真实范围"""
        high_low = high - low
        high_close = np.abs(high - close.shift())
        low_close = np.abs(low - close.shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        return true_range.rolling(window=window).mean()
    
    @staticmethod
    def momentum(data: pd.Series, period: int = 10) -> pd.Series:
        """动量指标"""
        return data - data.shift(period)
    
    @staticmethod
    def volatility(data: pd.Series, period: int = 20) -> pd.Series:
        """波动率指标"""
        return data.pct_change().rolling(window=period).std()
    
    @staticmethod
    def price_position(close: pd.Series, high: pd.Series, low: pd.Series, period: int = 14) -> pd.Series:
        """价格位置指标"""
        highest = high.rolling(window=period).max()
        lowest = low.rolling(window=period).min()
        return (close - lowest) / (highest - lowest)

class AdvancedIndicators:
    """高级技术指标计算器"""
    
    @staticmethod
    def calculate_comprehensive_features(df: pd.DataFrame) -> pd.DataFrame:
        """
        计算全套技术指标特征 - 时间安全版本
        严格使用滞后数据避免数据泄漏
        """
        df = df.copy()
        
        # 确保保留重要的标识列
        required_cols = ['timestamp', 'date', 'symbol']
        preserved_cols = {col: df[col] for col in required_cols if col in df.columns}
        
        # 数据类型转换
        numeric_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # === 时间安全特征（修复数据泄漏，使用已知数据）===
        # 当日开盘价 + 前一日高低收盘的混合特征
        df['ohlc_avg'] = (df['open'] + df['high'].shift(1) + df['low'].shift(1) + df['close'].shift(1)) / 4
        df['hl_avg'] = (df['high'].shift(1) + df['low'].shift(1)) / 2  
        df['price_change'] = df['close'].shift(1).pct_change()
        df['volume_change'] = df['volume'].shift(1).pct_change()
        df['price_range'] = df['high'].shift(1) - df['low'].shift(1)
        
        # === 纯滞后特征（完全基于前一日数据）===
        df['ohlc_avg_lag1'] = (df['open'].shift(1) + df['high'].shift(1) + df['low'].shift(1) + df['close'].shift(1)) / 4
        df['hl_avg_lag1'] = (df['high'].shift(1) + df['low'].shift(1)) / 2
        df['price_change_lag1'] = df['close'].shift(1).pct_change()
        df['volume_change_lag1'] = df['volume'].shift(1).pct_change()
        df['price_range_lag1'] = df['high'].shift(1) - df['low'].shift(1)
        
        # === 趋势指标（基于滞后数据）===
        for window in [5, 10, 20, 55, 233]:
            df[f'sma_{window}'] = TechnicalIndicators.sma(df['close'].shift(1), window)
        
        for window in [12, 26]:
            df[f'ema_{window}'] = TechnicalIndicators.ema(df['close'].shift(1), window)
        
        # === MACD指标（基于滞后数据）===
        macd, macd_signal, macd_histogram = TechnicalIndicators.macd(df['close'].shift(1))
        df['macd'], df['macd_signal'], df['macd_histogram'] = macd, macd_signal, macd_histogram
        
        # === 动量指标（基于滞后数据）===
        df['rsi'] = TechnicalIndicators.rsi(df['close'].shift(1))
        df['price_momentum'] = TechnicalIndicators.momentum(df['close'].shift(1), 10)
        
        # === 布林带（基于滞后数据）===
        bb_upper, bb_middle, bb_lower = TechnicalIndicators.bollinger_bands(df['close'].shift(1))
        df['bb_upper'], df['bb_middle'], df['bb_lower'] = bb_upper, bb_middle, bb_lower
        df['bb_position'] = (df['close'].shift(1) - bb_lower) / (bb_upper - bb_lower)
        
        # === 波动率指标（基于滞后数据）===
        df['atr'] = TechnicalIndicators.atr(df['high'].shift(1), df['low'].shift(1), df['close'].shift(1))
        df['volatility'] = TechnicalIndicators.volatility(df['close'].shift(1), 20)
        
        # === 成交量指标（基于滞后数据）===
        df['volume_sma'] = TechnicalIndicators.sma(df['volume'].shift(1), 20)
        df['trading_intensity'] = df['volume'].shift(1) / df['volume_sma']  # 修复：使用前一日volume
        df['volume_price_trend'] = (df['close'].shift(1) - df['close'].shift(2)) * df['volume'].shift(1)  # 修复：使用前一日数据
        
        # === 移除重复特征（已在上面修复为time-safe版本）===
        # trading_intensity 和 volume_price_trend 已修复，无需lag1版本
        
        # === 均线差异特征===
        df['sma_diff_20_55'] = df['sma_20'] - df['sma_55']
        df['ema_diff_12_26'] = df['ema_12'] - df['ema_26']
        
        # === 价格相对位置（基于滞后数据）===
        df['price_position'] = TechnicalIndicators.price_position(df['close'].shift(1), df['high'].shift(1), df['low'].shift(1), 14)
        
        # === 高级支撑阻力特征===
        df['resistance_strength'] = df['high'].shift(1).rolling(20).max() / df['close'].shift(1) - 1
        df['support_strength'] = 1 - df['low'].shift(1).rolling(20).min() / df['close'].shift(1)
        
        # === 价格与关键均线的关系（专业金融特征）===
        df['price_to_sma55_pct'] = (df['close'].shift(1) - df['sma_55']) / df['sma_55']
        df['proximity_to_sma55'] = np.abs(df['price_to_sma55_pct'])
        
        # === 均线支撑/压力强度===
        proximity_threshold = 0.02  # 2%阈值
        df['sma55_position'] = np.where(df['proximity_to_sma55'] <= proximity_threshold, 0,
                                       np.where(df['price_to_sma55_pct'] > 0, 1, -1))
        df['sma55_support_strength'] = df['proximity_to_sma55'] * df['trading_intensity']
        
        # === 缺口特征===
        df['gap_up'] = (df['open'] > df['close'].shift(1)).astype(int)
        df['gap_down'] = (df['open'] < df['close'].shift(1)).astype(int)
        df['gap_size'] = np.abs(df['open'] - df['close'].shift(1)) / df['close'].shift(1)
        
        # === 多时间框架特征===
        for period in [5, 10, 20]:
            df[f'high_{period}d'] = df['high'].shift(1).rolling(period).max()
            df[f'low_{period}d'] = df['low'].shift(1).rolling(period).min()
            df[f'return_{period}d'] = df['close'].shift(1).pct_change(period)
        
        # === 高级技术分析特征===
        df['price_breakthrough_strength'] = np.abs(df['price_change']) * df['trading_intensity']
        df['price_breakthrough_strength_lag1'] = np.abs(df['price_change_lag1']) * df['trading_intensity']
        
        # === 均线多头/空头排列信号===
        df['ma_bullish_signal'] = np.where(
            (df['sma_20'] > df['sma_55']), 1,
            np.where((df['sma_20'] < df['sma_55']), -1, 0)
        )
        
        # === 价格与多条均线的综合位置===
        df['price_above_key_ma'] = np.where(
            (df['close'].shift(1) > df['sma_20']) & (df['close'].shift(1) > df['sma_55']), 1,
            np.where((df['close'].shift(1) < df['sma_20']) & (df['close'].shift(1) < df['sma_55']), -1, 0)
        )
        
        # === 滞后特征（避免数据泄漏）===
        df['close_lag1'] = df['close'].shift(1)
        df['volume_lag1'] = df['volume'].shift(1)
        df['high_lag1'] = df['high'].shift(1)
        df['low_lag1'] = df['low'].shift(1)
        df['range_lag1'] = df['price_range'].shift(1)
        
        # === 短期价格动量特征===
        df['price_momentum_3d'] = (df['close_lag1'] / df['close'].shift(3) - 1)
        df['price_momentum_5d'] = (df['close_lag1'] / df['close'].shift(5) - 1)
        
        # === 新增：市场相对强度和微观结构特征 ===
        try:
            market_indicators = MarketRelativeIndicators()
            # 添加所有高级特征（相对强度 + 微观结构 + 市场环境）
            df = market_indicators.calculate_all_advanced_features(df)
            
            # === 强化数据清洗 - 修复无穷值问题 ===
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            
            # 1. 移除无穷值
            df[numeric_cols] = df[numeric_cols].replace([np.inf, -np.inf], np.nan)
            
            # 2. 极值裁剪 (0.1% - 99.9%)
            for col in numeric_cols:
                if col not in ['target', 'symbol']:  # 保护目标变量和symbol
                    q01 = df[col].quantile(0.001)
                    q99 = df[col].quantile(0.999)
                    df[col] = df[col].clip(lower=q01, upper=q99)
            
            # 3. 中位数填充剩余缺失值
            for col in numeric_cols:
                if df[col].isnull().sum() > 0:
                    df[col] = df[col].fillna(df[col].median())
                    
            print(f"✅ 成功添加高级特征，特征数量: {df.shape[1]}")
        except Exception as e:
            print(f"⚠️  高级特征计算失败，继续使用基础特征: {e}")
        
        # 确保重要的标识列被保留
        for col, data in preserved_cols.items():
            if col not in df.columns:
                df[col] = data
        
        return df

class DataValidator:
    """数据质量验证器"""
    
    @staticmethod
    def validate_temporal_consistency(df: pd.DataFrame, feature_cols: list) -> dict:
        """
        验证特征的时间一致性，检测潜在数据泄漏
        """
        risk_assessment = {
            'high_risk': [],      # 明确包含当日数据
            'medium_risk': [],    # 可能包含当日数据
            'low_risk': [],       # 使用滞后数据
            'safe': []           # 明确的滞后特征
        }
        
        for col in feature_cols:
            col_lower = col.lower()
            
            # 高风险特征：直接使用当日价格/成交量数据
            if any(keyword in col_lower for keyword in ['price_change', 'volume_change', 'trading_intensity', 
                                                        'volume_price_trend', 'price_breakthrough']):
                if 'lag' not in col_lower:
                    risk_assessment['high_risk'].append(col)
                else:
                    risk_assessment['safe'].append(col)
            
            # 中风险特征：可能间接包含当日数据
            elif any(keyword in col_lower for keyword in ['close', 'open', 'high', 'low', 'volume']):
                if 'lag' not in col_lower and col_lower not in ['close_lag1', 'volume_lag1', 'high_lag1', 'low_lag1']:
                    risk_assessment['medium_risk'].append(col)
                else:
                    risk_assessment['safe'].append(col)
            
            # 低风险特征：技术指标（通常基于历史数据）
            elif any(keyword in col_lower for keyword in ['sma', 'ema', 'rsi', 'macd', 'bb_', 'atr']):
                risk_assessment['low_risk'].append(col)
            
            # 安全特征：明确标记为滞后的特征
            elif 'lag' in col_lower or any(suffix in col_lower for suffix in ['_lag1', '_5d', '_10d', '_20d']):
                risk_assessment['safe'].append(col)
            
            else:
                risk_assessment['low_risk'].append(col)
        
        return risk_assessment
    
    @staticmethod
    def clean_data(df: pd.DataFrame) -> pd.DataFrame:
        """数据清洗"""
        # 移除无限值
        df = df.replace([np.inf, -np.inf], np.nan)
        
        # 前向填充缺失值
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        df[numeric_cols] = df[numeric_cols].ffill()
        
        # 移除仍有缺失值的行
        df = df.dropna()
        
        return df

def calculate_target_variable(df: pd.DataFrame, window: int = 10, threshold: float = 0.05) -> pd.DataFrame:
    """
    计算目标变量 - 未来收益率
    
    Args:
        df: 包含股票数据的DataFrame
        window: 预测窗口（天数）
        threshold: 涨幅阈值
    """
    df = df.copy()
    
    # 按股票分组计算未来收益率（避免跨股票计算）
    df['future_return'] = df.groupby('symbol')['close'].transform(
        lambda x: (x.shift(-window) - x) / x
    )
    
    # 目标：未来N天涨幅超过阈值
    df['target'] = (df['future_return'] > threshold).astype(int)
    
    return df


class MarketRelativeIndicators:
    """市场相对强度和微观结构特征计算器"""
    
    def __init__(self, data_dir: str = "data"):
        """
        初始化相对强度计算器
        
        Args:
            data_dir: 数据目录路径
        """
        from pathlib import Path
        self.data_dir = Path(data_dir)
        self._spy_data = None
        
    def load_spy_data(self, timeframe: str = "1d") -> Optional[pd.DataFrame]:
        """
        加载SPY基准数据
        
        Args:
            timeframe: 时间周期
            
        Returns:
            SPY数据DataFrame或None
        """
        try:
            spy_file = self.data_dir / "us" / "SPY" / f"{timeframe}.parquet"
            if spy_file.exists():
                spy_df = pd.read_parquet(spy_file)
                # 确保有timestamp和close列
                if 'timestamp' in spy_df.columns and 'close' in spy_df.columns:
                    spy_df['timestamp'] = pd.to_datetime(spy_df['timestamp'])
                    spy_df = spy_df.set_index('timestamp').sort_index()
                    return spy_df[['close']].rename(columns={'close': 'spy_close'})
            return None
        except Exception as e:
            print(f"加载SPY数据失败: {e}")
            return None
    
    def calculate_relative_strength_features(self, df: pd.DataFrame, 
                                           spy_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        计算相对强度特征
        
        Args:
            df: 股票数据DataFrame
            spy_data: SPY基准数据
            
        Returns:
            添加相对强度特征的DataFrame
        """
        if spy_data is None:
            spy_data = self.load_spy_data()
        
        if spy_data is None:
            print("警告: 无法获取SPY数据，跳过相对强度特征")
            return df
            
        df = df.copy()
        
        # 确保时间索引
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.set_index('timestamp')
        
        # 合并SPY数据（检查是否已存在spy_close列）
        if 'spy_close' not in df.columns:
            df = df.join(spy_data, how='left')
        
        # 前向填充SPY数据的缺失值
        if 'spy_close' in df.columns:
            df['spy_close'] = df['spy_close'].ffill()
        
        # === 相对收益率特征（多时间窗口）===
        for window in [5, 10, 20, 60]:
            # 股票收益率
            stock_return = df['close'].pct_change(window).shift(1)  # 使用滞后数据
            # SPY收益率
            spy_return = df['spy_close'].pct_change(window).shift(1)
            # 相对强度 = 股票收益率 - 市场收益率
            df[f'relative_return_{window}d'] = stock_return - spy_return
            # 相对强度比率
            df[f'relative_ratio_{window}d'] = stock_return / (spy_return + 1e-8)
        
        # === 相对技术指标特征 ===
        # 相对RSI (股票RSI / SPY RSI)
        stock_rsi = TechnicalIndicators.rsi(df['close'].shift(1), 14)
        spy_rsi = TechnicalIndicators.rsi(df['spy_close'].shift(1), 14)
        df['relative_rsi'] = stock_rsi / (spy_rsi + 1e-8)
        
        # 相对MACD
        stock_macd, _, _ = TechnicalIndicators.macd(df['close'].shift(1))
        spy_macd, _, _ = TechnicalIndicators.macd(df['spy_close'].shift(1))
        df['relative_macd'] = stock_macd / (spy_macd + 1e-8)
        
        # === 相对波动率特征 ===
        for window in [10, 20]:
            stock_vol = TechnicalIndicators.volatility(df['close'].shift(1), window)
            spy_vol = TechnicalIndicators.volatility(df['spy_close'].shift(1), window)
            df[f'relative_volatility_{window}d'] = stock_vol / (spy_vol + 1e-8)
        
        return df
    
    def calculate_microstructure_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算微观结构特征
        
        Args:
            df: 股票数据DataFrame
            
        Returns:
            添加微观结构特征的DataFrame
        """
        df = df.copy()
        
        # === 价格跳跃检测特征 ===
        # 修复：使用前一日价格范围相对于历史的异常程度
        historical_range_avg = ((df['high'] - df['low']) / df['close']).rolling(20).mean().shift(2)  # 再向前一日
        prev_range = (df['high'].shift(1) - df['low'].shift(1)) / df['close'].shift(1)  # 前一日范围
        df['price_jump_intensity'] = prev_range / (historical_range_avg + 1e-8)
        
        # Gap特征（开盘价相对于前一日收盘价的跳跃）
        gap_size = (df['open'] - df['close'].shift(1)) / df['close'].shift(1)
        df['gap_size'] = gap_size
        df['gap_up'] = (gap_size > 0.02).astype(int)  # 上跳2%以上
        df['gap_down'] = (gap_size < -0.02).astype(int)  # 下跳2%以上
        
        # === 成交量异常特征 ===
        # 修复：使用前一日成交量相对于移动平均的异常程度
        volume_ma = df['volume'].rolling(20).mean().shift(2)  # 再向前一日
        df['volume_anomaly'] = df['volume'].shift(1) / (volume_ma + 1e-8)  # 使用前一日volume
        df['high_volume_flag'] = (df['volume_anomaly'] > 2.0).astype(int)  # 异常高成交量
        
        # 修复：成交量价格确认度 - 使用前一日数据
        price_change = df['close'].shift(1).pct_change()
        volume_change = df['volume'].shift(1).pct_change()
        df['volume_price_confirmation'] = np.sign(price_change) * np.sign(volume_change)
        
        # === 日内波动模式特征 ===
        # 修复：使用前一日的日内波动范围
        df['intraday_volatility'] = (df['high'].shift(1) - df['low'].shift(1)) / df['open'].shift(1)
        df['intraday_vol_ma'] = df['intraday_volatility'].rolling(10).mean().shift(1)
        df['intraday_vol_ratio'] = df['intraday_volatility'] / (df['intraday_vol_ma'] + 1e-8)
        
        # 修复：前一日收盘价在前一日区间的位置
        df['close_position_in_range'] = (df['close'].shift(1) - df['low'].shift(1)) / (df['high'].shift(1) - df['low'].shift(1) + 1e-8)
        
        # === 价格动量衰减特征 ===
        # 多时间窗口动量的衰减模式
        for short, long in [(3, 10), (5, 20), (10, 60)]:
            short_momentum = df['close'].pct_change(short).shift(1)
            long_momentum = df['close'].pct_change(long).shift(1)
            df[f'momentum_decay_{short}_{long}'] = short_momentum / (long_momentum + 1e-8)
        
        return df
    
    def calculate_market_environment_features(self, df: pd.DataFrame, 
                                            spy_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        计算市场环境特征
        
        Args:
            df: 股票数据DataFrame  
            spy_data: SPY基准数据
            
        Returns:
            添加市场环境特征的DataFrame
        """
        if spy_data is None:
            spy_data = self.load_spy_data()
            
        if spy_data is None:
            print("警告: 无法获取SPY数据，跳过市场环境特征")
            return df
            
        df = df.copy()
        
        # 确保时间索引
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.set_index('timestamp')
        
        # 合并SPY数据（检查是否已存在spy_close列）
        if 'spy_close' not in df.columns:
            df = df.join(spy_data, how='left')
        
        # 前向填充SPY数据的缺失值  
        if 'spy_close' in df.columns:
            df['spy_close'] = df['spy_close'].ffill()
        
        # === 市场趋势强度特征 ===
        # SPY的趋势强度（作为市场环境指标）
        for window in [5, 10, 20]:
            spy_sma = df['spy_close'].rolling(window).mean().shift(1)
            df[f'market_trend_strength_{window}d'] = (df['spy_close'].shift(1) - spy_sma) / spy_sma
        
        # === 市场波动率制度特征 ===
        # 使用SPY波动率作为市场波动率制度的代理
        spy_volatility = TechnicalIndicators.volatility(df['spy_close'].shift(1), 20)
        spy_vol_ma = spy_volatility.rolling(60).mean()
        df['market_volatility_regime'] = spy_volatility / (spy_vol_ma + 1e-8)
        
        # 波动率制度分类
        df['high_vol_regime'] = (df['market_volatility_regime'] > 1.5).astype(int)
        df['low_vol_regime'] = (df['market_volatility_regime'] < 0.7).astype(int)
        
        # === 市场宽度指标（使用SPY技术指标作为代理）===
        # SPY相对于其移动平均线的位置
        spy_sma_55 = df['spy_close'].rolling(55).mean().shift(1)
        df['market_breadth_proxy'] = (df['spy_close'].shift(1) - spy_sma_55) / spy_sma_55
        
        # 市场动量指标
        df['market_momentum_5d'] = df['spy_close'].pct_change(5).shift(1)
        df['market_momentum_20d'] = df['spy_close'].pct_change(20).shift(1)
        
        return df
    
    def calculate_all_advanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算所有高级特征（相对强度 + 微观结构 + 市场环境）
        
        Args:
            df: 股票数据DataFrame
            
        Returns:
            添加所有高级特征的DataFrame
        """
        # 加载SPY数据一次，避免重复加载
        spy_data = self.load_spy_data()
        
        # 逐步添加各类特征
        df = self.calculate_relative_strength_features(df, spy_data)
        df = self.calculate_microstructure_features(df)
        df = self.calculate_market_environment_features(df, spy_data)
        
        # 确保timestamp列作为普通列而不是索引
        if df.index.name == 'timestamp':
            df = df.reset_index()
        
        return df