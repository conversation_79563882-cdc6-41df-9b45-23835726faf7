#!/usr/bin/env python3
"""
特征工程模块 - 主流程调度器
功能: 协调技术指标计算、特征选择、数据处理
架构: 模块化设计，调用indicators.py和selection.py
支持数据结构：data/market/symbol/timeframe.parquet
"""

import pandas as pd
import numpy as np
import warnings
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import concurrent.futures
from dataclasses import dataclass
import argparse
import json
import gc

# 导入子模块
from .indicators import AdvancedIndicators, DataValidator, calculate_target_variable
from .selection import FeatureSelector, FeatureRiskAnalyzer

warnings.filterwarnings('ignore')

# 配置常量
CONFIG = {
    'RANDOM_SEED': 42,
    'SAMPLE_SIZE': 40000,  # 特征优化时的最大样本数
    'MIN_DATA_POINTS': 200,  # 股票最小数据点要求  
    'MIN_VOLUME': 1000000,  # 最小平均日成交量
    'MIN_PRICE': 5.0,  # 最小平均价格（避免penny stocks）
    'MISSING_RATIO_THRESHOLD': 0.10,  # 缺失值比例阈值
    'TARGET_WINDOW': 10,  # 预测窗口
    'TARGET_THRESHOLD': 0.05,  # 涨幅阈值
    'MAX_FEATURES': 30,  # 最大特征数
}

@dataclass
class FeatureConfig:
    """特征工程配置"""
    target_window: int = 10
    target_threshold: float = 0.05
    max_features: int = 30
    validation_splits: int = 5
    
@dataclass
class ProcessingResult:
    """处理结果"""
    symbol: str
    success: bool
    features_count: int = 0
    data_points: int = 0
    error_msg: str = None

class FeatureEngineer:
    """特征工程器 - 支持新数据结构"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.model_dir = Path("model")
        self.logs_dir = Path("logs")
        
        # 创建必要目录
        self.model_dir.mkdir(exist_ok=True)
        (self.model_dir / "data").mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
        self.config = FeatureConfig()
        
        # 初始化子模块
        self.feature_selector = FeatureSelector(random_state=CONFIG['RANDOM_SEED'])
        self.risk_analyzer = FeatureRiskAnalyzer()
    
    @staticmethod
    def clean_data(df: pd.DataFrame) -> pd.DataFrame:
        """数据清洗 - 调用DataValidator"""
        return DataValidator.clean_data(df)
        
    def load_stock_data(self, market: str, symbol: str, timeframe: str = "1d") -> Optional[pd.DataFrame]:
        """加载单只股票数据 - 支持新数据结构"""
        try:
            # 新数据结构: data/market/symbol/timeframe.parquet
            data_file = self.data_dir / market / symbol / f"{timeframe}.parquet"
            
            if not data_file.exists():
                return None
                
            df = pd.read_parquet(data_file)
            if df.empty:
                return None
                
            # 确保数据格式统一
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.sort_values('timestamp').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            logging.error(f"加载{market}/{symbol}数据失败: {e}")
            return None
    
    def get_available_stocks(self, market: str, timeframe: str = "1d", limit: int = None) -> List[str]:
        """获取可用股票列表 - 支持新数据结构"""
        stocks = []
        market_dir = self.data_dir / market
        
        if not market_dir.exists():
            return stocks
            
        for symbol_dir in market_dir.iterdir():
            if symbol_dir.is_dir():
                data_file = symbol_dir / f"{timeframe}.parquet"
                if data_file.exists():
                    stocks.append(symbol_dir.name)
                    if limit and len(stocks) >= limit:
                        break
                        
        return sorted(stocks)
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标 - 调用AdvancedIndicators"""
        return AdvancedIndicators.calculate_comprehensive_features(df)
    
    def calculate_target(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算目标变量 - 调用indicators模块"""
        return calculate_target_variable(df, CONFIG['TARGET_WINDOW'], CONFIG['TARGET_THRESHOLD'])
    
    def process_single_stock(self, market: str, symbol: str, timeframe: str = "1d") -> ProcessingResult:
        """处理单只股票"""
        try:
            # 加载数据
            df = self.load_stock_data(market, symbol, timeframe)
            if df is None or len(df) < CONFIG['MIN_DATA_POINTS']:
                return ProcessingResult(symbol=symbol, success=False, error_msg="数据点不足")
            
            # 基础质量过滤
            avg_volume = df['volume'].mean()
            avg_price = df['close'].mean()
            missing_ratio = df.isnull().sum().sum() / df.size
            
            if avg_volume < CONFIG['MIN_VOLUME']:
                return ProcessingResult(symbol=symbol, success=False, error_msg="成交量过低")
            if avg_price < CONFIG['MIN_PRICE']:
                return ProcessingResult(symbol=symbol, success=False, error_msg="价格过低") 
            if missing_ratio > CONFIG['MISSING_RATIO_THRESHOLD']:
                return ProcessingResult(symbol=symbol, success=False, error_msg="数据缺失过多")
            
            # 计算技术指标
            df = self.calculate_technical_indicators(df)
            
            # 计算目标变量
            df = self.calculate_target(df)
            
            # 移除包含NaN的行
            df = df.dropna()
            
            if len(df) < 50:  # 清理后仍需要足够数据
                return ProcessingResult(symbol=symbol, success=False, error_msg="清理后数据不足")
            
            # 获取特征列
            feature_columns = self.get_feature_columns(df)
            
            return ProcessingResult(
                symbol=symbol,
                success=True,
                features_count=len(feature_columns),
                data_points=len(df)
            )
            
        except Exception as e:
            return ProcessingResult(symbol=symbol, success=False, error_msg=str(e))
    
    def get_feature_columns(self, df: pd.DataFrame) -> List[str]:
        """获取特征列列表"""
        exclude_columns = ['timestamp', 'symbol', 'future_return', 'target', 
                          'open', 'high', 'low', 'close', 'volume']
        
        feature_columns = [col for col in df.columns if col not in exclude_columns]
        return feature_columns
    
    def batch_process_stocks(self, market: str, stock_list: List[str], 
                           timeframe: str = "1d", max_workers: int = 4) -> List[pd.DataFrame]:
        """批量处理股票"""
        all_data = []
        successful_count = 0
        
        print(f"批处理启动: {len(stock_list)}只股票")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_symbol = {
                executor.submit(self.load_and_process_stock, market, symbol, timeframe): symbol 
                for symbol in stock_list
            }
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    df = future.result()
                    if df is not None and not df.empty:
                        all_data.append(df)
                        successful_count += 1
                except Exception as e:
                    logging.error(f"处理{symbol}失败: {e}")
        
        print(f"批处理完成")
        print(f"  成功率: {successful_count/len(stock_list)*100:.1f}% ({successful_count}/{len(stock_list)})")
        
        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            print(f"  总记录: {len(combined_df):,} 行")
            return combined_df
        
        return pd.DataFrame()
    
    def load_and_process_stock(self, market: str, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """加载并处理单只股票数据"""
        try:
            df = self.load_stock_data(market, symbol, timeframe)
            if df is None or len(df) < 100:
                return None
                
            df = self.calculate_technical_indicators(df)
            df = self.calculate_target(df)
            df = df.dropna()
            
            if len(df) < 50:
                return None
                
            return df
            
        except Exception as e:
            return None
    
    def check_feature_leakage(self, df: pd.DataFrame) -> Dict[str, List[str]]:
        """检查特征泄漏 - 调用FeatureRiskAnalyzer"""
        feature_columns = self.get_feature_columns(df)
        risk_assessment = FeatureRiskAnalyzer.analyze_temporal_risk(feature_columns)
        
        # 打印风险摘要
        FeatureRiskAnalyzer.print_risk_summary(feature_columns)
        
        return {
            'safe_features': risk_assessment['safe'],
            'low_risk_features': risk_assessment['safe'] + risk_assessment['low_risk'],
            'medium_risk_features': risk_assessment['medium_risk'],
            'high_risk_features': risk_assessment['high_risk']
        }
    
    def optimize_features(self, df: pd.DataFrame, max_features: int = None) -> Tuple[List[str], float]:
        """特征优化 - 调用FeatureSelector"""
        if max_features is None:
            max_features = CONFIG['MAX_FEATURES']
            
        print(f"\n智能特征优化（时序方法）")
        
        # 获取安全特征
        feature_columns = self.get_feature_columns(df)
        safe_features = FeatureRiskAnalyzer.get_safe_features(feature_columns, risk_level='low')
        
        print(f"  原始特征: {len(feature_columns)} 个")
        print(f"  安全特征: {len(safe_features)} 个")
        
        if len(safe_features) == 0:
            print("  警告: 无安全特征，使用所有特征")
            safe_features = feature_columns
        
        # 准备数据
        df_clean = df.dropna(subset=safe_features + ['target'])
        if len(df_clean) < 100:
            print("  错误: 清理后数据不足，返回前20个特征")
            return safe_features[:max_features], 0.5
        
        X = df_clean[safe_features]
        y = df_clean['target']
        timestamps = df_clean.get('timestamp', pd.Series(range(len(df_clean))))
        
        # 使用FeatureSelector进行特征选择
        selector = FeatureSelector(random_state=CONFIG['RANDOM_SEED'])
        selected_features, validation_auc = selector.select_time_series_aware(
            X, y, timestamps, top_k=max_features, n_splits=5
        )
        
        print(f"  优选特征: {len(selected_features)} 个")
        print(f"  验证AUC: {validation_auc:.4f}")
        
        return selected_features, validation_auc
    
    def validate_selected_features(self, df: pd.DataFrame, features: List[str]) -> float:
        """验证选择的特征 - 调用FeatureSelector内部方法"""
        try:
            X = df[features]
            y = df['target']
            
            selector = FeatureSelector(random_state=CONFIG['RANDOM_SEED'])
            return selector._validate_features(X, y)
            
        except Exception as e:
            return 0.5
    
    def calculate_data_quality_score(self, df: pd.DataFrame) -> float:
        """计算数据质量评分"""
        if df.empty:
            return 0.0
        
        # 基础指标
        total_cells = df.shape[0] * df.shape[1]
        non_null_cells = total_cells - df.isnull().sum().sum()
        completeness_score = non_null_cells / total_cells
        
        # 数据一致性检查
        consistency_issues = 0
        
        # 检查OHLC逻辑
        if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            consistency_issues += (df['high'] < df['low']).sum()
            consistency_issues += (df['open'] < df['low']).sum()
            consistency_issues += (df['close'] < df['low']).sum()
        
        consistency_score = max(0, 1 - consistency_issues / len(df))
        
        # 综合评分
        quality_score = (completeness_score * 0.7 + consistency_score * 0.3)
        return quality_score
    
    def save_processed_data(self, df: pd.DataFrame, selected_features: List[str]):
        """保存处理后的数据"""
        try:
            # 准备数据
            required_columns = ['timestamp', 'symbol', 'close', 'target', 'future_return'] + selected_features
            available_columns = [col for col in required_columns if col in df.columns]
            
            final_df = df[available_columns].dropna()
            
            # 为了兼容旧模块，添加date列
            final_df = final_df.copy()
            final_df['date'] = final_df['timestamp']
            
            # 时间排序
            final_df = final_df.sort_values('timestamp')
            
            # 训练/测试分割 - 按时间分割
            split_date = final_df['timestamp'].quantile(0.8)
            train_df = final_df[final_df['timestamp'] <= split_date]
            test_df = final_df[final_df['timestamp'] > split_date]
            
            # 数据清洗
            train_df = self.clean_data(train_df)
            test_df = self.clean_data(test_df)
            
            # 保存数据
            train_file = self.model_dir / "data" / "train_data.parquet"
            test_file = self.model_dir / "data" / "test_data.parquet"
            
            train_df.to_parquet(train_file, index=False)
            test_df.to_parquet(test_file, index=False)
            
            print(f"  特征配置已保存: model/optimal_features.json")
            print(f"  训练数据已保存: {train_file} ({len(train_df):,} 条)")
            print(f"  测试数据已保存: {test_file} ({len(test_df):,} 条)")
            
            # 保存特征配置
            feature_config = {
                'selected_features': selected_features,
                'feature_count': len(selected_features),
                'target_window': self.config.target_window,
                'target_threshold': self.config.target_threshold,
                'timestamp': datetime.now().isoformat(),
                'train_samples': len(train_df),
                'test_samples': len(test_df)
            }
            
            with open(self.model_dir / "optimal_features.json", 'w', encoding='utf-8') as f:
                json.dump(feature_config, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logging.error(f"保存数据失败: {e}")
    
    def run_feature_engineering(self, market: str = "us", mode: str = "research", 
                              stock_mode: str = "sample", count: int = 10,
                              optimize_features: bool = True) -> Dict:
        """运行特征工程流程"""
        
        print(f"专业特征工程系统启动 [{mode.upper()}模式]")
        
        # 获取股票列表
        if stock_mode == "sample":
            stock_list = self.get_available_stocks(market, limit=count)
            print(f"股票范围: {len(stock_list)} 只")
        else:
            stock_list = self.get_available_stocks(market)
            print(f"股票范围: 全部 {len(stock_list)} 只")
        
        if not stock_list:
            print("未找到可用股票数据")
            return {'success': False, 'error': '无可用数据'}
        
        # 根据模式选择处理方式
        if mode == "research":
            print(f" 研究模式启动，处理 {len(stock_list)} 只股票")
            combined_df = self.batch_process_stocks(market, stock_list)
            
            if combined_df.empty:
                return {'success': False, 'error': '数据处理失败'}
            
            # 数据质量检查
            quality_score = self.calculate_data_quality_score(combined_df)
            print(f"  数据质量分数: {quality_score:.3f}")
            
            # 特征泄漏检查
            feature_risk_analysis = self.check_feature_leakage(combined_df)
            
            selected_features = []
            validation_auc = 0.0
            
            if optimize_features:
                selected_features, validation_auc = self.optimize_features(combined_df)
                print(f"特征优化: {len(self.get_feature_columns(combined_df))} -> {len(selected_features)} 个")
            else:
                selected_features = feature_risk_analysis['low_risk_features'][:self.config.max_features]
                print(f"使用低风险特征: {len(selected_features)} 个")
            
            # 保存数据
            self.save_processed_data(combined_df, selected_features)
            
            return {
                'success': True,
                'features_count': len(selected_features),
                'validation_auc': validation_auc,
                'data_quality_score': quality_score
            }
            
        else:  # production mode
            print("生产模式：使用已有特征配置")
            
            # 检查是否存在特征配置
            feature_config_file = self.model_dir / "optimal_features.json"
            if not feature_config_file.exists():
                return {'success': False, 'error': '未找到特征配置，请先运行研究模式'}
            
            with open(feature_config_file, 'r') as f:
                config = json.load(f)
                selected_features = config['selected_features']
            
            # 处理最新数据
            combined_df = self.batch_process_stocks(market, stock_list)
            
            if combined_df.empty:
                return {'success': False, 'error': '数据处理失败'}
            
            # 保存数据（只保存测试数据用于生产）
            required_columns = ['timestamp', 'symbol', 'close'] + selected_features
            available_columns = [col for col in required_columns if col in combined_df.columns]
            
            final_df = combined_df[available_columns].dropna()
            
            # 为了兼容旧模块，添加date列
            final_df = final_df.copy()
            final_df['date'] = final_df['timestamp']
            
            # 数据清洗
            final_df = self.clean_data(final_df)
            
            test_file = self.model_dir / "data" / "test_data.parquet"
            final_df.to_parquet(test_file, index=False)
            
            print(f"生产数据已保存: {test_file} ({len(final_df):,} 条)")
            
            return {
                'success': True,
                'features_count': len(selected_features),
                'production_samples': len(final_df)
            }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='特征工程系统')
    parser.add_argument('mode', choices=['research', 'production'], 
                       help='运行模式: research(研发) 或 production(生产)')
    parser.add_argument('--market', choices=['us', 'cn'], default='us',
                       help='市场选择')
    parser.add_argument('--stock-mode', choices=['sample', 'all'], default='sample',
                       help='股票模式: sample(采样) 或 all(全部)')
    parser.add_argument('--count', type=int, default=10,
                       help='采样股票数量')
    parser.add_argument('--optimize-features', action='store_true',
                       help='启用特征优化')
    
    args = parser.parse_args()
    
    # 创建特征工程器
    engineer = FeatureEngineer()
    
    # 运行特征工程
    result = engineer.run_feature_engineering(
        market=args.market,
        mode=args.mode,
        stock_mode=args.stock_mode,
        count=args.count,
        optimize_features=args.optimize_features
    )
    
    if result['success']:
        print(f"AUC: {result.get('validation_auc', 0):.4f}")
    else:
        print(f"特征工程失败: {result.get('error', '未知错误')}")

if __name__ == "__main__":
    main()