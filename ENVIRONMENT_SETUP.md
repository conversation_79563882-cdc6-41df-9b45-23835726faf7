# Quant-Lab Environment Setup Guide

## 环境管理说明

项目依赖已添加到现有的 **quant** 环境中，使用 **mamba/conda** 环境管理。

### 转换完成状态

- ✅ 原pyproject.toml依赖已完全迁移
- ✅ 36个依赖包全部安装成功  
- ✅ quant环境包总数: 310个
- ✅ 核心包版本: PyTorch 2.7.1, Pandas 2.3.2, NumPy 2.2.6
- ✅ 已导出实际安装历史到environment.yml

### 快速开始

#### 1. 激活环境
```bash
mamba activate quant
```

#### 2. 验证安装
```bash
python -c "import pandas as pd, numpy as np, torch, sklearn, akshare; print('✅ 环境就绪')"
```

#### 3. 运行项目
```bash
python your_script.py
```

### 环境管理

#### 查看环境信息
```bash
mamba env list
mamba list | grep -E "(torch|pandas|numpy)"
```

#### 添加新包
```bash
# 优先使用conda-forge
mamba install package_name

# PyPI包使用pip
pip install package_name
```

#### 更新environment.yml

如需添加依赖，编辑 `environment.yml` 文件，然后：

```bash
mamba env update -f environment.yml --prune
```

#### 重新导出环境配置

```bash
# 导出基于安装历史的精确配置
mamba env export --from-history > environment.yml

# 导出包含所有依赖的完整配置  
mamba env export > environment-full.yml
```

### 核心依赖包

#### Conda packages (优化版本)
- numpy, pandas, scikit-learn
- pytorch (CPU版本)
- matplotlib, seaborn
- jupyter, jupyterlab
- xgboost, optuna

#### PyPI packages (特有包)
- akshare, tushare (中国金融数据)
- vectorbt (量化回测)
- ccxt, binance (交易所API)
- transformers (AI模型)

### 故障排除

#### 环境冲突
```bash
mamba clean --all
mamba env remove -n quant-lab
mamba env create -f environment.yml
```

#### 包版本问题
```bash
mamba search package_name
mamba install package_name=version
```

### 备份文件
- `uv_backup/pyproject.toml` - 原uv配置
- `uv_backup/uv.lock` - 原uv锁定版本

### 性能优化
- 使用libmamba求解器 (已启用)
- conda命令别名到mamba (已配置)
- 优化包通道顺序 (conda-forge优先)