#!/usr/bin/env python3
"""
模型验证模块
专业Walk-Forward分析系统 - 多维度模型验证
目标: 寻找能达到最优收益的最优模型配置
功能: 滚动时间窗口分析，超参数+特征组合同步优化
方法: 贝叶斯优化 + 时间序列交叉验证 + 样本外验证
"""

import pandas as pd
import numpy as np
import warnings
from pathlib import Path
from datetime import datetime, timedelta
import json
from typing import Dict, List, Tuple, Optional, Any
import joblib
from dataclasses import dataclass
from scipy import stats
import random
import sys
import argparse

# 机器学习相关
import xgboost as xgb
import optuna
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import roc_auc_score, classification_report
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier

# 专注XGBoost - 移除LightGBM依赖
HAS_LIGHTGBM = False

warnings.filterwarnings('ignore')

# 设置全局随机种子确保结果可重复
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)
random.seed(RANDOM_SEED)

@dataclass
class WalkForwardConfig:
    """专业Walk-Forward分析配置"""
    in_sample_start: str = "2015-01-01"
    in_sample_end: str = "2023-12-31"
    out_of_sample_start: str = "2024-01-01"
    out_of_sample_end: str = datetime.now().strftime("%Y-%m-%d")
    train_window_months: int = 24
    test_window_months: int = 6
    step_months: int = 3
    min_train_samples: int = 1000
    min_test_samples: int = 100
    n_trials_per_fold: int = 15  # diff优化：轻度超参数优化次数
    cv_folds: int = 5
    target_return_threshold: float = 1.47  # 147%收益目标 
    max_features: int = 25  # diff优化：与现有23个特征配置匹配
    ensemble_models: bool = True  # 是否使用模型集成
    fast_mode: bool = True  # diff优化：快速模式，减少计算复杂度

class ModelEnsemble:
    """模型集成器 - 多算法组合以提高预测精度"""
    
    def __init__(self):
        self.models = {
            'xgboost': None,
            'random_forest': None,
            'gradient_boosting': None
        }
        # 专注XGBoost，移除LightGBM
        self.weights = None
        
    def get_model_params(self, model_type: str, trial: optuna.Trial) -> Dict:
        """为不同模型类型生成超参数"""
        if model_type == 'xgboost':
            return {
                'n_estimators': trial.suggest_int('n_estimators', 50, 250),  # 基于diff优化：更保守的范围
                'max_depth': trial.suggest_int('max_depth', 4, 8),  # 基于diff优化：防止过拟合
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'subsample': trial.suggest_float('subsample', 0.7, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.7, 1.0),
                'gamma': trial.suggest_float('gamma', 0, 5),
                'lambda': trial.suggest_float('lambda', 0, 5),
                'alpha': trial.suggest_float('alpha', 0, 5),
                'objective': 'binary:logistic',
                'eval_metric': 'auc',
                'random_state': RANDOM_SEED
            }
        elif model_type == 'lightgbm':
            return {
                'n_estimators': trial.suggest_int('n_estimators', 100, 300),
                'max_depth': trial.suggest_int('max_depth', 4, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'subsample': trial.suggest_float('subsample', 0.7, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.7, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 0, 5),
                'reg_lambda': trial.suggest_float('reg_lambda', 0, 5),
                'objective': 'binary',
                'metric': 'auc',
                'random_state': RANDOM_SEED,
                'verbose': -1
            }
        elif model_type == 'random_forest':
            return {
                'n_estimators': trial.suggest_int('n_estimators', 100, 300),
                'max_depth': trial.suggest_int('max_depth', 5, 15),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 10),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 5),
                'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', None]),
                'random_state': RANDOM_SEED
            }
        elif model_type == 'gradient_boosting':
            return {
                'n_estimators': trial.suggest_int('n_estimators', 100, 300),
                'max_depth': trial.suggest_int('max_depth', 4, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'subsample': trial.suggest_float('subsample', 0.7, 1.0),
                'random_state': RANDOM_SEED
            }
        
    def create_model(self, model_type: str, params: Dict):
        """创建指定类型的模型"""
        if model_type == 'xgboost':
            return xgb.XGBClassifier(**params)
        elif model_type == 'lightgbm' and HAS_LIGHTGBM:
            return lgb.LGBMClassifier(**params)
        elif model_type == 'random_forest':
            return RandomForestClassifier(**params)
        elif model_type == 'gradient_boosting':
            return GradientBoostingClassifier(**params)
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
    def optimize_ensemble(self, X: np.ndarray, y: np.ndarray, cv_folds: int = 5) -> Dict:
        """优化模型集成权重"""
        print("优化模型集成...")
        
        # 使用时间序列交叉验证
        tscv = TimeSeriesSplit(n_splits=cv_folds)
        model_predictions = {model_type: [] for model_type in self.models.keys()}
        true_labels = []
        
        for train_idx, val_idx in tscv.split(X):
            X_train, X_val = X[train_idx], X[val_idx]
            y_train, y_val = y[train_idx], y[val_idx]
            
            if len(np.unique(y_train)) < 2:
                continue
                
            fold_preds = {}
            for model_type in self.models.keys():
                try:
                    # 快速参数优化
                    study = optuna.create_study(direction='maximize', 
                                              sampler=optuna.samplers.TPESampler(seed=RANDOM_SEED))
                    
                    def objective(trial):
                        params = self.get_model_params(model_type, trial)
                        model = self.create_model(model_type, params)
                        model.fit(X_train, y_train)
                        pred_proba = model.predict_proba(X_val)[:, 1]
                        return roc_auc_score(y_val, pred_proba)
                    
                    study.optimize(objective, n_trials=10)  # 快速收敛，减少计算量
                    
                    # 使用最佳参数训练模型
                    best_params = self.get_model_params(model_type, study.best_trial)
                    model = self.create_model(model_type, best_params)
                    model.fit(X_train, y_train)
                    fold_preds[model_type] = model.predict_proba(X_val)[:, 1]
                    
                except Exception as e:
                    print(f"警告: {model_type} 优化失败: {e}")
                    fold_preds[model_type] = np.random.random(len(y_val)) * 0.1 + 0.45
            
            # 收集预测结果
            for model_type, preds in fold_preds.items():
                model_predictions[model_type].extend(preds)
            true_labels.extend(y_val)
        
        # 优化集成权重
        if len(true_labels) > 0:
            weights = self._optimize_weights(model_predictions, true_labels)
            ensemble_score = self._evaluate_ensemble(model_predictions, true_labels, weights)
            
            print(f"集成优化完成，AUC: {ensemble_score:.4f}")
            return {
                'weights': weights,
                'ensemble_auc': ensemble_score,
                'individual_aucs': {
                    model_type: roc_auc_score(true_labels, preds) 
                    for model_type, preds in model_predictions.items()
                }
            }
        
        return {'weights': {model: 0.25 for model in self.models.keys()}, 'ensemble_auc': 0.0}
    
    def _optimize_weights(self, predictions: Dict, true_labels: List) -> Dict:
        """使用网格搜索优化集成权重"""
        from itertools import product
        
        best_score = 0
        best_weights = {model: 0.25 for model in self.models.keys()}
        
        # 简单网格搜索
        weight_options = [0.1, 0.2, 0.25, 0.3, 0.35]
        
        for weights in product(weight_options, repeat=len(self.models)):
            if abs(sum(weights) - 1.0) > 0.01:  # 权重和必须为1
                continue
                
            weight_dict = dict(zip(self.models.keys(), weights))
            score = self._evaluate_ensemble(predictions, true_labels, weight_dict)
            
            if score > best_score:
                best_score = score
                best_weights = weight_dict
        
        return best_weights
    
    def _evaluate_ensemble(self, predictions: Dict, true_labels: List, weights: Dict) -> float:
        """评估集成模型性能"""
        ensemble_pred = np.zeros(len(true_labels))
        
        for model_type, weight in weights.items():
            if model_type in predictions and len(predictions[model_type]) == len(true_labels):
                ensemble_pred += weight * np.array(predictions[model_type])
        
        try:
            return roc_auc_score(true_labels, ensemble_pred)
        except:
            return 0.0

class WalkForwardAnalyzer:
    """专业Walk-Forward分析器 - 多维度优化寻找147%收益"""
    
    def __init__(self, config: WalkForwardConfig = None):
        self.config = config or WalkForwardConfig()
        self.results_dir = Path("logs/walk_forward_results")
        # 简化文件结构：参数直接保存到model目录
        self.results_dir.mkdir(parents=True, exist_ok=True)
        self.model_ensemble = ModelEnsemble()
        
    def load_data(self) -> pd.DataFrame:
        """加载训练数据"""
        data_file = Path("model/data/train_data.parquet")
        if not data_file.exists():
            print("错误: 训练数据不存在，请先运行: python -m feature.engineering")
            return pd.DataFrame()
        
        print("加载训练数据...")
        data = pd.read_parquet(data_file)
        data['date'] = pd.to_datetime(data['date'])
        # 移除时区信息，以避免与 naive-datetime 比较时出错
        if data['date'].dt.tz is not None:
            data['date'] = data['date'].dt.tz_localize(None)
        
        print(f"✓ 数据加载完成: {len(data):,} 行, {data['symbol'].nunique()} 只股票")
        print(f"   时间范围: {data['date'].min().strftime('%Y-%m-%d')} ~ {data['date'].max().strftime('%Y-%m-%d')}")
        print(f"   正样本比例: {data['target'].mean():.2%}")
        
        return data.sort_values(['date', 'symbol']).reset_index(drop=True)
    
    def generate_walk_forward_windows(self, data: pd.DataFrame) -> List[Dict]:
        """生成Walk-Forward分析窗口"""
        print("\n> 生成Walk-Forward分析窗口...")
        in_sample_data = data[(data['date'] >= self.config.in_sample_start) & (data['date'] <= self.config.in_sample_end)].copy()
        
        windows = []
        current_start = pd.to_datetime(self.config.in_sample_start)
        end_date = pd.to_datetime(self.config.in_sample_end)
        
        while True:
            train_end = current_start + pd.DateOffset(months=self.config.train_window_months)
            test_start = train_end + pd.DateOffset(days=1)
            test_end = test_start + pd.DateOffset(months=self.config.test_window_months)
            
            if test_end > end_date: break
            
            train_data = in_sample_data[(in_sample_data['date'] >= current_start) & (in_sample_data['date'] <= train_end)]
            test_data = in_sample_data[(in_sample_data['date'] >= test_start) & (in_sample_data['date'] <= test_end)]
            
            if len(train_data) >= self.config.min_train_samples and len(test_data) >= self.config.min_test_samples:
                windows.append({'id': len(windows) + 1, 'train': train_data, 'test': test_data})
            
            current_start += pd.DateOffset(months=self.config.step_months)
        
        print(f"✓ 生成 {len(windows)} 个有效的Walk-Forward窗口")
        return windows
    
    def prepare_features(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """准备特征数据（使用最优特征配置）"""
        optimal_features = self._load_optimal_features()
        available_features = [f for f in optimal_features if f in data.columns]
        
        X = data[available_features].values
        y = data['target'].values
        
        if np.any(np.isnan(X)) or np.any(np.isinf(X)):
            X = np.nan_to_num(X) # 简单处理
            
        return X, y, available_features

    def _load_optimal_features(self) -> List[str]:
        """加载最优特征配置"""
        optimal_features_file = Path("model/optimal_features.json")
        if optimal_features_file.exists():
            try:
                with open(optimal_features_file, 'r', encoding='utf-8') as f:
                    return json.load(f)['selected_features']
            except Exception as e:
                print(f"! 加载最优特征配置失败: {e}")
        
        print("! 未找到配置文件，使用硬编码的32个最优特征")
        return [
            'ohlc_avg', 'hl_avg', 'price_change', 'volume_change', 'macd', 'macd_signal', 'macd_histogram', 'rsi',
            'sma_20', 'ema_12', 'ema_26', 'bb_upper', 'bb_middle', 'bb_lower', 'atr', 'volume_sma', 'price_momentum',
            'volatility', 'price_to_sma55_pct', 'price_to_sma233_pct', 'proximity_to_sma55', 'proximity_to_sma233',
            'sma55_position', 'sma233_position', 'sma55_support_strength', 'sma233_support_strength',
            'trading_intensity', 'volume_price_trend', 'bb_position', 'price_range', 'price_range_pct', 'price_position'
        ]

    def optimize_hyperparameters(self, X_train: np.ndarray, y_train: np.ndarray) -> Dict:
        """优化超参数"""
        def objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 50, 250),  # 优化范围
                'max_depth': trial.suggest_int('max_depth', 4, 8),  # 防止过拟合
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'subsample': trial.suggest_float('subsample', 0.7, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.7, 1.0),
                'gamma': trial.suggest_float('gamma', 0, 5),
                'lambda': trial.suggest_float('lambda', 0, 5),
                'alpha': trial.suggest_float('alpha', 0, 5),
                'objective': 'binary:logistic', 'eval_metric': 'auc', 'random_state': RANDOM_SEED
            }
            model = xgb.XGBClassifier(**params)
            model.fit(X_train, y_train, verbose=False)
            preds = model.predict_proba(X_train)[:, 1]
            return roc_auc_score(y_train, preds)

        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=RANDOM_SEED))
        # diff优化：基于快速模式调整试验次数
        n_trials = 10 if self.config.fast_mode else min(self.config.n_trials_per_fold, 15)
        study.optimize(objective, n_trials=n_trials)
        return study.best_params

    def evaluate_single_window(self, window: Dict) -> Dict:
        """评估单个窗口"""
        X_train, y_train, features = self.prepare_features(window['train'])
        X_test, y_test, _ = self.prepare_features(window['test'])
        
        if len(np.unique(y_train)) < 2 or len(np.unique(y_test)) < 2: return None

        best_params = self.optimize_hyperparameters(X_train, y_train)
        model = xgb.XGBClassifier(**best_params, random_state=RANDOM_SEED)
        model.fit(X_train, y_train)
        
        train_auc = roc_auc_score(y_train, model.predict_proba(X_train)[:, 1])
        test_auc = roc_auc_score(y_test, model.predict_proba(X_test)[:, 1])
        
        print(f"  窗口 {window['id']}: 训练AUC: {train_auc:.4f}, 测试AUC: {test_auc:.4f}")
        
        return {'window_id': window['id'], 'train_auc': train_auc, 'test_auc': test_auc, 'best_params': best_params}

    def run_walk_forward_analysis(self) -> Dict:
        """运行完整的Walk-Forward分析"""
        print("> === 完整Walk-Forward分析 ===")
        print("> 模式特点: 滚动窗口 + 每窗口轻度超参数优化 + 模型稳健性评估")
        
        data = self.load_data()
        if data.empty: return {}
        
        windows = self.generate_walk_forward_windows(data)
        if not windows: return {}
        
        print(f"\n> 开始评估 {len(windows)} 个滚动窗口...")
        window_results = []
        
        for i, window in enumerate(windows, 1):
            print(f"\n> 处理窗口 {i}/{len(windows)}...")
            result = self.evaluate_single_window(window)
            if result is not None:
                window_results.append(result)
        
        if not window_results:
            print("✗ 所有窗口评估失败")
            return {}
            
        self._summarize_and_save(window_results)
        return {'status': 'success', 'results': window_results}

    def _summarize_and_save(self, window_results: List[Dict]):
        """汇总结果并保存"""
        test_aucs = [r['test_auc'] for r in window_results]
        train_aucs = [r['train_auc'] for r in window_results]
        best_window = max(window_results, key=lambda x: x['test_auc'])
        
        # 模型稳健性分析
        auc_mean = np.mean(test_aucs)
        auc_std = np.std(test_aucs)
        auc_min = np.min(test_aucs)
        auc_max = np.max(test_aucs)
        overfitting_scores = [train - test for train, test in zip(train_aucs, test_aucs)]
        avg_overfitting = np.mean(overfitting_scores)
        
        # 稳健性评分 (0-1)
        stability_score = 1 - (auc_std / auc_mean) if auc_mean > 0 else 0
        robustness_score = max(0, 1 - avg_overfitting) if avg_overfitting >= 0 else 1
        
        summary = {
            'walk_forward_analysis': {
                'best_params': best_window['best_params'],
                'best_window_auc': best_window['test_auc'],
                'best_window_id': best_window['window_id'],
                'average_test_auc': auc_mean,
                'std_test_auc': auc_std,
                'min_test_auc': auc_min,
                'max_test_auc': auc_max,
                'method': 'full_walk_forward',
                'validation_approach': 'rolling_windows',
                'optimization_strategy': 'per_window_hyperopt',
                'robustness_analysis': {
                    'stability_score': stability_score,
                    'robustness_score': robustness_score,
                    'avg_overfitting': avg_overfitting,
                    'auc_consistency': auc_std / auc_mean if auc_mean > 0 else float('inf')
                }
            },
            'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
            'window_details': window_results
        }
        
        params_file = Path("model/optimal_params.json")
        with open(params_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
            
        print(f"\nWalk-Forward Analysis 完成")
        print(f"平均测试AUC: {auc_mean:.4f} ± {auc_std:.4f}")
        print(f"AUC范围: {auc_min:.4f} ~ {auc_max:.4f}")
        print(f"稳定性评分: {stability_score:.3f}")
        print(f"稳健性评分: {robustness_score:.3f}")
        print(f"平均过拟合: {avg_overfitting:.4f}")
        print(f"最佳参数已保存至: {params_file}")

def _load_optimal_features() -> List[str]:
    """加载最优特征配置"""
    optimal_features_file = Path("model/optimal_features.json")
    if optimal_features_file.exists():
        try:
            with open(optimal_features_file, 'r', encoding='utf-8') as f:
                return json.load(f)['selected_features']
        except Exception as e:
            print(f"! 加载最优特征配置失败: {e}")
    
    print("! 未找到配置文件，使用硬编码的32个最优特征")
    return [
        'ohlc_avg', 'hl_avg', 'price_change', 'volume_change', 'macd', 'macd_signal', 'macd_histogram', 'rsi',
        'sma_20', 'ema_12', 'ema_26', 'bb_upper', 'bb_middle', 'bb_lower', 'atr', 'volume_sma', 'price_momentum',
        'volatility', 'price_to_sma55_pct', 'price_to_sma233_pct', 'proximity_to_sma55', 'proximity_to_sma233',
        'sma55_position', 'sma233_position', 'sma55_support_strength', 'sma233_support_strength',
        'trading_intensity', 'volume_price_trend', 'bb_position', 'price_range', 'price_range_pct', 'price_position'
    ]

def run_simple_walk_forward():
    """轻量级Walk-Forward分析 - 快速验证模型性能"""
    print("> === 轻量级Walk-Forward分析 ===")
    print("> 模式特点: 单窗口时间分割 + 轻量级超参数优化 (15次试验)")
    print("> 数据分割: 最近24个月训练 + 最近6个月测试")
    
    # 加载数据
    data_file = Path("model/data/train_data.parquet")
    if not data_file.exists():
        print("✗ 训练数据不存在，请先运行特征工程")
        return
    
    data = pd.read_parquet(data_file)
    data['date'] = pd.to_datetime(data['date'])
    
    # 处理时区问题
    if data['date'].dt.tz is not None:
        data['date'] = data['date'].dt.tz_localize(None)
    
    print(f"> 数据概览: {len(data):,} 行, {data['symbol'].nunique()} 只股票")
    print(f"   时间范围: {data['date'].min().strftime('%Y-%m-%d')} ~ {data['date'].max().strftime('%Y-%m-%d')}")
    print(f"   正样本比例: {data['target'].mean():.2%}")
    
    # 使用最优特征配置
    optimal_features = _load_optimal_features()
    available_features = [f for f in optimal_features if f in data.columns]
    
    X = data[available_features].fillna(0)
    y = data['target']
    
    print(f"使用最优特征: {len(available_features)}/{len(optimal_features)}")
    
    # 时间分割 - 遵循金融建模原则：使用最后一个时间窗口进行验证
    # 模拟Walk-Forward最后一个窗口：训练24个月，测试6个月
    data_end_date = data['date'].max()
    test_start_date = data_end_date - pd.DateOffset(months=6)
    train_start_date = test_start_date - pd.DateOffset(months=24)
    
    train_mask = (data['date'] >= train_start_date) & (data['date'] < test_start_date)
    test_mask = data['date'] >= test_start_date
    
    X_train, X_test = X[train_mask], X[test_mask]
    y_train, y_test = y[train_mask], y[test_mask]
    
    print(f"训练集: {len(X_train):,} 样本 (训练期24个月，正样本: {y_train.mean():.2%})")
    print(f"测试集: {len(X_test):,} 样本 (测试期6个月，正样本: {y_test.mean():.2%})")
    print(f"训练期间: {train_start_date.strftime('%Y-%m-%d')} ~ {test_start_date.strftime('%Y-%m-%d')}")
    print(f"测试期间: {test_start_date.strftime('%Y-%m-%d')} ~ {data_end_date.strftime('%Y-%m-%d')}")
    
    # 轻量级超参数优化 - 减少参数空间
    print("\n> 开始轻量级超参数优化...")
    
    def objective(trial):
        # 简化参数范围，减少计算量
        params = {
            'n_estimators': trial.suggest_int('n_estimators', 50, 200),  # diff优化：快速收敛范围
            'max_depth': trial.suggest_int('max_depth', 4, 8),  # diff优化：防止过拟合
            'learning_rate': trial.suggest_float('learning_rate', 0.05, 0.2),  # diff优化：稳定范围
            'subsample': trial.suggest_float('subsample', 0.8, 1.0),  # diff优化：高质量采样
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.8, 1.0),  # diff优化：特征采样
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'random_state': RANDOM_SEED,
            'verbose': False
        }
        
        model = xgb.XGBClassifier(**params)
        model.fit(X_train, y_train)
        
        # 使用训练集的一部分进行验证
        split_idx = int(len(X_train) * 0.8)
        X_val = X_train[split_idx:]
        y_val = y_train[split_idx:]
        
        y_pred_proba = model.predict_proba(X_val)[:, 1]
        return roc_auc_score(y_val, y_pred_proba)
    
    study = optuna.create_study(direction='maximize', 
                               sampler=optuna.samplers.TPESampler(seed=RANDOM_SEED))
    study.optimize(objective, n_trials=15)  # 轻量级优化
    
    print(f"✓ 超参数优化完成，最佳AUC: {study.best_value:.4f}")
    
    # 使用最佳参数训练最终模型
    print("\n> 训练最终模型...")
    best_params = study.best_params
    best_params.update({
        'objective': 'binary:logistic',
        'eval_metric': 'auc',
        'random_state': RANDOM_SEED,
        'verbose': False
    })
    
    final_model = xgb.XGBClassifier(**best_params)
    final_model.fit(X_train, y_train)
    
    # 评估模型
    train_auc = roc_auc_score(y_train, final_model.predict_proba(X_train)[:, 1])
    test_auc = roc_auc_score(y_test, final_model.predict_proba(X_test)[:, 1])
    
    print(f"\n> 模型性能评估:")
    print(f"   训练集AUC: {train_auc:.4f}")
    print(f"   测试集AUC: {test_auc:.4f}")
    print(f"   过拟合程度: {train_auc - test_auc:.4f}")
    
    # 保存最佳参数
    summary = {
        'walk_forward_analysis': {
            'best_params': best_params,
            'best_window_auc': test_auc,
            'best_window_id': 1,
            'average_test_auc': test_auc,
            'std_test_auc': 0.0,
            'method': 'simple_validation',
            'validation_approach': 'single_window_time_split',
            'optimization_strategy': 'lightweight_hyperopt'
        },
        'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
        'performance_summary': {
            'train_auc': train_auc,
            'test_auc': test_auc,
            'feature_count': len(available_features),
            'train_samples': len(X_train),
            'test_samples': len(X_test),
            'train_period': 'last_24_months',
            'test_period': 'last_6_months'
        }
    }
    
    # 简化文件结构：参数直接保存到model目录
    params_file = Path("model/optimal_params.json")
    
    with open(params_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    print(f"\n最佳参数已保存: {params_file}")
    print(f"轻量级Walk-Forward分析完成，测试AUC: {test_auc:.4f}")
    
    return summary

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Walk-Forward分析系统')
    parser.add_argument('--mode', choices=['simple', 'full'], default='simple',
                       help='运行模式: simple(轻量级) 或 full(完整Walk-Forward)')
    
    args = parser.parse_args()
    
    if args.mode == 'simple':
        run_simple_walk_forward()
    else:
        print("启动完整Walk-Forward Analysis...")
        analyzer = WalkForwardAnalyzer()
        analyzer.run_walk_forward_analysis()

if __name__ == "__main__":
    main()