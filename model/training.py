#!/usr/bin/env python3
"""
模型训练模块
基于validation.py优化参数的模型训练、回测、评估功能
"""

import pandas as pd
import numpy as np
import warnings
from pathlib import Path
from datetime import datetime
import json
from typing import Dict, List, Tuple
import joblib
import random
import sys

# 机器学习相关
import xgboost as xgb
from sklearn.metrics import roc_auc_score
from dataclasses import dataclass

warnings.filterwarnings('ignore')

# 设置全局随机种子确保结果可重复
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)
random.seed(RANDOM_SEED)

@dataclass
class Position:
    symbol: str
    shares: int
    buy_price: float
    buy_date: datetime
    current_price: float = 0.0
    
    @property
    def market_value(self) -> float: return self.shares * self.current_price
    @property
    def unrealized_return(self) -> float: return (self.current_price - self.buy_price) / self.buy_price

class ModelTrainer:
    """模型训练器"""
    def __init__(self):
        self.model = None
        self.xgb_params = self._load_optimized_params()
    
    def _load_optimized_params(self) -> Dict:
        """加载由model.validation模块生成的优化参数"""
        optimized_params_file = Path("model/optimal_params.json")
        if not optimized_params_file.exists():
            print("未找到优化参数文件，请先运行 python -m model.validation")
            print("回退到使用高性能默认参数")
            return {
                'objective': 'binary:logistic', 
                'eval_metric': 'auc', 
                'random_state': RANDOM_SEED,
                'n_estimators': 200, 'max_depth': 6, 'learning_rate': 0.05,
                'subsample': 0.8, 'colsample_bytree': 0.8, 'gamma': 1, 'lambda': 1, 'alpha': 0
            }

        try:
            with open(optimized_params_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            best_params = data['walk_forward_analysis']['best_params']
            best_auc = data['walk_forward_analysis']['best_window_auc']
            
            # 添加必要的固定参数
            best_params['objective'] = 'binary:logistic'
            best_params['eval_metric'] = 'auc'
            best_params['random_state'] = RANDOM_SEED

            print(f"成功加载 Walk-Forward 优化参数 (最佳窗口AUC: {best_auc:.4f})")
            return best_params
        except (KeyError, json.JSONDecodeError) as e:
            print(f"解析优化参数文件失败: {e}")
            print("回退到使用高性能默认参数")
            return {
                'objective': 'binary:logistic', 
                'eval_metric': 'auc', 
                'random_state': RANDOM_SEED,
                'n_estimators': 200, 'max_depth': 6, 'learning_rate': 0.05,
                'subsample': 0.8, 'colsample_bytree': 0.8, 'gamma': 1, 'lambda': 1, 'alpha': 0
            }

    def _validate_params(self, params: Dict) -> bool:
        """验证参数是否在合理范围内，避免过拟合"""
        max_depth = params.get('max_depth', 6)
        learning_rate = params.get('learning_rate', 0.1)
        n_estimators = params.get('n_estimators', 100)
        
        if max_depth > 8 or learning_rate < 0.01 or n_estimators > 300:
            return False
        return True

    def train_model(self, X_train: np.ndarray, y_train: np.ndarray):
        """训练XGBoost模型"""
        print("开始训练XGBoost模型...")
        self.model = xgb.XGBClassifier(**self.xgb_params)
        self.model.fit(X_train, y_train, verbose=False)
        print("模型训练完成。")
    
    def calculate_target_variable(self, data: pd.DataFrame, threshold: float = 0.05) -> pd.Series:
        """diff优化：计算目标变量，基于未来收益率"""
        # 按股票分组计算未来收益率
        target = []
        for symbol in data['symbol'].unique():
            symbol_data = data[data['symbol'] == symbol].copy()
            symbol_data = symbol_data.sort_values('date')
            
            # 计算未来5日收益率
            symbol_data['future_return'] = symbol_data['close'].shift(-5) / symbol_data['close'] - 1
            # 目标：未来收益率 > threshold
            symbol_data['target'] = (symbol_data['future_return'] > threshold).astype(int)
            
            target.extend(symbol_data['target'].tolist())
        
        return pd.Series(target, index=data.index)

    def save_model(self, filename: str = "xgboost_model.pkl"):
        """保存模型"""
        models_dir = Path("model/saved_models")
        models_dir.mkdir(exist_ok=True, parents=True)
        model_path = models_dir / filename
        joblib.dump(self.model, model_path)
        print(f"模型已保存到: {model_path}")

class BacktestEngine:
    """量化交易策略回测引擎"""
    def __init__(self, initial_capital: float = 100000.0):
        self.initial_capital = initial_capital
        self.cash = initial_capital
        self.positions = {}
        self.trade_records = []
        self.daily_portfolio_value = []
        
        # 交易成本参数 (平衡现实性与盈利能力)
        self.commission_rate = 0.0005
        self.slippage_rate = 0.0005
        
        # 策略参数
        self.probability_threshold = 0.45   # 维持最优的买入阈值
        self.top_k_stocks = 10             # 维持分散度优势
        self.take_profit_threshold = 0.15   # 更现实的止盈目标
        self.stop_loss_threshold = 0.05     # 加强止损控制
        self.max_holding_days = 15          # 略缩短周期
        
    def run_backtest(self, model, test_data: pd.DataFrame, feature_columns: List[str]) -> Dict:
        """运行回测"""
        print("开始回测...")
        test_dates = sorted(test_data['date'].unique())
        
        for current_date in test_dates:
            current_data = test_data[test_data['date'] == current_date].set_index('symbol')
            self._update_positions_price(current_data)
            self._execute_sell_logic(current_data, current_date)
            self._execute_buy_logic(model, current_data, feature_columns, current_date)
            
            portfolio_value = self.cash + sum(pos.market_value for pos in self.positions.values())
            self.daily_portfolio_value.append(portfolio_value)
        
        return self._calculate_results()
    
    def _update_positions_price(self, current_data: pd.DataFrame):
        for symbol, position in self.positions.items():
            if symbol in current_data.index:
                position.current_price = current_data.loc[symbol, 'close']
    
    def _execute_sell_logic(self, current_data: pd.DataFrame, current_date: datetime):
        symbols_to_sell = []
        for symbol, pos in self.positions.items():
            if symbol not in current_data.index: continue
            
            return_rate = (pos.current_price - pos.buy_price) / pos.buy_price
            days_held = (current_date - pos.buy_date).days
            
            sell_reason = None
            if return_rate >= self.take_profit_threshold: sell_reason = "TAKE_PROFIT"
            elif return_rate <= -self.stop_loss_threshold: sell_reason = "STOP_LOSS"
            elif days_held >= self.max_holding_days: sell_reason = "TIME_LIMIT"
            
            if sell_reason:
                # diff优化：更详细的交易成本计算
                gross_sell_value = pos.shares * pos.current_price
                commission = gross_sell_value * self.commission_rate
                slippage = gross_sell_value * self.slippage_rate
                sell_value = gross_sell_value - commission - slippage
                
                self.cash += sell_value
                
                # diff优化：更详细的交易记录
                self.trade_records.append({
                    'date': current_date, 
                    'symbol': symbol, 
                    'action': 'SELL', 
                    'shares': pos.shares,
                    'price': pos.current_price,
                    'value': sell_value,
                    'reason': sell_reason, 
                    'return': return_rate,
                    'days_held': days_held
                })
                
                # diff优化：添加卖出信息输出
                print(f"卖出 {symbol}: {pos.shares}股 @ {pos.current_price:.2f}, 原因: {sell_reason}, 收益率: {return_rate:.2%}")
                symbols_to_sell.append(symbol)
        
        for symbol in symbols_to_sell:
            del self.positions[symbol]
    
    def _execute_buy_logic(self, model, current_data: pd.DataFrame, feature_columns: List[str], current_date: datetime):
        X = current_data[feature_columns].values
        probabilities = model.predict_proba(X)[:, 1]
        
        buy_candidates = []
        prob_filter_count = 0
        risk_filter_count = 0
        total_candidates = len(current_data.index)
        
        for i, symbol in enumerate(current_data.index):
            prob = probabilities[i]  # 正类概率
            if prob > self.probability_threshold and symbol not in self.positions:
                prob_filter_count += 1
                # 计算风险评分（基于波动性和技术指标）
                volatility = current_data.loc[symbol, 'volatility'] if 'volatility' in current_data.columns else 0.02
                rsi = current_data.loc[symbol, 'rsi'] if 'rsi' in current_data.columns else 50
                
                # 避免买入过度超买或高波动的股票
                if rsi < 85 and volatility < 5.0:  # 修正波动性条件（数据是百分比形式）
                    risk_filter_count += 1
                    # 计算调整后的分数（概率 - 风险惩罚）
                    risk_adjusted_score = prob - (volatility * 0.05) - (max(0, rsi - 75) * 0.0005)
                    buy_candidates.append((symbol, prob, risk_adjusted_score))
        
        # 调试信息（仅在有买入候选时输出）
        if len(buy_candidates) > 0:
            print(f"  筛选统计: 总候选{total_candidates}, 概率过滤后{prob_filter_count}, 风险过滤后{risk_filter_count}, 最终候选{len(buy_candidates)}")
        
        # 按风险调整后的分数排序
        buy_candidates.sort(key=lambda x: x[2], reverse=True)
        
        cash_per_stock = (self.cash * 0.9) / self.top_k_stocks if self.top_k_stocks > 0 else 0
        
        for symbol, prob, risk_adjusted_score in buy_candidates[:self.top_k_stocks]:
            price = current_data.loc[symbol, 'open']
            shares = int(cash_per_stock / price)
            
            if shares > 0:
                # diff优化：更详细的买入成本计算
                gross_cost = shares * price
                commission = gross_cost * self.commission_rate
                slippage = gross_cost * self.slippage_rate
                total_cost = gross_cost + commission + slippage
                
                if total_cost <= self.cash:
                    self.cash -= total_cost
                    self.positions[symbol] = Position(symbol, shares, price, current_date, price)
                    
                    # diff优化：更详细的买入记录
                    self.trade_records.append({
                        'date': current_date, 
                        'symbol': symbol, 
                        'action': 'BUY', 
                        'shares': shares,
                        'price': price,
                        'value': total_cost,
                        'probability': prob,
                        'risk_score': risk_adjusted_score
                    })
                    
                    # diff优化：添加买入信息输出
                    print(f"买入 {symbol}: {shares}股 @ {price:.2f} (概率:{prob:.3f}, 分数:{risk_adjusted_score:.3f})")

    def _calculate_results(self) -> Dict:
        """计算回测结果"""
        if not self.daily_portfolio_value: return {}
        
        final_value = self.daily_portfolio_value[-1]
        total_return = (final_value / self.initial_capital) - 1
        
        daily_returns = pd.Series(self.daily_portfolio_value).pct_change().dropna()
        
        # 计算风险指标
        volatility = np.std(daily_returns) * np.sqrt(252)
        sharpe_ratio = (total_return * 252 / len(daily_returns)) / volatility if volatility > 0 else 0
        
        cum_returns = (1 + daily_returns).cumprod()
        peak = cum_returns.expanding(min_periods=1).max()
        drawdown = (cum_returns/peak - 1)
        max_drawdown = drawdown.min()
        
        # 保存详细交易记录
        results_dir = Path("logs/model_results")
        results_dir.mkdir(exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        trades_path = results_dir / f"backtest_trades_{timestamp}.json"
        pd.DataFrame(self.trade_records).to_json(trades_path, orient='records', indent=2)
        print(f"详细交易日志已保存至: {trades_path}")
        
        print(f"回测完成: 总收益率: {total_return:.2%}, 夏普比率: {sharpe_ratio:.2f}, 最大回撤: {max_drawdown:.2%}, 年化波动率: {volatility:.2%}")
        return {
            'total_return': total_return, 
            'max_drawdown': max_drawdown, 
            'sharpe_ratio': sharpe_ratio,
            'volatility': volatility
        }

class QuantSystem:
    """量化交易系统"""
    def __init__(self):
        self.trainer = ModelTrainer()
        self.backtest_engine = BacktestEngine()
    
    def calculate_target_variable(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算目标变量：未来10个交易日内是否上涨超过5%"""
        df = data.copy()
        
        # 技术报告要求
        target_window = 10  # 10个交易日
        target_threshold = 0.05  # 5%上涨阈值
        
        # 计算未来回报率
        df['future_return'] = df.groupby('symbol')['close'].transform(
            lambda x: x.pct_change(periods=target_window).shift(-target_window)
        )
        
        # 生成二分类目标标签
        df['target'] = (df['future_return'] > target_threshold).astype(int)
        
        return df
    
    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """加载数据"""
        train_file = Path("model/data/train_data.parquet")
        test_file = Path("model/data/test_data.parquet")
        if not train_file.exists() or not test_file.exists():
            print("未找到数据文件，请先运行 python -m feature.engineering")
            return pd.DataFrame(), pd.DataFrame()
        
        train_data, test_data = pd.read_parquet(train_file), pd.read_parquet(test_file)
        return train_data, test_data
    
    def prepare_features(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """准备特征数据"""
        optimal_features = self._load_optimal_features()
        available_features = [f for f in optimal_features if f in data.columns]
        
        clean_data = data.dropna(subset=available_features + ['target', 'future_return']).copy()
        X = clean_data[available_features].values
        y = clean_data['target'].values
        
        if np.any(np.isnan(X)) or np.any(np.isinf(X)):
            X = np.nan_to_num(X)
            
        print(f"使用 {len(available_features)}/{len(optimal_features)} 个最优特征, 准备了 {len(X)} 个样本。")
        return X, y, available_features

    def _load_optimal_features(self) -> List[str]:
        """加载最优特征配置"""
        optimal_features_file = Path("model/optimal_features.json")
        if optimal_features_file.exists():
            try:
                with open(optimal_features_file, 'r') as f:
                    return json.load(f)['selected_features']
            except Exception as e:
                print(f"加载最优特征配置失败: {e}")
        
        print("未找到配置文件，使用硬编码的32个最优特征")
        return [
            'ohlc_avg', 'hl_avg', 'price_change', 'volume_change', 'macd', 'macd_signal', 'macd_histogram', 'rsi',
            'sma_20', 'ema_12', 'ema_26', 'bb_upper', 'bb_middle', 'bb_lower', 'atr', 'volume_sma', 'price_momentum',
            'volatility', 'price_to_sma55_pct', 'price_to_sma233_pct', 'proximity_to_sma55', 'proximity_to_sma233',
            'sma55_position', 'sma233_position', 'sma55_support_strength', 'sma233_support_strength',
            'trading_intensity', 'volume_price_trend', 'bb_position', 'price_range', 'price_range_pct', 'price_position'
        ]

    def run_full_pipeline(self):
        """运行完整流程"""
        print("=== 运行完整的量化交易系统 ===\n")
        train_data, test_data = self.load_data()
        if train_data.empty or test_data.empty: return
        
        X_train, y_train, feature_columns = self.prepare_features(train_data)
        self.trainer.train_model(X_train, y_train)
        self.trainer.save_model()
        
        self.backtest_engine.run_backtest(self.trainer.model, test_data, feature_columns)
        print("\n量化交易系统运行完成！")

def main():
    """主函数"""
    import argparse
    parser = argparse.ArgumentParser(description='量化交易模型训练评估系统')
    parser.add_argument('--mode', choices=['full', 'train'], default='full',
                        help='运行模式: full=完整流程, train=仅训练模型')
    parser.add_argument('--max-iterations', type=int, default=10,
                        help='最大迭代次数')
    parser.add_argument('--auto', action='store_true',
                        help='自动化模式（兼容性参数）')
    
    args = parser.parse_args()
    
    system = QuantSystem()
    
    if args.auto or args.mode == "full":
        print("\n🚀 运行完整流程...")
        system.run_full_pipeline()
    elif args.mode == "train":
        print("\n🤖 仅训练模型...")
        train_data, _ = system.load_data()
        if not train_data.empty:
            X_train, y_train, _ = system.prepare_features(train_data)
            system.trainer.train_model(X_train, y_train)
            system.trainer.save_model()
    else:
        print("\n🚀 默认运行完整流程...")
        system.run_full_pipeline()

if __name__ == "__main__":
    main()