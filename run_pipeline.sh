#!/bin/bash
# ==============================================================================
# 专业量化交易完整流程控制脚本 (Professional Quant Workflow Pipeline)
# ==============================================================================
#
# 基于模块化架构的量化交易系统，支持两个核心工作流:
#
# 1. 研究流程 (research):
#    目标: 完整特征优化、模型验证、训练评估的研究级流程
#    步骤: data.data -> feature.engineering -> model.validation -> model.training -> strategy.signals -> strategy.portfolio
#    步骤: 01数据 -> 02特征优化 -> 03全面验证 -> 04自动训练评估 -> 05信号 -> 06组合
#    适用: 策略研发、模型优化、定期回测 (每周/月)
#
# 2. 生产流程 (live):
#    目标: 基于已训练模型的日常交易信号生成
#    步骤: data.data -> feature.engineering -> strategy.signals -> strategy.portfolio
#    步骤: 01数据 -> 02实时特征 -> 05信号生成 -> 06投资组合管理
#    适用: 每日交易信号生成
#
# 使用方法:
#   ./run_pipeline.sh research  - 运行完整研究流程 (约30-60分钟)
#   ./run_pipeline.sh live      - 运行生产流程 (约5-10分钟)
#   ./run_pipeline.sh           - 显示帮助信息
#
# 模块架构:
#   data/     - 数据采集和存储
#   feature/  - 特征工程和优化
#   model/    - 模型训练和验证
#   strategy/ - 信号生成和组合管理
#
# ==============================================================================

set -e # 任何命令失败则立即退出
set -o pipefail # 管道中的命令失败也视为失败

# --- 配置参数 ---
PYTHON_CMD="uv run"  # 使用uv run来执行Python脚本
LOG_DIR="logs/pipeline_runs"

# --- 辅助函数 ---
print_header() {
    echo ""
    echo "=============================================================================="
    echo "  $1"
    echo "=============================================================================="
}

print_step() {
    echo ""
    echo "▶ $1"
    echo "----------------------------------------"
}

run_step() {
    local step_name="$1"
    local command="$2"
    local start_time=$(date +%s)
    
    print_step "$step_name"
    echo "执行命令: $command"
    
    if eval "$command"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        echo "✅ [SUCCESS] $step_name 完成 (用时: ${duration}s)"
    else
        echo "❌ [FAILURE] $step_name 失败"
        exit 1
    fi
}

setup_environment() {
    print_header "环境准备"
    
    # 创建必要的目录
    mkdir -p logs model/data model/saved_models $LOG_DIR
    
    # 记录pipeline运行信息
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    echo "Pipeline运行时间: $(date)" > "$LOG_DIR/pipeline_$1_$timestamp.log"
    echo "模式: $1" >> "$LOG_DIR/pipeline_$1_$timestamp.log"
    echo "Python命令: $PYTHON_CMD" >> "$LOG_DIR/pipeline_$1_$timestamp.log"
}

# --- 核心工作流定义 ---

run_research_pipeline() {
    local start_time=$(date +%s)
    print_header "开始执行 [研究流程] - 完整特征优化与模型训练"
    
    setup_environment "research"
    
    print_step "清理研究环境"
    echo "清理旧的研究数据和模型..."
    rm -rf logs/feature_* logs/model_* logs/trading_* logs/portfolio_*
    rm -rf model/data/* model/saved_models/* model/optimal_*.json
    echo "研究环境已清理"

    # 01 数据获取与预处理
    run_step "01 数据获取与预处理" "$PYTHON_CMD -m data.data --market us --timeframe 1d"
    
    # 02 特征工程 - 研究模式（包含特征优化）
    run_step "02 特征工程 - 全股票特征优化" "$PYTHON_CMD -m feature.engineering research --market us --stock-mode all --optimize-features"
    
    # 03 模型验证 - Walk-Forward分析
    run_step "03 模型验证 - Walk-Forward全面分析" "$PYTHON_CMD -m model.validation --mode full"
    
    # 04 模型训练与评估 - 自动模式
    run_step "04 模型训练评估 - 自动优化训练" "$PYTHON_CMD -m model.training --mode train"
    
    # 05 信号生成 - 验证训练结果
    run_step "05 交易信号生成 - 验证模型输出" "$PYTHON_CMD -m strategy.signals"
    
    # 06 投资组合管理 - 完整测试
    run_step "06 投资组合管理 - 专业风险管理" "$PYTHON_CMD -m strategy.portfolio"
    
    local end_time=$(date +%s)
    local total_duration=$((end_time - start_time))
    
    print_header "[研究流程] 执行完毕"
    echo "总用时: $((total_duration / 60))分 $((total_duration % 60))秒"
    echo ""
    echo "📊 研究结果位置:"
    echo "   特征优化: model/optimal_features.json"
    echo "   模型文件: model/saved_models/"
    echo "   回测报告: logs/model_validation/"
    echo "   信号测试: logs/trading_signals/"
    echo "   组合测试: logs/portfolio_state/"
}

run_live_pipeline() {
    local start_time=$(date +%s)
    print_header "开始执行 [生产流程] - 实时交易信号生成"
    
    setup_environment "live"
    
    # 检查必要文件
    if [ ! -f "model/saved_models/xgboost_model.pkl" ]; then
        echo "❌ 错误: 未找到训练好的模型文件"
        echo "请先运行研究流程: ./run_pipeline.sh research"
        exit 1
    fi

    # 01 数据获取与预处理
    run_step "01 数据获取 - 最新市场数据" "$PYTHON_CMD -m data.data --market us --timeframe 1d"
    
    # 02 特征工程 - 实时模式
    run_step "02 特征工程 - 实时特征生成" "$PYTHON_CMD -m feature.engineering production --market us --stock-mode sample --count 10"
    
    # 05 信号生成 - 基于最新数据
    run_step "05 交易信号生成 - 实时信号输出" "$PYTHON_CMD -m strategy.signals"
    
    # 06 投资组合管理 - 交易决策
    run_step "06 投资组合管理 - 交易执行决策" "$PYTHON_CMD -m strategy.portfolio"
    
    local end_time=$(date +%s)
    local total_duration=$((end_time - start_time))
    
    print_header "[生产流程] 执行完毕"
    echo "总用时: $((total_duration / 60))分 $((total_duration % 60))秒"
    echo ""
    echo "📈 今日交易结果:"
    echo "   交易信号: logs/trading_signals/"
    echo "   投资组合: logs/portfolio_state/"
    echo "   交易订单: logs/portfolio_orders/"
}

# --- 主逻辑 ---
main() {
    local pipeline_start=$(date +%s)
    
    if [ "$1" == "research" ]; then
        run_research_pipeline
    elif [ "$1" == "live" ]; then
        run_live_pipeline
    else
        print_header "量化交易Pipeline使用说明"
        echo ""
        echo "使用方法:"
        echo "  $0 research  - 运行完整研究流程 (特征优化+模型训练+验证)"
        echo "  $0 live      - 运行生产流程 (实时信号生成+投资组合管理)"
        echo ""
        echo "流程说明:"
        echo "  research: data→feature→model.validation→model.training→strategy.signals→strategy.portfolio (~30-60分钟)"
        echo "  live:     data→feature→strategy.signals→strategy.portfolio (~5-10分钟)"
        echo ""
        echo "注意事项:"
        echo "  - 首次使用请先运行 research 流程"
        echo "  - 确保已安装依赖: uv, pandas, scikit-learn等"
        echo "  - 生产流程需要先完成研究流程的模型训练"
        exit 1
    fi
    
    local pipeline_end=$(date +%s)
    local pipeline_duration=$((pipeline_end - pipeline_start))
    echo ""
    echo "🎉 Pipeline执行完成! 总用时: $((pipeline_duration / 60))分 $((pipeline_duration % 60))秒"
}

main "$@"