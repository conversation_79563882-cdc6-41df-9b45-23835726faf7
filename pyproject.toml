[project]
name = "quant-lab"
version = "0.0.0"
description = "quant-lab research"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "akshare>=1.17.35",
    "baostock>=0.8.9",
    "dotenv>=0.9.9",
    "duckdb>=1.3.2",
    "lxml>=6.0.0",
    "matplotlib>=3.10.3",
    "networkx>=3.5",
    "numpy<2.0",
    "optuna>=4.4.0",
    "pandas>=2.3.1",
    "pandas-market-calendars>=5.1.1",
    "pandas-ta>=0.3.14b0",
    "pathlib>=1.0.1",
    "pyarrow>=17.0.0",
    "python-dotenv>=1.0.0",
    "pytz>=2024.1",
    "schedule>=1.2.2",
    "scikit-learn>=1.7.0",
    "seaborn>=0.13.2",
    "setuptools>=80.9.0",
    "tushare>=1.4.0",
    "xgboost>=3.0.2",
    "yfinance>=0.2.65",
    # Kronos项目依赖（深度学习股票预测）
    "torch>=1.9.0",
    "transformers>=4.20.0",
    "ccxt>=3.0.0",
    "binance>=0.3.0",
    "jupyter>=1.0.0",
    "ipython>=7.0.0",
    "vectorbt>=0.28.1",
    "quantstats>=0.0.76",
    "anywidget>=0.9.18",
    "ipywidgets>=8.1.7",
    "einops>=0.8.1",
    "huggingface-hub>=0.34.4",
    "safetensors>=0.6.2",
]
