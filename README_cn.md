# 专业级量化交易研究框架

本项目是一个基于现代机器学习技术构建的、端到端的专业级量化交易研究与实战模拟框架。它遵循金融行业的最佳实践，采用模块化设计，覆盖了从数据获取、特征工程、模型验证、策略回测到投资组合管理的全流程。

其核心设计哲学是：**科学、严谨、可复现**。

---

## 核心特性与设计哲学

- **严谨的防数据泄漏机制**: 将时间序列的“时间安全性”作为第一原则。所有特征计算、模型验证和回测流程都经过精心设计，以杜绝前视偏差（Look-ahead Bias），确保研究结果在模拟实战中的有效性。

- **科学的模型验证体系**: 摒弃了简单的训练/测试集划分，采用业界黄金标准的**滚动前向分析（Walk-Forward Analysis）**。在持续滚动的多个时间窗口上对模型进行训练和验证，并结合**窗口内超参数寻优**，以最接近实战的方式评估模型的稳健性与泛化能力。

- **专业的特征工程**:
  - **特征丰富性**: 不仅包含常规技术指标，更引入了**市场相对强度**、**市场环境**及**微观结构**等高级特征，从多维度捕捉市场信息。
  - **两阶段特征选择**: 采用“规则风险预审 + 时序模型优选”的先进流程，先剔除高风险特征，再通过机器学习模型在时序交叉验证中挑选出最有效的特征组合。

- **模块化的系统架构**: 系统被清晰地划分为多个低耦合的功能模块（数据、特征、模型、策略等），每个模块职责单一，易于独立测试、维护和扩展。

- **全面的投资组合管理框架**: 超越了简单的信号回测，引入了现代投资组合理论（MPT），包含了**仓位规模管理**、**风险指标计算（VaR/CVaR）** 以及 **组合优化（均值-方差、风险平价）** 的专业框架。

- **高可追溯性与可复现性**: 流水线中的每一步关键产出（如数据集、最优特征、最优参数、交易信号、持仓状态等）都会被持久化为结构化文件，使得整个研究和决策过程完全透明、可审计、可复现。

---

## 系统架构

项目采用分层流水线（Pipeline）架构，确保数据流和逻辑流的清晰、单向。

```mermaid
graph TD
    A[原始数据层 /data] --> B[特征工程层 /feature];
    B --> C[模型验证层 /model/validation.py];
    C --> D[模型训练层 /model/training.py];
    D --> E[信号生成层 /strategy/signals.py];
    E --> F[组合管理层 /strategy/portfolio.py];
    G[./run_pipeline.sh] --> A;
    G --> B;
    G --> C;
    G --> D;
    G --> E;
    G --> F;

    subgraph "数据准备"
        A
        B
    end

    subgraph "建模与回测"
        C
        D
    end

    subgraph "模拟交易"
        E
        F
    end
    
    subgraph "总调度"
        G
    end
```

- **/data**: 负责从多个数据源（如 YFinance, TuShare）获取原始行情数据，并进行清洗、标准化和增量更新。
- **/feature**: 系统的“智慧”核心。负责计算上百种技术指标和高级特征，并执行科学的特征选择，最终生成供模型使用的、干净的训练/测试数据集。
  - **特征体系概览**:
    - **价格与价值类**: `ohlc_avg` (综合价格), `hl_avg` (高低均价), `price_range` (价格范围), `price_position` (价格位置) 等。
    - **动量与变化类**: `price_change` (价格变化率), `volume_change` (成交量变化率), `price_momentum` (价格动量) 等。
    - **趋势与均线类**: `sma_20` (20日均线), `ema_12` (12日指数均线), `macd` (MACD指标), `price_to_sma55_pct` (价格与均线偏离度) 等。
    - **波动率类**: `atr` (平均真实波幅), `volatility` (历史波动率), `bb_position` (布林带位置) 等。
    - **成交量类**: `volume_sma` (成交量均线), `trading_intensity` (交易强度), `volume_price_trend` (量价趋势) 等。
    - **震荡与超买超卖类**: `rsi` (相对强弱指数)。
    - **支撑与压力类**: `sma55_support_strength` (关键均线支撑强度) 等。
    - **市场相对强度类**: `relative_return` (相对市场收益), `relative_volatility` (相对市场波动) 等。
- **/model**: 包含模型验证、训练和评估的全部逻辑。
- **/strategy**: 将训练好的模型转化为具体的交易决策，包含信号生成和投资组合管理两大模块。
- **/stockrl**: 一个独立的、探索前沿的强化学习交易模型子项目。
- **/backtest**: 存放基于 `vectorbt` 和 `quantstats` 生成的策略回测报告。
- **/tools**: 包含各类辅助分析和自动化脚本。

---

## 环境配置

1.  **Python 版本**: 项目要求 Python `3.11` 或更高版本。
2.  **依赖管理**: 项目使用 `uv` 作为包管理工具。
3.  **安装依赖**:
    ```bash
    # 推荐在虚拟环境中执行
    uv pip install -r requirements.txt
    ```

---

## 使用流程

项目通过根目录下的 `run_pipeline.sh` 脚本进行总调度，该脚本定义了两种核心工作流。

### 1. 研究流程 (`research`)

此流程用于模型和策略的深度研发，执行从数据到回测的全套步骤。适用于策略开发、特征优化或周期性（如每周/每月）的策略效果验证。

**执行命令:**
```bash
bash ./run_pipeline.sh research
```

**执行步骤:**
1.  **数据更新**: `data.data` - 获取并更新本地股票数据。
2.  **特征工程**: `feature.engineering` - 对全量数据计算特征，并运行特征优化算法，生成 `optimal_features.json`。
3.  **模型验证**: `model.validation` - 执行完整的滚动前向分析，评估模型稳健性，并生成 `optimal_params.json`。
4.  **模型训练**: `model.training` - 使用最优参数和特征，在训练集上训练最终模型，并在测试集上进行回测。
5.  **信号生成**: `strategy.signals` - 基于训练好的模型，在测试集上生成模拟交易信号。
6.  **组合管理**: `strategy.portfolio` - 基于生成的信号，模拟投资组合的构建与再平衡。

### 2. 生产流程 (`live`)

此流程用于模拟每日的实盘交易，它依赖于“研究流程”已产出的模型和配置，高效地生成当日交易信号。

**执行命令:**
```bash
bash ./run_pipeline.sh live
```

**执行步骤:**
1.  **数据更新**: `data.data` - 获取最新的市场数据。
2.  **特征工程**: `feature.engineering` - 使用已有的特征配置，仅对最新数据计算特征。
3.  **信号生成**: `strategy.signals` - 加载已训练好的模型，对最新特征数据进行预测，生成当日交易信号。
4.  **组合管理**: `strategy.portfolio` - 加载当前持仓状态和当日信号，生成最终的交易订单（Orders）。

---

## 未来展望

本项目已经构建了一个坚实且专业的框架，但仍有广阔的扩展空间：

- **全面整合投资组合优化器**: 将 `strategy/portfolio.py` 中已实现的均值-方差或风险平价优化器，完全整合到交易订单生成逻辑中，实现从“信号选择”到“组合优化”的跨越。
- **参数与配置的外部化**: 将代码中硬编码的策略参数（如交易费率、止盈止损点、模型参数等）全部移至独立的配置文件（如 `config.yml`），方便快速实验和迭代。
- **完善强化学习（RL）流程**: 将 `/stockrl` 子项目完全整合到主流水线中，与监督学习模型形成对比或互补。
- **更丰富的可视化分析**: 开发一个基于 Streamlit 或 Dash 的可视化仪表盘，用于展示回测结果、模型性能和投资组合状态。
