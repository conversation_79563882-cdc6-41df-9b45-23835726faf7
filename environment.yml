name: quant
channels:
  - conda-forge
dependencies:
  - duckdb
  - ipython
  - ipywidgets
  - jupyter
  - jupyterlab
  - lxml
  - matplotlib
  - networkx
  - notebook
  - numpy
  - optuna
  - pandas
  - pathlib
  - plotly
  - pyarrow
  - python=3.11
  - pytorch
  - schedule
  - scikit-learn
  - scipy
  - seaborn
  - setuptools
  - xgboost
  - yfinance
  - pip:
    - QuantStats==0.0.76
    - akshare==1.17.39
    - anywidget==0.9.18
    - baostock==0.8.9
    - binance==0.3.64
    - ccxt==4.5.1
    - einops==0.8.1
    - huggingface-hub==0.34.4
    - pandas_market_calendars==5.1.1
    - pandas_ta==0.3.14b0
    - python-dotenv==1.1.1
    - safetensors==0.6.2
    - transformers==4.55.4
    - tushare==1.4.23
    - vectorbt==0.28.1
    - yfinance==0.2.65

prefix: "/Users/<USER>/miniforge3/envs/quant"
